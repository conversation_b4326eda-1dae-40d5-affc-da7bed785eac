package com.cloudpense.standard.vo.claim;

import com.cloudpense.standard.dao.entity.ExpClaimHeader;
import com.cloudpense.standard.dao.entity.ExpClaimLine;
import com.cloudpense.standard.dao.entity.FndWorkflowPath;
import com.cloudpense.standard.vo.masterdata.CityVo;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class ExpClaimHeaderVo extends ExpClaimHeader {

    @JsonProperty("header_type_code")
    private String headerTypeCode;
    @JsonProperty("link_header_column")
    @Deprecated
    @ApiModelProperty(value = "旧版前序单据编码, 使用最新external_id + headerTypeCode确定唯一单据")
    private String linkHeaderColumn;
    @JsonProperty("link_header_vos")
    private List<LinkHeaderVo> linkHeaderVos;
    @JsonProperty("link_header_vo")
    private LinkHeaderVo linkHeaderVo;
    @JsonProperty("submit_user_code")
    private String submitUserCode;
    @JsonProperty("charge_user_code")
    private String chargeUserCode;
    @JsonProperty("created_by_code")
    private String createdByCode;
    @JsonProperty("pay_user_code")
    private String payUserCode;
    @JsonProperty("submit_user_name")
    private String submitUserName;
    @JsonProperty("charge_user_name")
    private String chargeUserName;
    @JsonProperty("created_by_user_name")
    private String createdByUserName;
    @JsonProperty("pay_user_name")
    private String payUserName;
    @JsonProperty("branch_code")
    private String branchCode;
    @JsonProperty("submit_department_code")
    private String submitDepartmentCode;
    @JsonProperty("charge_department_code")
    private String chargeDepartmentCode;
    @JsonProperty("destination_cities")
    private String destinationCities;
    @JsonProperty("destination_city_name")
    private String destinationCityName;
    @JsonProperty("destination_city_to_name")
    private String destinationCityToName;
    @JsonProperty("user_account_code")
    private String userAccountCode;
    @JsonProperty("supplier_code")
    private String supplierCode;
    @JsonProperty("supplier_account_code")
    private String supplierAccountCode;
    @JsonProperty("branch_account_code")
    private String branchAccountCode;
    @JsonProperty("customer_code")
    private String customerCode;
    @JsonProperty("project_code")
    private String projectCode;
    @JsonProperty("ignore_warning")
    private String ignoreWarning;
    @JsonProperty("approver_list")
    private List<Integer> approverList;

    @JsonProperty("column130")
    private String column130;

    //todo 请假类型
    @JsonProperty("absence_code")
    private String absenceCode;

    //单据行
    @JsonProperty("claim_lines")
    @JsonAlias("claim_line")
    private List<ExpClaimLineVo> expClaimLineVoList = new ArrayList<ExpClaimLineVo>();
    //时间行
    @JsonProperty("time_line")
    private List<ExpClaimTimeLineVo> expClaimTimeLineVoList = new ArrayList<ExpClaimTimeLineVo>();

    //单据头附件
    @JsonProperty("claim_attachments")
    @JsonAlias("attachments")
    private List<ClaimAttachmentVo> claimAttachmentVos = new ArrayList<>();
    private List<FndWorkflowPath> workflowPathList = new ArrayList<FndWorkflowPath>();
    private List<ExpClaimLine> expClaimLineList = new ArrayList<ExpClaimLine>();
    private List<CityVo> headerCitys = new ArrayList<CityVo>();

    private List<ExpTypeColumnBaseVo> expTypeColumnBaseVoList = new ArrayList<>();

    public void setExpTypeColumnBaseVoList(List<ExpTypeColumnBaseVo> expTypeColumnBaseVoList) {
        this.expTypeColumnBaseVoList = new ArrayList<>(expTypeColumnBaseVoList);
    }

    private ExpClaimHeader  linkHeader;

    private String positionCode;

    @JsonProperty("customer_account_code")
    private String customerAccountCode;

    @JsonProperty("period_name")
    private String periodName;

//    @JsonProperty("column130")
//    private String column130;

    //预算分摊行
    @JsonProperty("budget_line")
    private List<BudgetLineVo> budgetLineVoList = new ArrayList<>();
}