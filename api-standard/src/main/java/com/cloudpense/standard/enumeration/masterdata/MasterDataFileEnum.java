package com.cloudpense.standard.enumeration.masterdata;

/**
 * 主数据批量上传状态枚举类
 */
public enum MasterDataFileEnum {

    // 文件备份成功
    UPLOAD("upload"),
    // 处理未完成
    INCOMPLETE("incomplete"),
    // 已完成
    COMPLETED("completed"),
    // 处理异常
    ERROR("error");

    private  String code;

    MasterDataFileEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

}
