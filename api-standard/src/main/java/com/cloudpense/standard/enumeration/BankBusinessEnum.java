package com.cloudpense.standard.enumeration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum BankBusinessEnum {

    ORGANIZE("organize", "对公业务"),
    PERSON("person", "对私业务"),
    CURRENCY("currency", "本币业务"),
    FOREIGN("foreign", "外币业务"),
    OUR("our", "本行业务"),
    INTER("inter", "跨行业务"),
    ORGANIZE_OUR("organize_our", "对公本行业务"),
    ORGANIZE_INTER("organize_inter", "对公跨行业务"),
    PERSON_OUR("person_our", "对私本行业务"),
    PERSON_INTER("person_inter", "对私跨行业务");

    private String code;

    private String description;

    public static final Map<String, String> codeToDes = new HashMap<>();

    public static final List<String> codes = new ArrayList<>();

    BankBusinessEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    static {
        for (BankBusinessEnum bank : BankBusinessEnum.values()) {
            codeToDes.put(bank.getCode(), bank.getDescription());
            codes.add(bank.getCode());
        }
    }

    public String getCode() {

        return this.code;
    }

    public String getDescription() {

        return this.description;
    }

}
