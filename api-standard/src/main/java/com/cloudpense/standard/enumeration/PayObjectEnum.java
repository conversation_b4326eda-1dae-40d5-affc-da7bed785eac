package com.cloudpense.standard.enumeration;

import java.util.HashMap;
import java.util.Map;

public enum PayObjectEnum {

    SUPPLIER("s", "支付给供应商"),
    OWEN("U", "支付给个人");

    public static final Map<String, String> eventToName = new HashMap<>();

    static {
        for (PayObjectEnum accountEnum : PayObjectEnum.values()) {
            eventToName.put(accountEnum.getCode(), accountEnum.getContent());
        }
    }

    private String code;

    private String content;

    PayObjectEnum(String code, String content) {
        this.code = code;
        this.content = content;
    }

    public String getCode() {

        return this.code;
    }

    public String getContent() {

        return this.content;
    }

}
