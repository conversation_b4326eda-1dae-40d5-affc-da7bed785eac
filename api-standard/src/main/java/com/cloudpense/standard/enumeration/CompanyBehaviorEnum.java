package com.cloudpense.standard.enumeration;

import java.util.HashMap;
import java.util.Map;

/**
 * 公司行为Enum
 * Date: 2020-05-20
 *
 * <AUTHOR>
 */
public enum CompanyBehaviorEnum {

    START_BY_TENANT("start_by_tenant", "企业启用", "飞书企业启用通知"),
    STOP_BY_TENANT("stop_by_tenant", "企业停用", "飞书企业停用通知"),
    PAY_BY_TENANT("pay_by_tenant", "企业购买", "飞书企业购买通知");

    /**
     * 英文名
     */
    private String enName;
    /**
     * 中文名
     */
    private String zhName;
    /**
     * 邮件主题
     */
    private String emailSubject;

    public static final Map<String, CompanyBehavior> enName2CompanyBehaviorMap = new HashMap();

    static {
        for (CompanyBehaviorEnum companyBehaviorEnum : CompanyBehaviorEnum.values()) {
            CompanyBehavior companyBehavior = new CompanyBehavior(companyBehaviorEnum.getZhName(), companyBehaviorEnum.getEmailSubject());
            enName2CompanyBehaviorMap.put(companyBehaviorEnum.getEnName(), companyBehavior);

        }
    }

    CompanyBehaviorEnum(String enName, String zhName, String emailSubject) {
        this.enName = enName;
        this.zhName = zhName;
        this.emailSubject = emailSubject;
    }

    public String getEnName() {
        return this.enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public String getZhName() {
        return this.zhName;
    }

    public void setZhName(String zhName) {
        this.zhName = zhName;
    }

    public String getEmailSubject() {
        return this.emailSubject;
    }

    public void setEmailSubject(String emailSubject) {
        this.emailSubject = emailSubject;
    }

    public static class CompanyBehavior {
        /**
         * 中文名
         */
        private String zhName;
        /**
         * 邮箱主题
         */
        private String emailSubject;

        public CompanyBehavior(String zhName, String emailSubject) {
            this.zhName = zhName;
            this.emailSubject = emailSubject;
        }

        public String getZhName() {
            return this.zhName;
        }

        public void setZhName(String zhName) {
            this.zhName = zhName;
        }

        public String getEmailSubject() {
            return this.emailSubject;
        }

        public void setEmailSubject(String emailSubject) {
            this.emailSubject = emailSubject;
        }
    }
}
