package com.cloudpense.standard.enumeration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum TypeToClassEnum {


    BRANCH("branch", "department"),
    COSTCENTER("cost_center", "department"),
    CUSTOMER("customer", "customer"),
    DEPARTMENT("department", "department"),
    GLPERIOND("gl_period", "glperiod"),
    POSITION("position", "position"),
    PROJECT("project", "project"),
    SUPPLIER("supplier", "supplier"),
    TAXCODE("tax_code", "exptaxcode"),
    CITY("city", "city"),
    USER("user", "user");

    private String type;

    private String className;

    public static final Map<String, String> typeToClassMap = new HashMap<>();

    public static final List<String> typeList = new ArrayList<>();

    static {
        for (TypeToClassEnum e : TypeToClassEnum.values()) {
            typeToClassMap.put(e.getType(), e.getClassName());
            typeList.add(e.getType());
        }
    }

    TypeToClassEnum(String type, String className) {
        this.type = type;
        this.className = className;
    }

    public String getType() {

        return this.type;
    }

    public String getClassName() {

        return this.className;
    }
}