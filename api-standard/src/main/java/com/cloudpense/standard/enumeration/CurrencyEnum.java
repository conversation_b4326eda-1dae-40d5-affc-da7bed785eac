package com.cloudpense.standard.enumeration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 币种枚举
 * Date: 2020-07-06
 *
 * <AUTHOR>
 */
public enum CurrencyEnum {

    CNY("CNY", "人民币"),
    USD("USD", "美元"),
    AED("AED", "阿联酋迪拉姆"),
    AUD("AUD", "澳大利亚元"),
    BRL("BRL", "巴西里亚尔"),
    CAD("CAD", "加拿大元"),
    CHF("CHF", "瑞士法郎"),
    DKK("DKK", "丹麦克朗"),
    EUR("EUR", "欧元"),
    GBP("GBP", "英镑"),
    HKD("HKD", "港币"),
    IDR("IDR", "印尼卢比"),
    INR("INR", "印度卢比"),
    JPY("JPY", "日元"),
    KRW("KRW", "韩国元"),
    MOP("MOP", "澳门元"),
    MYR("MYR", "林吉特"),
    NOK("NOK", "挪威克朗"),
    NZD("NZD", "新西兰元"),
    PHP("PHP", "菲律宾比索"),
    RUB("RUB", "卢布"),
    SAR("SAR", "沙特里亚尔"),
    SEK("SEK", "瑞典克朗"),
    SGD("SGD", "新加坡元"),
    THB("THB", "泰国铢"),
    TRY("TRY", "土耳其里拉"),
    TWD("TWD", "新台币"),
    ZAR("ZAR", "南非兰特");

    /**
     * 英文名
     */
    private String enName;
    /**
     * 中文名
     */
    private String zhName;

    /**
     * 英文名-中文名Map
     */
    public static final Map<String, String> en2ZhMap = new HashMap<>();

    /**
     * 币种列表
     */
    public static final List<String> currencyList = new ArrayList<>();

    static {
        for (CurrencyEnum currencyEnum: CurrencyEnum.values()) {
            en2ZhMap.put(currencyEnum.getEnName(),currencyEnum.getZhName());
            currencyList.add(currencyEnum.getEnName());
        }
    }

    CurrencyEnum(String enName, String zhName) {
        this.enName = enName;
        this.zhName = zhName;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public String getZhName() {
        return zhName;
    }

    public void setZhName(String zhName) {
        this.zhName = zhName;
    }
}
