package com.cloudpense.standard.enumeration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 银行编码枚举
 * Date: 2020-07-06
 *
 * <AUTHOR>
 */
public enum BankCodeEnum {

    ICBC("ICBC", "工商银行", "ICBC"),
    BOC("BOC", "中国银行", "BOC"),
    ABC("ABC", "农业银行", "ABCHINA"),
    CCB("CCB", "建设银行", "CCB"),
    CMB("CMB", "招商银行", "CMBCHINA"),
    COMM("COMM", "交通银行", "BANKCOMM");
    /**
     * 英文名
     */
    private String enName;
    /**
     * 中文名
     */
    private String zhName;
    /**
     * 易源数据名字
     */
    private String showapiName;

    public static final Map<String, String> en2ZhMap = new HashMap();

    public static final List<String> bankCodeList = new ArrayList<>();

    public static final List<String> showapiCodeList = new ArrayList<>();

    public static final Map<String, String> showapi2EnMap = new HashMap<>();

    static {
        for (BankCodeEnum bankCodeEnum: BankCodeEnum.values()) {
            en2ZhMap.put(bankCodeEnum.getEnName(),bankCodeEnum.getZhName());
            bankCodeList.add(bankCodeEnum.getEnName());
            showapiCodeList.add(bankCodeEnum.getShowapiName());
            showapi2EnMap.put(bankCodeEnum.getShowapiName(), bankCodeEnum.getEnName());
        }
    }

    BankCodeEnum(String enName, String zhName, String showapiName) {
        this.enName = enName;
        this.zhName = zhName;
        this.showapiName = showapiName;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public String getZhName() {
        return zhName;
    }

    public void setZhName(String zhName) {
        this.zhName = zhName;
    }

    public String getShowapiName() {
        return showapiName;
    }

    public void setShowapiName(String showapiName) {
        this.showapiName = showapiName;
    }
}
