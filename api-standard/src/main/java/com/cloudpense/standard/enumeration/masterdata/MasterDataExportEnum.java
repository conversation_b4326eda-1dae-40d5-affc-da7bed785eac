package com.cloudpense.standard.enumeration.masterdata;

public enum MasterDataExportEnum {

    PROCESSING("processing", "数据处理中"),
    COMPLETE("complete", "已完成"),
    ERROR("error", "处理异常");

    private String code;

    private String message;

    MasterDataExportEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}
