package com.cloudpense.standard.enumeration;

import java.util.HashMap;
import java.util.Map;

public enum AccountRuleEnum {

    EXP_DR("EXP_DR", "费用借方"),
    EXP_CR("EXP_CR", "费用贷方"),
    EXP_TAX("EXP_TAX", "税金借方"),
    PAY_CR("PAY_CR", "付款贷方"),
    EXP_TAX_CR("EXP_TAX_CR", "税金贷方"),
    PAY_DR("PAY_DR", "付款借方");


    public static final Map<String, String> eventToName = new HashMap<>();

    static {
        for (AccountRuleEnum accountEnum : AccountRuleEnum.values()) {
            eventToName.put(accountEnum.getAccountEvent(), accountEnum.getAccountEventName());
        }
    }

    private String accountEvent;

    private String accountEventName;

    AccountRuleEnum(String type, String name) {
        this.accountEvent = type;
        this.accountEventName = name;
    }

    public String getAccountEvent(){

        return accountEvent;
    }

    public String getAccountEventName(){

        return accountEventName;
    }


}
