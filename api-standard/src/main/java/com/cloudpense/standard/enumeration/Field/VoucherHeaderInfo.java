package com.cloudpense.standard.enumeration.Field;

import com.cloudpense.standard.vo.Column;

/**
 * <AUTHOR>
 * @date 2019/9/16 15:09
 */
@Deprecated
public enum VoucherHeaderInfo {


    /**
     *单据号
     */
    documentNum {
        @Override
        public String getName() {
            return "documentNum";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.document_num");
            column.setAlias("documentNum");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     *单据id
     */
    documentId {
        @Override
        public String getName() {
            return "documentId";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.document_id");
            column.setAlias("documentId");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     * 头id
     */
    headerId {
        @Override
        public String getName() {
            return "headerId";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.header_id");
            column.setAlias("headerId");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     *单据状态
     */
    headerStatus {
        @Override
        public String getName() {
            return "headerStatus";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.status");
            column.setAlias("headerStatus");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     *单据头字段colunmn10
     */
    headerColumn10{
        @Override
        public String getName() {
            return "headerColumn10";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.column10");
            column.setAlias("headerColumn10");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     *单据提交人的员工号
     */
    submitEmployeeNumber {
        @Override
        public String getName() {
            return "submitEmployeeNumber";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("submitUser.employee_number");
            column.setAlias("submitEmployeeNumber");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     * 单据提交人
     */
    submitUserName {
        @Override
        public String getName() {
            return "submitUserName";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("submitUser.full_name");
            column.setAlias("submitUserName");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     * 单据报销费用人
     */
    chargeUserName {
        @Override
        public String getName() {
            return "chargeUserName";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("chargeUser.full_name");
            column.setAlias("chargeUserName");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     *供应商名称
     */
    supplierName {
        @Override
        public String getName() {
            return "supplierName";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("supplierTl.supplier_name");
            column.setAlias("supplierName");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     *供应商Code
     */
    supplierCode {
        @Override
        public String getName() {
            return "supplierCode";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("supplier.supplier_code");
            column.setAlias("supplierCode");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     *供应商语种选择
     */
    supplierLanguage {
        @Override
        public String getName() {
            return "suppLanguage";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("supplierTl.language");
            column.setAlias("suppLanguage");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     *单据代码
     */
    headerTypeCode {
        @Override
        public String getName() {
            return "headerTypeCode";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("headerType.type_Code");
            column.setAlias("headerTypeCode");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     *凭证日期取付款日期
     */
    payDate {
        @Override
        public String getName() {
            return "payDate";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("paymentHeader.payment_date");
            column.setAlias("payDate");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     *公司代码
     */
    departmentCode {
        @Override
        public String getName() {
            return "departmentCode";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("departmentBranch.department_code");
            column.setAlias("departmentCode");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     *charge_department_column1字段
     */
    chargeDepartmentColumn1 {
        @Override
        public String getName() {
            return "chargeDepartmentColumn1";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("chargeDepartment.column1");
            column.setAlias("chargeDepartmentColumn1");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     *header_colunm_32字段
     */
    headerColunm32 {
        @Override
        public String getName() {
            return "headerColunm32";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.column32");
            column.setAlias("headerColunm32");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     *header_colunm_33字段
     */
    headerColunm33 {
        @Override
        public String getName() {
            return "headerColunm33";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.column33");
            column.setAlias("headerColunm33");
            column.setIsSpecial(false);
            return column;
        }
    },


    /**
     *header_colunm_34字段
     */
    headerColunm34 {
        @Override
        public String getName() {
            return "headerColunm34";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.column34");
            column.setAlias("headerColunm34");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     *header_colunm_35字段
     */
    headerColunm35 {
        @Override
        public String getName() {
            return "headerColunm35";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.column35");
            column.setAlias("headerColunm35");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     *header_colunm_36字段
     */
    headerColunm36 {
        @Override
        public String getName() {
            return "headerColunm36";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.column36");
            column.setAlias("headerColunm36");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     *header_colunm_37字段
     */
    headerColunm37 {
        @Override
        public String getName() {
            return "headerColunm37";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.column37");
            column.setAlias("headerColunm37");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     *header_colunm_38字段
     */
    headerColunm38 {
        @Override
        public String getName() {
            return "headerColunm38";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.column38");
            column.setAlias("headerColunm38");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     *header_colunm_39字段
     */
    headerColunm39 {
        @Override
        public String getName() {
            return "headerColunm39";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.column39");
            column.setAlias("headerColunm39");
            column.setIsSpecial(false);
            return column;
        }
    },


    /**
     *货币码
     */
    currencyCode {
        @Override
        public String getName() {
            return "currencyCode";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.currency_code");
            column.setAlias("currencyCode");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     *收款对象
     */
    payObject {
        @Override
        public String getName() {
            return "payObject";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.pay_object");
            column.setAlias("payObject");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     *员工账号数字
     */
    userAccountNumber {
        @Override
        public String getName() {
            return "userAccountNumber";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("fndUserAccount.account_number");
            column.setAlias("userAccountNumber");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     *员工账号名称
     */
    userAccountName {
        @Override
        public String getName() {
            return "userAccountName";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("fndUserAccount.account_Name");
            column.setAlias("userAccountName");
            column.setIsSpecial(false);
            return column;
        }
    },


    /**
     *员工账号对应的银行代码
     */
    userBankCode {
        @Override
        public String getName() {
            return "userBankCode";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("fndUserAccount.bank_code");
            column.setAlias("userBankCode");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     *供应商账号数字
     */
    supplierAccountNumber {
        @Override
        public String getName() {
            return "supplierAccountNumber";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("expSupplierAccount.account_number");
            column.setAlias("supplierAccountNumber");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     *供应商账号名称
     */
    supplierAccountName {
        @Override
        public String getName() {
            return "supplierAccountName";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("expSupplierAccount.account_Name");
            column.setAlias("supplierAccountName");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     *供应商账号对应的银行代码
     */
    supplierBankCode {
        @Override
        public String getName() {
            return "supplierBankCode";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("expSupplierAccount.bank_code");
            column.setAlias("supplierBankCode");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     *供应商账号对应的银行代码
     */
    advanceAmount {
        @Override
        public String getName() {
            return "advanceAmount";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.advance_amount");
            column.setAlias("advanceAmount");
            column.setIsSpecial(false);
            return column;
        }
    },
    /**
     *附件数量
     */
    attachments {
        @Override
        public String getName() {
            return "attachments";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("attachments");
            column.setAlias("attachments");
            column.setIsSpecial(true);
            return column;
        }
    },

    /**
     *描述
     */
     headerDescription{
        @Override
        public String getName() {
            return "headerDescription";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.description");
            column.setAlias("headerDescription");
            column.setIsSpecial(false);
            return column;
        }
    },

    /**
     *总支付金额
     */
    totalPayAmount {
        @Override
        public String getName() {
            return "totalPayAmount";
        }

        @Override
        public Column getField() {
            Column column = new Column();
            column.setName("claimHeader.total_pay_amount");
            column.setAlias("totalPayAmount");
            column.setIsSpecial(false);
            return column;
        }
    };

    public abstract String getName();
    public abstract Column getField();


}
