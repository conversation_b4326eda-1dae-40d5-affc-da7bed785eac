package com.cloudpense.standard.enumeration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 校验规则触发条件
 * operator: 操作符
 * Date: 2021-02-19
 *
 * <AUTHOR>
 */
public enum ValRuleEventsEnum {

    EVENT_EXP_SUBMITTED("submitted", "expsubmitted"),
    EVENT_EXP_INCOMPLETED("incomplete", "expsubmitted"),
    EVENT_EXP_CHECKED("approved", "expchecked");


    /**
     * 操作符
     */
    private String status;
    /**
     * 描述
     */
    private String code;

    public static final Map<String, String> statusMap = new HashMap();

    public static final List<String> statusList = new ArrayList<>();


    static {
        for (ValRuleEventsEnum valRuleEventsEnum: ValRuleEventsEnum.values()) {
            statusMap.put(valRuleEventsEnum.getStatus(),valRuleEventsEnum.getCode());
            statusList.add(valRuleEventsEnum.getStatus());
        }
    }

    ValRuleEventsEnum(String status, String code) {
        this.status = status;
        this.code = code;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
