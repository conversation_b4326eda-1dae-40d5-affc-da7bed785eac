package com.cloudpense.standard.enumeration.company;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2019/11/21 17:59
 */
public class Company {

    @ApiModelProperty("长飞光纤光缆股份有限公司")
    public final static Integer CHANGFEI = 16895;

    @ApiModelProperty("东风商用车")
    public final static Integer DONGFENG = 17812;

    @ApiModelProperty("大长江")
    public final static Integer DACHANGJIANG = 16895;

    @ApiModelProperty("EVCARD")
    public final static Integer EVCARD = 16904;

    @ApiModelProperty("海南省农垦投资控股集团有限公司")
    public final static Integer NONGKENG = 15587;

    @ApiModelProperty("捷豹路虎")
    public final static Integer JIEBAO = 14968;

    @ApiModelProperty("jissbon")
    public final static Integer JISSBON = 17868;

    @ApiModelProperty("翠丰集团")
    public final static Integer CUIFENG = 11865;

    @ApiModelProperty("柳工集团")
    public final static Integer LIUGONG = 16877;

    @ApiModelProperty("立邦投资有限公司")
    public final static Integer LIBANG = 15220;

    @ApiModelProperty("菲尼克斯电气集团")
    public final static Integer FEINIKESI = 11052;

    @ApiModelProperty("Remy China VIP")
    public final static Integer REMYCHINA = 2722;

    @ApiModelProperty("神威药业集团")
    public final static Integer SHENWEI = 17717;

    @ApiModelProperty("商发")
    public final static Integer SHANGFA = 16894;

    @ApiModelProperty("uae")
    public final static Integer UAE = 2594;

    @ApiModelProperty("移动报销平台")
    public final static Integer YIDONG = 15825;

    @ApiModelProperty("复星")
    public final static Integer FUXING = 17815;

    @ApiModelProperty("中通")
    public final static Integer ZHONGTONG = 17880;

    @ApiModelProperty("移动报销平台")
    public final static Integer OTHER = 0;

    @ApiModelProperty("振德")
    public final static Integer ZDM = 20801;

}
