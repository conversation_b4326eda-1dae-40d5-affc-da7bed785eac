package com.cloudpense.standard.enumeration.voucher;

/**
 * Created by wa<PERSON><PERSON><PERSON><PERSON> on 2019/11/12.
 * 凭证状态枚举
 */
public enum GlStatusEnum {
    //未生成
    PENDING("pending"),
    //生成中
    GENERATING("generating"),
    //已生成
    GENERATED("generated"),
    //生成失败
    ERROR("error");

    private String code;

    private GlStatusEnum(String code){
        this.code = code;
    }

    public String getCode(){
        return code;
    }
}
