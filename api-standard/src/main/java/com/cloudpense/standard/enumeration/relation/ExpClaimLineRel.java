package com.cloudpense.standard.enumeration.relation;

import com.cloudpense.standard.vo.Relation;

/**
 * <AUTHOR>
 * @date 2019/10/11 9:22
 */
@Deprecated
public enum  ExpClaimLineRel {

    glAccountDr{
        @Override
        public Relation getRelation() {

            return getRelationObjct("glAccountDr");
        }
    },
    glAccountCr{
        @Override
        public Relation getRelation() {

            return getRelationObjct("glAccountCr");
        }
    },
    glAccountTax{
        @Override
        public Relation getRelation() {

            return getRelationObjct("glAccountTax");
        }
    },
    glAccountTaxCr{
        @Override
        public Relation getRelation() {

            return getRelationObjct("glAccountTaxCr");
        }
    },
    expSupplierTl{
        @Override
        public Relation getRelation() {

            return getRelationObjct("expSupplierTl");
        }
    },
    expSupplier{
        @Override
        public Relation getRelation() {

            return getRelationObjct("expSupplier");
        }
    },
    expTaxCode{
        @Override
        public Relation getRelation() {

            return getRelationObjct("expTaxCode");
        }
    },
    expType{
        @Override
        public Relation getRelation() {

            return getRelationObjct("expType");
        }
    },
    expTypeTl{
        @Override
        public Relation getRelation() {

            return getRelationObjct("expTypeTl");
        }
    },
    fndUserAccount{
        @Override
        public Relation getRelation() {

            return getRelationObjct("fndUserAccount");
        }
    },
    expSupplierAccount{
        @Override
        public Relation getRelation() {

            return getRelationObjct("expSupplierAccount");
        }
    },
    glAccountTlDr{
        @Override
        public Relation getRelation() {

            return getRelationObjct("glAccountTlDr");
        }
    },
    fndUserPay{
        @Override
        public Relation getRelation() {

            return getRelationObjct("fndUserPay");
        }
    },

    mainTable {
        @Override
        public Relation getRelation() {
            Relation relation = new Relation();
            relation.setTableName("exp_claim_line");
            return relation;
        }
    };

    public abstract Relation getRelation();

    private static Relation getRelationObjct(String relationShip) {
        Relation relation = new Relation();
        relation.setRelation(relationShip);
        relation.setTableName("exp_claim_line");
        return relation;
    }
}
