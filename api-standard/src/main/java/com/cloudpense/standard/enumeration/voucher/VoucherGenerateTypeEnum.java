package com.cloudpense.standard.enumeration.voucher;

/**
 * <AUTHOR> wujie
 * @date : 2020/5/26 11:11
 * @Description : 凭证生成类型枚举类
 * gl_je_batch表的type字段，用于区分凭证生成类型，目前凭证有两种，一种是手工的，另一种是跟外部SAP等系统交互
 */
public enum VoucherGenerateTypeEnum {
    // 手工
    MANUAL("manual"),
    // 外部对接
    API("api");

    private String code;

    VoucherGenerateTypeEnum(String code){
        this.code = code;
    }

    public String getCode(){
        return code;
    }
}
