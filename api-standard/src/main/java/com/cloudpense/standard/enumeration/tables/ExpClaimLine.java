package com.cloudpense.standard.enumeration.tables;

/**
 * <AUTHOR>
 * @date 2019/10/11 9:32
 */
public class ExpClaimLine extends TableObject{

    private String payObject;

    private String claimAmount;

    private String headerId;

    private String finNetAmount;

    private String finClaimAmount;

    // 税金借方对应金额
    private String finTaxAmount;

    // 税金贷方对应金额
    private String finTransTaxAmount;

    private String lineId;

    private String drAccountId;

    private String crAccountId;

    // 税金借方
    private String taxAccountId;

    // 税金贷方
    private String taxCrAccountId;

    private String column10;

    private String originalAmount;

    private String internalType;

    private String finReceiptAmount;

    private String column22;

    public String getColumn22() {
        return getFieldName("column22");
    }

    public String getFinReceiptAmount() {
        return getFieldName("fin_receipt_amount");
    }

    public String getInternalType() {
        return getFieldName("internal_type");
    }

    public String getOriginalAmount() {
        return getFieldName("original_amount");
    }

    public String getColumn10() {
        return getFieldName("column10");
    }

    public String getFinNetAmount() {
        return getFieldName("fin_net_amount");
    }

    public String getFinClaimAmount() {
        return getFieldName("fin_claim_amount");
    }

    public String getFinTaxAmount() {
        return getFieldName("fin_tax_amount");
    }

    public String getFinTransTaxAmount() {
        return getFieldName("fin_trans_tax_amount");
    }

    public String getLineId() {
        return getFieldName("line_id");
    }

    public String getDrAccountId() {
        return getFieldName("dr_account_id");
    }

    public String getCrAccountId() {
        return getFieldName("cr_account_id");
    }

    public String getTaxAccountId() {
        return getFieldName("tax_account_id");
    }

    public String getTaxCrAccountId() {
        return getFieldName("tax_cr_account_id");
    }

    public String getHeaderId() {
        return getFieldName("header_id");
    }

    public String getClaimAmount() {
        return getFieldName("claim_amount");
    }

    public String getPayObject() {
        return getFieldName("pay_object");
    }


}
