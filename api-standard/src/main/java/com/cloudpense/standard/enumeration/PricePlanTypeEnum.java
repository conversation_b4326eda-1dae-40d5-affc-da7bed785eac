package com.cloudpense.standard.enumeration;

import java.util.HashMap;
import java.util.Map;

/**
 * 付费方案类型Enum
 * Date: 2020-05-20
 *
 * <AUTHOR>
 */
public enum PricePlanTypeEnum {

    TWENTY_PERSON("price_9ee044f76b3f900d", "20人套餐（可试用）"),
    FIFTY_PERSON("price_9ee048a5bbfc500d", "50人套餐"),
    ONE_HUNDRED_PERSON("price_9ee041220b79900d", "100人套餐"),
    TWO_HUNDRED_PERSON("price_9ee051320572900d", "200人套餐"),
    TEN_PERSON_TEST("price_9e38c2e9a5be900d", "10人付费（测试专用）"),
    FIFTY_PERSON_TSET("price_9e23f3a7713f500e", "50人付费（测试专用）");

    /**
     * 付费方案id
     */
    private String id;
    /**
     * 付费方案名称
     */
    private String name;

    public static final Map<String, String> id2NameMap = new HashMap();

    static {
        for (PricePlanTypeEnum pricePlanTypeEnum : PricePlanTypeEnum.values()) {
            id2NameMap.put(pricePlanTypeEnum.getId(), pricePlanTypeEnum.getName());
        }
    }

    PricePlanTypeEnum(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
