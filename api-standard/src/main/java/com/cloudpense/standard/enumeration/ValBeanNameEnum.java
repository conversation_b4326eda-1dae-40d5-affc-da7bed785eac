package com.cloudpense.standard.enumeration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 校验规则beanName
 * valName: 前台展示名称，标准校验规则统一以s开头，定制校验规则统一以c开头
 * beanName: 代码中bean的名称
 * Date: 2021-02-19
 *
 * <AUTHOR>
 */
public enum ValBeanNameEnum {

    S_RULE1("s_verifyPreAbsence", "verifyPreAbsence", "判断前序假期"),
    S_RULE2("s_verifyQuota", "verifyAccountQuota", "校验额度是否足够"),
    S_RULE3("s_verifyCancellation", "verifyCancellation", "销假时校验额度是否对应请假额度"),
    S_RULE4("s_hrDateDiff", "hrDateDiff", "公式(头submit_date减start_datetime天差)"),
    S_RULE5("s_attachmentCount", "attachmentCount", "头附件数校验"),
    S_RULE6("s_lineAttachmentCount", "lineAttachmentCount", "行附件数校验"),
    S_RULE7("s_destination", "destination", "发票上城市在头城市列表中返回0，否则返回1"),
    S_RULE8("s_destinationTo", "destinationTo", "发票上城市与单据行上城市一致返回0，否则返回1"),
    S_RULE9("s_passengerNameCheck", "passengerNameCheck", "火车票乘客姓名和单据charge_user姓名校验"),
    S_RULE10("s_invoiceType", "invoiceType", "单据行invoiceType校验"),
    S_RULE11("s_verifyLeaveTime", "verifyLeaveTime", "时间行请假时间校验"),
    S_RULE12("s_verifyCancelTime", "verifyCancelTime", "时间行销假时间校验"),
    S_RULE13("s_blockchainInvoice", "blockchainInvoice", "非查验发票（区块链发票）上购买方税号税码和单据公司branch_id对应税号或税码不一致返回1否则0"),
    S_RULE14("s_baseCity", "baseCity", "行上的destination_city_to和行上的头上的charge user的base_city校验,一致返回0，不一致返回1"),
    S_RULE15("s_expensePayMethod", "expensePayMethod", "expense支付方式校验"),
    S_RULE16("s_expenseSource", "expenseSource", "消费来源校验"),
    S_RULE17("s_receiptModify", "receiptModify", "票据内容与识别出来的值不同返回1，否则返回0")
  ;
    /**
     * 检验规则名称
     */
    private String valName;
    /**
     * beanName
     */
    private String beanName;
    /**
     * 描述
     */
    private String description;

    public static final Map<String, String> valNameMap = new HashMap<>();

    public static final List<String> valNameList = new ArrayList<>();

    public static final List<String> beanNameList = new ArrayList<>();

    static {
        for (ValBeanNameEnum valBeanNameEnum: ValBeanNameEnum.values()) {
            valNameMap.put(valBeanNameEnum.getValName(),valBeanNameEnum.getBeanName());
            valNameList.add(valBeanNameEnum.getValName());
            beanNameList.add(valBeanNameEnum.getBeanName());
        }
    }

    ValBeanNameEnum(String valName, String beanName, String description) {
        this.valName = valName;
        this.beanName = beanName;
        this.description = description;
    }

    public String getValName() {
        return valName;
    }

    public void setValName(String valName) {
        this.valName = valName;
    }

    public String getBeanName() {
        return beanName;
    }

    public void setBeanName(String beanName) {
        this.beanName = beanName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
