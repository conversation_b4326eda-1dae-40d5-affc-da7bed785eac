package com.cloudpense.standard.enumeration;

import java.util.HashMap;
import java.util.Map;

/**
 * 购买类型Enum
 * Date: 2020-05-20
 *
 * <AUTHOR>
 */
public enum BuyTypeEnum {

    BUY("buy", "普通购买"),
    UPGRADE("upgrade", "升级购买"),
    RENEW("renew", "续费购买");

    /**
     * 英文名
     */
    private String enName;
    /**
     * 中文名
     */
    private String zhName;

    public static final Map<String, String> en2ZhMap = new HashMap();

    static {
        for (BuyTypeEnum buyTypeEnum: BuyTypeEnum.values()) {
            en2ZhMap.put(buyTypeEnum.getEnName(),buyTypeEnum.getZhName());
        }
    }

    BuyTypeEnum(String enName, String zhName) {
        this.enName = enName;
        this.zhName = zhName;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public String getZhName() {
        return zhName;
    }

    public void setZhName(String zhName) {
        this.zhName = zhName;
    }
}
