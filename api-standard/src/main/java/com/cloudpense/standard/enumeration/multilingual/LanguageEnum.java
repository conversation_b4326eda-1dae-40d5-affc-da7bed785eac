package com.cloudpense.standard.enumeration.multilingual;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum LanguageEnum {

    ZH_CN("zh_cn", "zh_CN"),
    ZH_TW("zh_tw", "zh_TW"),
    en_US("en_us", "en_US"),
    JA_JP("ja_jp", "ja_JP");

    private String code;

    private String name;

    public static final Map<String, String> codeToName = new HashMap<>();

    public static final List<String> names = new ArrayList<>();

    LanguageEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    static {
        for (LanguageEnum languageEnum : LanguageEnum.values()) {
            codeToName.put(languageEnum.getCode(), languageEnum.getName());
            names.add(languageEnum.getName());
        }
    }

    public String getCode() {

        return this.code;
    }

    public String getName() {

        return this.name;
    }

}
