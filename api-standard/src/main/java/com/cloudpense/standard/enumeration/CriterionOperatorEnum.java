package com.cloudpense.standard.enumeration;

import java.util.HashMap;
import java.util.Map;

public enum CriterionOperatorEnum {
    EQUAL("eq", "="),
    NOT_EQUAL("neq", "!="),
    IN("in", "in"),
    NOT_IN("nin", "not in"),
    LIKE("lk", "like"),
    NOT_LIKE("nlk", "not like"),
    BETWEEN("btw", "between"),
    EGT("egt", ">="),
    ELT("elt", "<="),
    GT("gt", ">"),
    LT("lt", "<"),
    AND("op1", "and"),
    OR("op2", "or");

    /**
     * 英文名
     */
    private String code;
    /**
     * 中文名
     */
    private String value;

    public static final Map<String, String> criterionOperatorMap = new HashMap();

    static {
        for (CriterionOperatorEnum criterionOperatorEnum: CriterionOperatorEnum.values()) {
            criterionOperatorMap.put(criterionOperatorEnum.getCode(),criterionOperatorEnum.getValue());
        }
    }

    CriterionOperatorEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValue(String code) {
        return criterionOperatorMap.get(code);
    }
}
