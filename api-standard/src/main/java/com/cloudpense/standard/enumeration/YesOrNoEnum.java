package com.cloudpense.standard.enumeration;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2021/7/5 11:33
 */
@Getter
public enum YesOrNoEnum {
    /**
     * Y、N的枚举
     */
    YES("Y", "是"),
    NO("N", "否");
    private final String code;
    private final String des;

    YesOrNoEnum(String code, String des) {
        this.code = code;
        this.des = des;
    }

    public static YesOrNoEnum getByCode(String code){
        YesOrNoEnum[] values = YesOrNoEnum.values();
        return Arrays.stream(values).filter(u -> u.code.equals(code)).findFirst().orElse(null);
    }
}
