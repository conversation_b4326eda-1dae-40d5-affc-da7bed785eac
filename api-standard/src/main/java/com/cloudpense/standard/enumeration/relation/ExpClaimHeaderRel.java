package com.cloudpense.standard.enumeration.relation;


import com.cloudpense.standard.vo.Relation;

/**
 * <AUTHOR>
 * @date 2019/10/7 15:45
 */
@Deprecated
public enum ExpClaimHeaderRel {

    /**
     * "fnd_user表和exp_claim_header关联，关联键user_id 和 chargeUser"
     */
    fndUserCharge{
        @Override
        public Relation getRelation() {

            return getRelationObjct("fndUserCharge");
        }
    },


    /**
     * fnd_user表和exp_claim_header关联，关联键user_id和 submitUser
     */
    fndUserSubmit{
        @Override
        public Relation getRelation() {
           return getRelationObjct("fndUserSubmit");
        }
    },

    /**
     * fnd_user表和exp_claim_header关联，关联键pay_user和 submitUser
     */
    fndUserPay{
        @Override
        public Relation getRelation() {
            return getRelationObjct("fndUserPay");
        }
    },


    /**
     * "exp_supplier表和exp_claim_header关联，supplier_id 和 supplier_id"
     */
    expSupplier{
        @Override
        public Relation getRelation() {
            return getRelationObjct("expSupplier");
        }
    },

    /**
     *  "exp_supplier_tl表和exp_claim_header关联，supplier_id 和 supplier_id"
     */
    expSupplierTl{
        @Override
        public Relation getRelation() {
            return getRelationObjct("expSupplierTl");
        }
    },

    /**
     *  "exp_header_type表和exp_claim_header关联，关联键header_type_id 和 type_id"
     */
    expHeaderType{
        @Override
        public Relation getRelation() {
            return getRelationObjct("expHeaderType");
        }
    },

    /**
     *  "ap_payment_header表和exp_claim_header关联，关联键source_id 和 header_id"
     */
    apPaymentHeader{
        @Override
        public Relation getRelation() {
            return getRelationObjct("apPaymentHeader");
        }
    },

    /**
     *  "fnd_department表和exp_claim_header关联，关联键 department_id和 branch_id"
     */
    fndDepartmentBranch {
        @Override
        public Relation getRelation() {
            return getRelationObjct("fndDepartmentBranch");
        }
    },

    /**
     *  fnd_department表和exp_claim_header关联，关联键charge_department 和 department_id"
     */
    fndDepartmentCharge {
        @Override
        public Relation getRelation() {
            return getRelationObjct("fndDepartmentCharge");
        }
    },

    /**
     *  "exp_claim_line表和exp_claim_header关联， header_id 和  header_id"
     */
    expClaimLine {
        @Override
        public Relation getRelation() {
            return getRelationObjct("expClaimLine");
        }
    },


    /**
     *  fnd_user_account表和exp_claim_header关联， account_id 和  user_account_id"
     */
    fndUserAccount {
        @Override
        public Relation getRelation() {
            return getRelationObjct("fndUserAccount");
        }
    },


    /**
     *  "exp_supplier_account 表和exp_claim_header关联， account_id 和  supplier_account_id")
     */
    expSupplierAccount {
        @Override
        public Relation getRelation() {
            return getRelationObjct("expSupplierAccount");
        }
    },



    mainTable {
                @Override
                public Relation getRelation() {
                    Relation relation = new Relation();
                    relation.setTableName("exp_claim_header");
                    return relation;
                }
            };

    public abstract Relation getRelation();

    private static Relation getRelationObjct(String relationShip) {
        Relation relation = new Relation();
        relation.setRelation(relationShip);
        relation.setTableName("exp_claim_header");
        return relation;
    }
}
