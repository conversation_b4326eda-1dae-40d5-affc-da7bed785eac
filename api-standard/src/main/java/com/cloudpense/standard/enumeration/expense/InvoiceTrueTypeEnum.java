package com.cloudpense.standard.enumeration.expense;

import java.util.HashMap;
import java.util.Map;
public enum InvoiceTrueTypeEnum {
    Y("Y", "已验真"),
    F("F", "查验失败"),
    C("C", "待查验"),
    R("R", "识别中"),
    U("U", "无法识别"),
    I("I", "已识别");

    public static final Map<String, String> InvoiceTrueTypeEnumToName = new HashMap<>();

    static {
        for (InvoiceTrueTypeEnum invoiceTrueType : InvoiceTrueTypeEnum.values()) {
            InvoiceTrueTypeEnumToName.put(invoiceTrueType.getCode(), invoiceTrueType.getValue());
        }
    }

    private String code;

    private String value;

    InvoiceTrueTypeEnum (String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {

        return this.code;
    }

    public String getValue() {

        return this.value;
    }
}
