package com.cloudpense.standard.enumeration;

/**
 * 平台Enum
 * Date: 2020-06-02
 *
 * <AUTHOR>
 */
public enum  PlatformEnum {

    WeChatWork("wechatqy", "企业微信"),
    <PERSON><PERSON><PERSON>("feishu", "飞书"),
    <PERSON><PERSON><PERSON><PERSON>("dingtalk", "钉钉");

    /**
     * 英文名
     */
    private String enName;
    /**
     * 中文名
     */
    private String zhName;

    PlatformEnum(String enName, String zhName) {
        this.enName = enName;
        this.zhName = zhName;
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName;
    }

    public String getZhName() {
        return zhName;
    }

    public void setZhName(String zhName) {
        this.zhName = zhName;
    }
}
