package com.cloudpense.standard.enumeration.tables;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2019/10/7 15:17
 */
public class  ExpClaimHeader extends TableObject {

    private String headerId;

    private String companyId;

    private String documentId;

    private String documentNum;

    private String linkHeaderId;

    @ApiModelProperty("link_budget_id")
    private String linkBudgetId;

    @ApiModelProperty("link_close")
    private String linkClose;


    private String internalType;

    @ApiModelProperty("travel_method")
    private String travelMethod;


    @ApiModelProperty("business_purpose")
    private String businessPurpose;

    @ApiModelProperty("leave_type")
    private String leaveType;

    @ApiModelProperty("leave_day")
    private String leaveDay;

    private String totalAmount;

    private String totalClaimAmount;

    private String advanceAmount;

    private String totalPayAmount;

    @ApiModelProperty("total_adjust_point")
    private String totalAdjustPoint;

    private String currencyCode;

    @ApiModelProperty("submit_date")
    private String submitDate;

    @ApiModelProperty("start_datetime")
    private String startDatetime;

    @ApiModelProperty("end_datetime")
    private String endDatetime;

    @ApiModelProperty("destination_city")
    private String destinationCity;

    private String description;

    @ApiModelProperty("finance_description")
    private String financeDescription;

    @ApiModelProperty("formula_description")
    private String formulaDescription;


    @ApiModelProperty("product_name")
    private String productName;

    @ApiModelProperty("project_name")
    private String projectName;

    @ApiModelProperty("customer_name")
    private String customerName;

    @ApiModelProperty("supplier_id")
    private String supplierId;

    @ApiModelProperty("supplier_name")
    private String supplierName;

    @ApiModelProperty("due_date")
    private String dueDate;

    @ApiModelProperty("reminder_date")
    private String reminderDate;

    @ApiModelProperty("plan_start_datetime")
    private String planStartDatetime;

    @ApiModelProperty("plan_end_datetime")
    private String planEndDatetime;

    @ApiModelProperty("branch_id")
    private String branchId;

    @ApiModelProperty("positionId")
    private String positionId;

    @ApiModelProperty("submit_department")
    private String submitDepartment;

    @ApiModelProperty("charge_department")
    private String chargeDepartment;

    @ApiModelProperty("submit_user")
    private String submitUser;

    @ApiModelProperty("charge_user")
    private String chargeUser;

    private String payObject;

    @ApiModelProperty("pay_user")
    private String payUser;

    @ApiModelProperty("invoice_flag")
    private String invoiceFlag;

    @ApiModelProperty("trans_serial_num")
    private String transSerialNum;

    @ApiModelProperty("payment_date")
    private String paymentDate;

    @ApiModelProperty("actual_payment_date")
    private String actualPaymentDate;

    @ApiModelProperty("user_account_id")
    private String userAccountId;

    @ApiModelProperty("supplier_account_id")
    private String supplier_account_id;

    @ApiModelProperty("branch_account_id")
    private String branchAccountId;

    @ApiModelProperty("gl_period")
    private String glPeriod;

    @ApiModelProperty("priority")
    private String priority;

    @ApiModelProperty("header_type_id")
    private String headerTypeId;

    private String column1;

    private String column2;

    private String column3;

    private String column4;

    private String column5;

    private String column6;

    private String column7;
    private String column8;
    private String column9;

    private String column10;

    private String column11;
    private String column12;
    private String column13;
    private String column14;
    private String column15;
    private String column16;
    private String column17;
    private String column18;
    private String column19;
    private String column20;
    private String column21;
    private String column22;

    private String column23;
    private String column24;
    private String column25;
    private String column26;
    private String column27;
    private String column28;
    private String column29;
    private String column30;

    private String column31;

    private String column32;

    private String column33;

    private String column34;

    private String column35;

    private String column36;

    private String column37;

    private String column38;

    private String column39;

    private String column40;

    private String column41;
    private String column42;
    private String column43;
    private String column44;
    private String column45;
    private String column46;
    private String column47;
    private String column48;
    private String column49;

    private String column50;

    @ApiModelProperty("column_json")
    private String columnJson;

    @ApiModelProperty("credit_pay")
    private String creditPay;

    @ApiModelProperty("print_count")
    private String printCount;

    @ApiModelProperty("cost_element_id")
    private String costElementId;

    @ApiModelProperty("ledger1")
    private String ledger1;

    @ApiModelProperty("ledger2")
    private String ledger2;

    @ApiModelProperty("gl_batch_id")
    private String glBatchId;

    private String status;

    @ApiModelProperty("workflow_status")
    private String workflowStatus;

    @ApiModelProperty("workflow_count")
    private String workflowCount;

    @ApiModelProperty("ap_batch_id")
    private String apBatchId;

    @ApiModelProperty("fin_status")
    private String finStatus;

    @ApiModelProperty("pay_status")
    private String payStatus;

    @ApiModelProperty("pay_status_message")
    private String payStatusMessage;

    @ApiModelProperty("gl_status")
    private String glStatus;

    @ApiModelProperty("gl_status2")
    private String glStatus2;

    @ApiModelProperty("gl_message")
    private String glMessage;

    @ApiModelProperty("journal_num")
    private String journalNum;

    @ApiModelProperty("delivery_status")
    private String deliveryStatus;

    @ApiModelProperty("delivery_num")
    private String deliveryNum;

    @ApiModelProperty("workflow_id")
    private String workflowId;

    @ApiModelProperty("preapproved_date")
    private String preapprovedDate;

    @ApiModelProperty("checked_date")
    private String checkedDate;

    @ApiModelProperty("approved_date")
    private String approvedDate;

    @ApiModelProperty("closed_date")
    private String closedDate;

    @ApiModelProperty("gl_date")
    private String glDate;

    @ApiModelProperty("lock_status")
    private String lockStatus;

    @ApiModelProperty("lock_message")
    private String lockMessage;

    @ApiModelProperty("lock_date")
    private String lockDate;

    @ApiModelProperty("auditor_id")
    private String auditorId;

    @ApiModelProperty("reviewer_id")
    private String reviewerId;

    @ApiModelProperty("poster_id")
    private String posterId;

    @ApiModelProperty("external_status")
    private String externalStatus;

    @ApiModelProperty("external_message")
    private String externalMessage;

    @ApiModelProperty("external_date")
    private String externalDate;

    @ApiModelProperty("created_by")
    private String createdBy;

    @ApiModelProperty("creation_date")
    private String creationDate;

    @ApiModelProperty("last_updated_by")
    private String lastUpdatedBy;

    @ApiModelProperty("last_update_date")
    private String lastUpdateDate;

    @ApiModelProperty("revision_id")
    private String revisionId;

    @ApiModelProperty("revision_status")
    private String revisionStatus;

    @ApiModelProperty("revision_count")
    private String revisionCount;

    @ApiModelProperty("exception_level")
    private String exceptionLevel;

    @ApiModelProperty("exception_level_approve")
    private String exceptionLevelApprove;

    @ApiModelProperty("transaction_id")
    private String transactionId;

    @ApiModelProperty("old_request_id")
    private String oldRequestId;

    @ApiModelProperty("approval_number")
    private String approvalNumber;

    @ApiModelProperty("destination_city_to")
    private String destinationCityTo;

    @ApiModelProperty("from_cities")
    private String fromCities;

    @ApiModelProperty("to_cities")
    private String toCities;

    @ApiModelProperty("long_description")
    private String longDescription;


    public String getLinkBudgetId() {
        return getFieldName("link_budget_id");
    }

    public String getLinkClose() {
        return getFieldName("linkClose");
    }

    public String getTravelMethod() {
        return getFieldName("travel_method");
    }

    public String getBusinessPurpose() {
        return getFieldName("business_purpose");
    }

    public String getLeaveType() {
        return getFieldName("leave_type");
    }

    public String getLeaveDay() {
        return getFieldName("leave_day");
    }

    public String getTotalAdjustPoint() {
        return getFieldName("total_adjust_point");
    }

    public String getSubmitDate() {
        return  getFieldName("submit_date");
    }

    public String getStartDatetime() {
        return  getFieldName("start_datetime");
    }

    public String getEndDatetime() {
        return  getFieldName("end_datetime");
    }

    public String getDestinationCity() {
        return  getFieldName("destination_city");
    }

    public String getFinanceDescription() {
        return  getFieldName("finance_description");
    }

    public String getFormulaDescription() {
        return  getFieldName("formula_description");
    }

    public String getProductName() {
        return  getFieldName("product_name");
    }

    public String getProjectName() {
        return  getFieldName("project_name");
    }

    public String getCustomerName() {
        return  getFieldName("customer_name");
    }

    public String getSupplierId() {
        return  getFieldName("supplier_id");
    }

    public String getSupplierName() {
        return  getFieldName("supplier_name");
    }

    public String getDueDate() {
        return  getFieldName("due_date");
    }

    public String getReminderDate() {
        return  getFieldName("reminder_date");
    }

    public String getPlanStartDatetime() {
        return  getFieldName("plan_start_datetime");
    }

    public String getPlanEndDatetime() {
        return  getFieldName("plan_end_datetime");
    }

    public String getBranchId() {
        return  getFieldName("branch_id");
    }

    public String getPositionId() {
        return  getFieldName("positionId");
    }

    public String getSubmitDepartment() {
        return  getFieldName("submit_department");
    }

    public String getChargeDepartment() {
        return  getFieldName("charge_department");
    }

    public String getSubmitUser() {
        return  getFieldName("submit_user");
    }

    public String getChargeUser() {
        return  getFieldName("charge_user");    }

    public String getPayUser() {
        return  getFieldName("pay_user");
    }

    public String getInvoiceFlag() {
        return  getFieldName("invoice_flag");
    }

    public String getTransSerialNum() {
        return  getFieldName("trans_serial_num");
    }

    public String getPaymentDate() {
        return  getFieldName("payment_date");
    }

    public String getActualPaymentDate() {
        return  getFieldName("actual_payment_date");
    }

    public String getUserAccountId() {
        return  getFieldName("user_account_id");
    }

    public String getSupplier_account_id() {
        return  getFieldName("supplier_account_id");
    }

    public String getBranchAccountId() {
        return  getFieldName("branch_account_id");
    }

    public String getGlPeriod() {
        return glPeriod;
    }

    public String getPriority() {
        return priority;
    }

    public String getHeaderTypeId() {
        return headerTypeId;
    }

    public String getColumn8() {
        return column8;
    }

    public String getColumn9() {
        return column9;
    }

    public String getColumn12() {
        return column12;
    }

    public String getColumn13() {
        return column13;
    }

    public String getColumn14() {
        return column14;
    }

    public String getColumn15() {
        return column15;
    }

    public String getColumn16() {
        return column16;
    }

    public String getColumn17() {
        return column17;
    }

    public String getColumn18() {
        return column18;
    }

    public String getColumn19() {
        return column19;
    }

    public String getColumn20() {
        return column20;
    }

    public String getColumn21() {
        return column21;
    }

    public String getColumn22() {
        return getFieldName("column22");
    }

    public String getColumn24() {
        return column24;
    }

    public String getColumn25() {
        return column25;
    }

    public String getColumn26() {
        return column26;
    }

    public String getColumn27() {
        return column27;
    }

    public String getColumn28() {
        return column28;
    }

    public String getColumn29() {
        return column29;
    }

    public String getColumn30() {
        return column30;
    }

    public String getColumn42() {
        return column42;
    }

    public String getColumn43() {
        return column43;
    }

    public String getColumn44() {
        return column44;
    }

    public String getColumn45() {
        return column45;
    }

    public String getColumn46() {
        return column46;
    }

    public String getColumn47() {
        return column47;
    }

    public String getColumn48() {
        return column48;
    }

    public String getColumn49() {
        return column49;
    }

    public String getColumnJson() {
        return columnJson;
    }

    public String getCreditPay() {
        return creditPay;
    }

    public String getPrintCount() {
        return printCount;
    }

    public String getCostElementId() {
        return costElementId;
    }

    public String getLedger1() {
        return ledger1;
    }

    public String getLedger2() {
        return ledger2;
    }

    public String getGlBatchId() {
        return glBatchId;
    }

    public String getWorkflowStatus() {
        return workflowStatus;
    }

    public String getWorkflowCount() {
        return workflowCount;
    }

    public String getApBatchId() {
        return apBatchId;
    }

    public String getFinStatus() {
        return finStatus;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public String getPayStatusMessage() {
        return payStatusMessage;
    }

    public String getGlStatus() {
        return glStatus;
    }

    public String getGlStatus2() {
        return glStatus2;
    }

    public String getGlMessage() {
        return glMessage;
    }

    public String getJournalNum() {
        return journalNum;
    }

    public String getDeliveryStatus() {
        return deliveryStatus;
    }

    public String getDeliveryNum() {
        return deliveryNum;
    }

    public String getWorkflowId() {
        return workflowId;
    }

    public String getPreapprovedDate() {
        return preapprovedDate;
    }

    public String getCheckedDate() {
        return checkedDate;
    }

    public String getApprovedDate() {
        return approvedDate;
    }

    public String getClosedDate() {
        return closedDate;
    }

    public String getGlDate() {
        return glDate;
    }

    public String getLockStatus() {
        return lockStatus;
    }

    public String getLockMessage() {
        return lockMessage;
    }

    public String getLockDate() {
        return lockDate;
    }

    public String getAuditorId() {
        return auditorId;
    }

    public String getReviewerId() {
        return reviewerId;
    }

    public String getPosterId() {
        return posterId;
    }

    public String getExternalMessage() {
        return externalMessage;
    }

    public String getExternalDate() {
        return externalDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public String getCreationDate() {
        return creationDate;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public String getLastUpdateDate() {
        return lastUpdateDate;
    }

    public String getRevisionId() {
        return revisionId;
    }

    public String getRevisionStatus() {
        return revisionStatus;
    }

    public String getRevisionCount() {
        return revisionCount;
    }

    public String getExceptionLevel() {
        return exceptionLevel;
    }

    public String getExceptionLevelApprove() {
        return exceptionLevelApprove;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public String getOldRequestId() {
        return oldRequestId;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public String getDestinationCityTo() {
        return destinationCityTo;
    }

    public String getFromCities() {
        return fromCities;
    }

    public String getToCities() {
        return toCities;
    }

    public String getLongDescription() {
        return longDescription;
    }

    public String getColumn11() {
        return getFieldName("column11");
    }

    public String getColumn50() {
        return getFieldName("column50");
    }

    public String getExternalStatus() {
        return getFieldName("external_status");
    }

    public String getColumn23() {
        return getFieldName("column23");
    }

    public String getLinkHeaderId() {
        return getFieldName("link_header_id");
    }

    public String getColumn1() {
        return getFieldName("column1");
    }

    public String getColumn2() {
        return getFieldName("column2");
    }

    public String getColumn3() {
        return getFieldName("column3");
    }

    public String getColumn4() {
        return getFieldName("column4");
    }

    public String getColumn5() {
        return getFieldName("column5");
    }

    public String getColumn6() {
        return getFieldName("column6");
    }

    public String getColumn7() {
        return getFieldName("column7");
    }

    public String getColumn31() {
        return getFieldName("column31");
    }

    public String getColumn41() {
        return getFieldName("column41");
    }

    public String getColumn32() {
        return getFieldName("column32");
    }

    public String getColumn33() {
        return getFieldName("column33");
    }

    public String getColumn34() {
        return getFieldName("column34");
    }

    public String getColumn35() {
        return getFieldName("column35");
    }

    public String getColumn36() {
        return getFieldName("column36");
    }

    public String getColumn37() {
        return getFieldName("column37");
    }

    public String getColumn38() {
        return getFieldName("column38");
    }

    public String getColumn39() {
        return getFieldName("column39");
    }


    public String getColumn40() {
        return getFieldName("column40");
    }

    public String getHeaderId() {
        return getFieldName("header_id");
    }

    public String getTotalPayAmount() {
        return getFieldName("total_pay_amount");
    }

    public String getCompanyId() {
        return getFieldName("company_id");
    }

    public String getDocumentId() {
        return getFieldName("document_id");
    }

    public String getDocumentNum() {
        return getFieldName("document_num");
    }

    public String getInternalType() {
        return getFieldName("internal_type");
    }

    public String getTotalAmount() {
        return getFieldName("total_amount");
    }

    public String getTotalClaimAmount() {
        return getFieldName("total_claim_amount");
    }

    public String getAdvanceAmount() {
        return getFieldName("advance_amount");
    }

    public String getCurrencyCode() {
        return getFieldName("currency_code");
    }

    public String getDescription() {
        return getFieldName("description");
    }

    public String getPayObject() {
        return getFieldName("pay_object");
    }

    public String getColumn10() {
        return getFieldName("column10");
    }

    public String getStatus() {
        return getFieldName("status");
    }

}
