package com.cloudpense.standard.enumeration;

import java.util.HashMap;
import java.util.Map;

public enum WhetherEnum {

    YES("Y", "是"),
    NO("N", "否");

    public static final Map<String, String> eventToName = new HashMap<>();

    static {
        for (WhetherEnum accountEnum : WhetherEnum.values()) {
            eventToName.put(accountEnum.getCode(), accountEnum.getValue());
        }
    }

    private String code;

    private String value;

    WhetherEnum (String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {

        return this.code;
    }

    public String getValue() {

        return this.value;
    }
}
