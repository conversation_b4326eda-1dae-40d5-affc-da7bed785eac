package com.cloudpense.standard.enumeration.masterdata;

/**
 * Created by liyu on 2021/03/19.
 * GeneratedCriteria查询表名缩写枚举
 */
public enum TableNameEnum {
    // 人员
    USER("fu"),
    // 人员绑定
    USER_BIND("fub"),
    // 部门
    DEPARTMENT("fd"),
    // 部门绑定
    DEPARTMENT_BIND("fdb"),
    // 供应商
    SUPPLIER("es"),
    // 客户
    CUSTOMER("ec"),
    // 汇率
    EXCHANGERATE("fer"),
    // 会计科目
    GLACCOUNT("gla"),
    // 项目
    PROJECT("pp"),
    // 职位
    Position("fp"),
    //投递状态
    DlvDelivery("dd"),
    //单据头
    ExpClaimHeader("ech"),
    ExpClaimLine("ecl"),
    //城市
    City("fcv"),
    //城市公司
    CityCompany("fcc"),
    //工作流
    FndWorkflowPath("fwp"),
    // 值列表(fnd_lov)
    FndLov("fl"),
    // 值列表(fnd_lov_value)
    FndLovValue("flv"),
    // 客户银行
    CustomerAccount("eca"),
    // 供应商银行
    SupplierAccount("esa");

    private String code;

    TableNameEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

}
