package com.cloudpense.standard.enumeration.masterdata;

/**
 * 对外主数据查询，interfaceName枚举
 */
public enum MasterDataInterfaceNameEnum {
    //人员
    USER("getUser"),
    //供应商
    SUPPLIER("getSupplier"),
    //供应商银行
    SUPPLIER_ACCOUNT("getSupplierAccount"),
    //客户
    CUSTOMER("getCustomer"),
    //客户银行
    CUSTOMER_ACCOUNT("getCustomerAccount"),
    POSITION("getPosition"),
    //值列表数据
    LOV_VALUE("getLovValue"),
    PROJECT("getProject"),
    //部门
    DEPARTMENT("getDepartment");

    private String interfaceName;

    public String getInterfaceName() {
        return interfaceName;
    }

    MasterDataInterfaceNameEnum(String interfaceName) {
        this.interfaceName = interfaceName;
    }
}
