package com.cloudpense.standard.enumeration;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2021/7/5 16:56
 */
@Getter
public enum WorkflowTimeoutActionEnum {
    /**
     * 工作流超时操作
     */
    SEND_EMAIL(100, "sendEmail", "发送邮件"),
    AUTO_APPROVE(9999, "autoApprove", "自动审批通过");
    private final Integer rank;
    private final String action;
    private final String des;

    WorkflowTimeoutActionEnum(Integer rank, String action, String des) {
        this.rank = rank;
        this.action = action;
        this.des = des;
    }

    public static WorkflowTimeoutActionEnum getByAction(String action) {
        WorkflowTimeoutActionEnum[] values = WorkflowTimeoutActionEnum.values();
        return Arrays.stream(values).filter(u -> u.action.equals(action)).findFirst().orElse(null);
    }

}
