package com.cloudpense.standard.enumeration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 校验规则触发条件
 * operator: 操作符
 * Date: 2021-02-19
 *
 * <AUTHOR>
 */
public enum ValRuleSourceEnum {

    NEXP("NEXP", "n_exp"),
    EXP("NEXP", "exp"),
    EXPL("NEXPL", "claim_line"),
    NEXPL("NEXPL", "n_claim_line"),
    NEXPTL("NEXPTL", "time_line");


    /**
     * source
     */
    private String source;
    /**
     * 对应json
     */
    private String code;

    public static final Map<String, String> sourceMap = new HashMap();

    public static final List<String> sourceList = new ArrayList<>();


    static {
        for (ValRuleSourceEnum valRuleSourceEnum: ValRuleSourceEnum.values()) {
            sourceMap.put(valRuleSourceEnum.getSource(),valRuleSourceEnum.getCode());
            sourceList.add(valRuleSourceEnum.getSource());
        }
    }

    ValRuleSourceEnum(String source, String code) {
        this.source = source;
        this.code = code;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
