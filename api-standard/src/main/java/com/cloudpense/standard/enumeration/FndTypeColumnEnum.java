package com.cloudpense.standard.enumeration;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> wujie
 * @date : 2020/7/27 0:25
 * @Description : fnd_type_column表source字段枚举
 */
public enum FndTypeColumnEnum {
    CLAIM("claim", "exp_claim_header"),
    LOV("lov", "fnd_lov_value"),
    USER("user", "fnd_user"),
    SALARY("salary", "hr_salary_month"),
    FRM("frm", "frm_value"),
    PROJECT("project", "pjm_project"),
    SUPPLIER("supplier", "exp_supplier"),
    DEPARTMENT("department", "fnd_department"),
    CUSTOMER("customer", "exp_customer"),
    CITY("city", "fnd_city"),
    RULES("accountRules", "exp_account_rules"),
    ACCOUNT("accountObject", "exp_account_object"),
    EXP_BUDGET("expBudgetAssign", "exp_budget"),
    COLUMN_VALUE("columnValue", "column_value"),
    TRANS_SERIAL("transSerial", "fnd_trans_serial"),
    GL_BUDGET_ACCOUNT("glBudgetAccount","gl_budget_account"),
    FINANCE_DOCUMENT("documentExportFile", "document_export_file"),
    RECEIPT_MANAGE("receiptManageExport", "receipt_manage_export"),
    PAYMENT_DOCUMENT("paymentManagement", "payment_management"),
    EXP_OUTPUT_INVOICE("expOutputInvoiceExport","exp_output_invoice_export"),
    INVOICE_CANCEL("invoiceCancelExport","invoice_cancel_export"),
    COMMODITY("commodity", "commodity"),
    INVOICE_AUTHENTICATION("invoiceAuthenticationExport", "invoice_authentication_export"),
    INVOICE_AUTHENTICATION_ATTRIBUTION_STATISTICAL("invoiceAttributionStatisticalExport","invoice_attribution_statistical_export"),
    POSITION("position","fnd_position")
    ;


    /**
     * 主数据的类型 fnd_master_data_file_config 表中的 column_type 字段
     */
    private String type;

    /**
     * fnd_column_type 表中的 type字段
     */
    private String source;

    public static final Map<String, String> typeToSource = new HashMap<>();

    static {
        for (FndTypeColumnEnum enmu : FndTypeColumnEnum.values()) {
            typeToSource.put(enmu.getType(), enmu.getSource());
        }
    }

    FndTypeColumnEnum(String type, String source) {
        this.type = type;
        this.source = source;
    }

    public String getType() {
        return type;
    }

    public String getSource() {
        return source;
    }
}
