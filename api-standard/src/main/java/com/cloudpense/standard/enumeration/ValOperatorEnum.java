package com.cloudpense.standard.enumeration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 校验规则操作符
 * operator: 操作符
 * Date: 2021-02-19
 *
 * <AUTHOR>
 */
public enum ValOperatorEnum {

    EQUAL("=",  "等于(=)", "=="),
    NOT_EQUAL("<>",  "不等于(<>)", "!="),
    GT(">",  "大于(>)", ">"),
    LT("<",  "小于(<)", "<"),
    EGT(">=",  "大于等于(>=)", ">="),
    ELT("<=",  "小于等于(<=)", "<="),
    IN("in",  "包含(contain)", "contains"),
    NOT_IN("not in",  "不包含(not contain)", "contains"),
    EMPTY("is null",  "为空(is empty)", "== null"),
    NOT_EMPTY("is not null",  "不为空(is not empty)", "!= null");


    /**
     * 操作符
     */
    private String operator;
    /**
     * 描述
     */
    private String description;

    private String code;

    public static final Map<String, String> operatorMap = new HashMap();

    public static final Map<String, String> codeMap = new HashMap();

    public static final List<String> operatorList = new ArrayList<>();


    static {
        for (ValOperatorEnum valOperatorEnum: ValOperatorEnum.values()) {
            operatorMap.put(valOperatorEnum.getOperator(),valOperatorEnum.getDescription());
            codeMap.put(valOperatorEnum.getOperator(),valOperatorEnum.getCode());
            operatorList.add(valOperatorEnum.getOperator());
        }
    }

    ValOperatorEnum(String operator, String description, String code) {
        this.operator = operator;
        this.description = description;
        this.code = code;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
