package com.cloudpense.standard.enumeration;

import java.util.HashMap;
import java.util.Map;

public enum Class2TableMappingEnum {
    EXP_CLAIM_HEADER("ExpClaimHeaderCommonVo", "exp_claim_header"),
    <PERSON><PERSON><PERSON>("DepartmentVo", "fnd_department"),
    BR<PERSON>CH_ACCOUNT("UserApiVo", "fnd_user"),
    CUSTOMER("CustomerVo", "fnd_customer"),
    CUSTOMER_ACCOUNT("CustomerAccountVo", "fnd_customer_account");

    /**
     * 类名
     */
    private String className;

    /**
     * 表名
     */
    private String tableName;

    private static final Map<String, String> class2tableMap = new HashMap();

    static {
        for (Class2TableMappingEnum columnTypeSource : Class2TableMappingEnum.values()) {
            class2tableMap.put(columnTypeSource.getClassName(), columnTypeSource.getTableName());
        }
    }

    Class2TableMappingEnum(String className, String tableName) {
        this.className = className;
        this.tableName = tableName;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public static String getTableName(String className) {
        return class2tableMap.get(className);
    }
}
