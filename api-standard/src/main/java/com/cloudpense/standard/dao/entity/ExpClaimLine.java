package com.cloudpense.standard.dao.entity;

import com.cloudpense.standard.constants.ValidateErrorMessage;
import com.cloudpense.standard.vo.common.BaseColumn100Vo;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ExpClaimLine  extends BaseColumn100Vo {

    @JsonProperty("line_id")
    private Integer lineId;
    @JsonProperty("header_id")
    private Integer headerId;
    private String source;
    @JsonProperty("expense_id")
    private Integer expenseId;
    @JsonProperty("link_header_id")
    private Integer linkHeaderId;
    @JsonProperty("link_line_id")
    private Integer linkLineId;
    @JsonProperty("type_id")
    private Integer typeId;
    @JsonProperty("internal_type")
    private String internalType;
    @JsonProperty("trip_type")
    private Integer tripType;
    @JsonProperty("flight_type")
    private Integer flightType;
    @JsonProperty("train_type")
    private Integer trainType;
    @JsonProperty("flight_class")
    private String flightClass;
    @JsonProperty("train_class")
    private String trainClass;
    @JsonProperty("ship_class")
    private String shipClass;
    @JsonProperty("time_length")
    private BigDecimal timeLength;
    @JsonProperty("depart_begin_datetime")
    private Date departBeginDatetime;
    @JsonProperty("depart_end_datetime")
    private Date departEndDatetime;
    @JsonProperty("offset_amount")
    private BigDecimal offsetAmount;
    @JsonProperty("depart_type")
    private Integer departType;
    @JsonProperty("return_begin_datetime")
    private Date returnBeginDatetime;
    @JsonProperty("return_end_datetime")
    private Date returnEndDatetime;
    @JsonProperty("return_type")
    private Integer returnType;
    @JsonProperty("passenger_list")
    private String passengerList;
    @JsonProperty("passenger_num")
    private Integer passengerNum;
    @JsonProperty("charge_user")
    private Integer chargeUser;
    @JsonProperty("standard_id")
    private Integer standardId;
    @JsonProperty("approval_number")
    private String approvalNumber;
    @JsonProperty("line_num")
    private String lineNum;

    private BigDecimal price;
    @JsonProperty("standard_price")
    private BigDecimal standardPrice;
    @JsonProperty("rm_receive_amount")
    private BigDecimal rmReceiveAmount;
    @JsonProperty("fl_receive_amount")
    private BigDecimal flReceiveAmount;
    @JsonProperty("receive_complete")
    private String receiveComplete;
    @JsonProperty("standard_currency")
    private String standardCurrency;

    private BigDecimal quantity;
    @JsonProperty("original_amount")
    private BigDecimal originalAmount;
    @JsonProperty("receipt_date")
    private Date receiptDate;
    @JsonProperty("receipt_amount")
    @NotBlank(message = ValidateErrorMessage.CLAIM_LINE_RECEIPT_NOT_BLANK)
    private BigDecimal receiptAmount;
    @JsonProperty("receipt_currency")
    @NotBlank(message = ValidateErrorMessage.CLAIM_RECEIPT_CURRENCY_NOT_BLANK)
    private String receiptCurrency;
    @JsonProperty("exchange_rate")
    @NotBlank(message = ValidateErrorMessage.CLAIM_EXCHANGE_RATE_NOT_BLANK)
    private BigDecimal exchangeRate;
    @JsonProperty("com_exchange_rate")
    private BigDecimal comExchangeRate;
    @NotBlank(message = ValidateErrorMessage.CLAIM_AMOUNT_NOT_BLANK)
    @JsonProperty("claim_amount")
    private BigDecimal claimAmount;
    @JsonProperty("claim_currency")
    private String claimCurrency;
    @JsonProperty("tax_code_id")
    private Integer taxCodeId;
    @JsonProperty("tax_amount")
    private BigDecimal taxAmount;

    @JsonProperty("pay_currency_amount")
    private BigDecimal payCurrencyAmount;
    @JsonProperty("fin_pay_currency_amount")
    private BigDecimal finPayCurrencyAmount;
    @JsonProperty("pay_exchange_rate")
    private BigDecimal payExchangeRate;

    @JsonProperty("trans_tax_amount")
    private BigDecimal transTaxAmount;
    @JsonProperty("net_amount")
    private BigDecimal netAmount;
    @JsonProperty("pay_method_id")
    private Integer payMethodId;
    @JsonProperty("pay_object")
    private String payObject;
    @JsonProperty("payment_type")
    private String paymentType;
    @JsonProperty("trans_method_id")
    private String transMethodId;
    @JsonProperty("serial_id")
    private Integer serialId;
    @JsonProperty("pay_amount")
    private BigDecimal payAmount;
    @JsonProperty("pay_claim_amount")
    private BigDecimal payClaimAmount;
    @JsonProperty("adjust_point")
    private BigDecimal adjustPoint;
    @JsonProperty("offset_detail_str")
    private String offsetDetail;
    @JsonProperty("return_amount")
    private BigDecimal returnAmount;
    @JsonProperty("linked_amount")
    private BigDecimal linkedAmount;

    private String comments;
    @JsonProperty("fin_receipt_amount")
    private BigDecimal finReceiptAmount;
    @JsonProperty("fin_exchange_rate")
    private BigDecimal finExchangeRate;
    @JsonProperty("fin_claim_amount")
    private BigDecimal finClaimAmount;
    @JsonProperty("fin_tax_code_id")
    private Integer finTaxCodeId;
    @JsonProperty("fin_tax_amount")
    private BigDecimal finTaxAmount;
    @JsonProperty("fin_trans_tax_amount")
    private BigDecimal finTransTaxAmount;
    @JsonProperty("fin_net_amount")
    private BigDecimal finNetAmount;
    @JsonProperty("fin_pay_amount")
    private BigDecimal finPayAmount;
    @JsonProperty("fin_pay_claim_amount")
    private BigDecimal finPayClaimAmount;
    @JsonProperty("fin_adjust_point")
    private BigDecimal finAdjustPoint;
    @JsonProperty("finance_comments")
    private String financeComments;
    @JsonProperty("receipt_location")
    private String receiptLocation;
    @JsonProperty("location_from")
    private String locationFrom;
    @JsonProperty("location_to")
    private String locationTo;
    @JsonProperty("shop_name")
    private String shopName;
    @JsonProperty("shop_location")
    private String shopLocation;
    @JsonProperty("start_datetime")
    private Date startDatetime;
    @JsonProperty("end_datetime")
    private Date endDatetime;
    @JsonProperty(value = "destination_city")
    private Integer destinationCity;
    @JsonProperty(value = "destination_city_to")
    private Integer destinationCityTo;
    @JsonProperty("from_cities")
    private String fromCities;
    @JsonProperty("to_cities")
    private String toCities;
    @JsonProperty("attendee_list")
    private String attendeeList;

    private String address;

    private String route;
    @JsonProperty("flight_number")
    private String flightNumber;

    private Integer mileage;
    @JsonProperty("mileage_rate")
    private BigDecimal mileageRate;
    @JsonProperty("attendee_number")
    private Integer attendeeNumber;
    @JsonProperty("business_purpose")
    private String businessPurpose;
    @JsonProperty("mobile_number")
    private String mobileNumber;

    private String duration;
    @JsonProperty("telephone_number")
    private String telephoneNumber;
    @JsonProperty("equipment_id")
    private String equipmentId;
    @JsonProperty("attachment_count")
    private Integer attachmentCount;
    @JsonProperty("product_name")
    private String productName;
    @JsonProperty("project_name")
    private String projectName;
    @JsonProperty("customer_name")
    private String customerName;
    @JsonProperty("supplier_id")
    private Integer supplierId;
    @JsonProperty("supplier_name")
    private String supplierName;
    @JsonProperty("cost_center_id")
    private Integer costCenterId;
    @JsonProperty("cost_center_code")
    private String costCenterCode;
    @JsonProperty("dr_account_id")
    private Integer drAccountId;
    @JsonProperty("cr_account_id")
    private Integer crAccountId;
    @JsonProperty("tax_account_id")
    private Integer taxAccountId;
    @JsonProperty("tax_cr_account_id")
    private Integer taxCrAccountId;
    @JsonProperty("dr_account_id2")
    private Integer drAccountId2;
    @JsonProperty("cr_account_id2")
    private Integer crAccountId2;
    @JsonProperty("tax_account_id2")
    private Integer taxAccountId2;
    @JsonProperty("tax_cr_account_id2")
    private Integer taxCrAccountId2;
    @JsonProperty("budget_id")
    private Integer budgetId;

    private String recharge;
    @JsonProperty("invoice_serial")
    private String invoiceSerial;
    @JsonProperty("invoice_num")
    private String invoiceNum;
    @JsonProperty("invoice_code")
    private String invoiceCode;
    @JsonProperty("invoice_type")
    private String invoiceType;

    private Integer cid;
    @JsonProperty("budget_trx_id")
    private Integer budgetTrxId;
    @JsonProperty("trv_detail")
    private String trvDetail;
    @JsonProperty("trv_ref_id")
    private Integer trvRefId;

    @JsonProperty("column_json")
    private String columnJson;
    @JsonProperty("order_status")
    private Byte orderStatus;
    @JsonProperty("created_by")
    private Integer createdBy;
    @JsonProperty("creation_date")
    private Date creationDate;
    @JsonProperty("last_updated_by")
    private Integer lastUpdatedBy;
    @JsonProperty("last_update_date")
    private Date lastUpdateDate;
    @JsonProperty("version_id")
    private Integer versionId;
    @JsonProperty("budget_period")
    private Integer budgetPeriod;
    @JsonProperty("long_comments")
    private String longComments;


    @JsonProperty("accumulation_type_id")
    private Integer accumulationTypeId;

    @ApiModelProperty(name = "cashflow_item_id", value = "资金计划id")
    @JsonProperty("cashflow_item_id")
    private Integer cashflowItemId;

    @JsonProperty("customer_account_id")
    private Integer customerAccountId;

    @JsonProperty("pay_user_id")
    private Integer payUser;

    @ApiModelProperty(name = "gender", value = "性别")
    @JsonProperty("gender")
    private String gender;

    @ApiModelProperty(name = "nationality", value = "国籍")
    @JsonProperty("nationality")
    private String nationality;

    @JsonProperty("user_account_id")
    private Integer userAccountId;

    @JsonProperty("supplier_account_id")
    private Integer supplierAccountId;

    @JsonProperty("unit_id")
    private Integer unitId;

    @JsonProperty("material_id")
    private Integer materialId;

    @JsonProperty("item_num")
    private Integer itemNum;

    @JsonProperty("material_name")
    private String materialName;

    @JsonProperty("order_complete")
    private String orderComplete;
    @JsonProperty("claim_complete")
    private String claimComplete;
    @JsonProperty("invoice_complete")
    private String invoiceComplete;

    @JsonProperty("product_id")
    private Integer productId;
    @JsonProperty("product_abbreviation")
    private String productAbbreviation;
    @JsonProperty("product_specification")
    private String productSpecification;
    @JsonProperty("net_price")
    private BigDecimal netPrice;
    @JsonProperty("payment_terms")
    private String paymentTerms;
    @JsonProperty("item_index")
    private Integer itemIndex;
    @JsonProperty("accumulation_claim_amount")
    private BigDecimal accumulationClaimAmount;
    @JsonProperty("price_unit")
    private BigDecimal priceUnit;
    @JsonProperty("actual_quantity")
    private BigDecimal actualQuantity;
    @JsonProperty("fl_order_quantity")
    private BigDecimal flOrderQuantity;
    @JsonProperty("rm_order_quantity")
    private BigDecimal rmOrderQuantity;
    @JsonProperty("fl_receive_quantity")
    private BigDecimal flReceiveQuantity;
    @JsonProperty("rm_receive_quantity")
    private BigDecimal rmReceiveQuantity;
    @JsonProperty("rm_invoice_quantity")
    private BigDecimal rmInvoiceQuantity;
    @JsonProperty("fl_invoice_quantity")
    private BigDecimal flInvoiceQuantity;
    @JsonProperty("fl_invoice_amount")
    private BigDecimal flInvoiceAmount;
    @JsonProperty("rm_invoice_amount")
    private BigDecimal rmInvoiceAmount;
    @JsonProperty("fl_claim_amount")
    private BigDecimal flClaimAmount;
    @JsonProperty("rm_claim_amount")
    private BigDecimal rmClaimAmount;
    @JsonProperty("pay_currency")
    private String payCurrency;

}