package com.cloudpense.common.constants;

/**
 * 类功能描述
 *
 * <AUTHOR>
 * @Date 2023/10/12
 */
public class ApiPathConstant {

    /** 预算同步接口 **/
    public static final String BUDGET_SYNC = "common/budgets/v2/batch";

    /** 预算详情查询 common/budgets/findDetails **/

    public static final String BUDGET_DETAILS_FIND_V2 = "common/budgets/v2/findDetails";

    /** 安全 */
    // 获取token
    public static final String GET_TOKEN = "/common/unAuth/tokens/get";

    /** 部门 */
    // 部门批量更新
    public static final String DEPARTMENT_BATCH = "/common/departments/v2/batch";
    // 部门编码单笔查询
    public static final String DEPARTMENT_FIND_BY_CODE = "/common/departments/v2/findByCode";
    // 部门编码列表查询
    public static final String DEPARTMENT_FIND_BY_CODES = "/common/departments/v2/findByCodes";
    // 部门分页查询
    public static final String DEPARTMENT_FIND_BY_PAGE = "/common/departments/v2/findByPage";

    /** 部门绑定 */
    // 部门绑定批量更新
    public static final String DEPARTMENT_BINDING_BATCH = "/common/departmentbindings/v2/batch";

    /** 公司银行 */
    // 公司付款账号批量更新
    public static final String COMPANY_ACCOUNT_PAY_BATCH = "/common/companyAccounts/v2/batch";
    // 公司收款账号批量更新
    public static final String COMPANY_ACCOUNT_RCV_BATCH = "/common/companyDeliveryAccounts/v2/batch";
    // 公司账号分页查询
    public static final String COMPANY_ACCOUNT_FIND_BY_PAGE = "/common/companyDeliveryAccounts/v2/batch";

    /** 职位 */
    // 职位批量更新
    public static final String POSITION_BATCH = "/common/positions/batch";
    // 职位编码单笔查询
    public static final String POSITION_FIND_BY_CODE = "/common/positions/v2/findByCode";
    // 职位编码列表查询
    public static final String POSITION_FIND_BY_CODES = "/common/positions/v2/findByCodes";
    // 职位分页查询
    public static final String POSITION_FIND_BY_PAGE = "/common/positions/findByPage";
    // 职位分页查询V2
    public static final String POSITION_FIND_BY_PAGE_V2 = "/common/positions/v2/findByPage";

    /** 职级 */
    // 职级批量更新
    public static final String LEVEL_BATCH = "/common/levels/batch";

    /** 用户 */
    // 用户批量更新
    public static final String USER_BATCH = "/common/users/v2/batch";
    // 用户编码单笔查询
    public static final String USER_FIND_BY_CODE = "/common/users/v2/findByCode";
    // 用户名单笔查询
    public static final String USER_FIND_BY_NAME = "/common/users/v2/findByName";
    // 用户编码列表查询
    public static final String USER_FIND_BY_CODES = "/common/users/v2/findByCodes";
    // 用户名列表查询
    public static final String USER_FIND_BY_NAMES = "/common/users/v2/findByNames";
    // 用户分页查询
    public static final String USER_FIND_BY_PAGE = "/common/users/findByPage";

    /** 用户绑定 */
    // 用户绑定批量更新
    public static final String USER_BINDING_BATCH = "/common/userbindings/v2/batch";

    /** 用户银行 */
    // 用户银行账号批量更新
    public static final String USER_ACCOUNT_BATCH = "/common/userAccounts/v2/batch";

    /** 用户证件 */
    // 用户证件批量更新
    public static final String USER_CARD_BATCH = "/common/userCard/v2/batch";

    /** 项目 */
    // 项目批量更新
    public static final String PROJECT_BATCH = "/common/projects/v2/batch";
    // 项目编码单笔查询
    public static final String PROJECT_FIND_BY_CODE = "/common/projects/v2/findByCode";
    // 项目编码列表查询
    public static final String PROJECT_FIND_BY_CODES = "/common/projects/v2/findByCodes";
    // 项目分页查询
    public static final String PROJECT_FIND_BY_PAGE = "/common/projects/findByPage";

    /** 供应商 */
    // 供应商批量更新
    public static final String SUPPLIER_BATCH = "/common/suppliers/v2/batch";
    // 供应商编码单笔查询
    public static final String SUPPLIER_FIND_BY_CODE = "/common/suppliers/v2/findByCode";
    // 供应商编码列表查询
    public static final String SUPPLIER_FIND_BY_CODES = "/common/suppliers/v2/findByCodes";
    // 供应商分页查询
    public static final String SUPPLIER_FIND_BY_PAGE = "/common/suppliers/findByPage";

    /** 供应商银行 */
    // 供应商账号批量更新
    public static final String SUPPLIER_ACCOUNT_BATCH = "/common/supplierAccounts/v2/batch";
    // 供应商账号分页查询
    public static final String SUPPLIER_ACCOUNT_FIND_BY_PAGE = "/common/supplierAccounts/v2/findByPage";

    /** 客户 */
    // 客户批量更新
    public static final String CUSTOMER_BATCH = "/common/customers/v2/batch";
    // 客户编码单笔查询
    public static final String CUSTOMER_FIND_BY_CODE = "/common/customers/v2/findByCode";
    // 客户编码列表查询
    public static final String CUSTOMER_FIND_BY_CODES = "/common/customers/v2/findByCodes";
    // 客户分页查询
    public static final String CUSTOMER_FIND_BY_PAGE = "/common/customers/findByPage";

    /** 客户银行 */
    // 客户账号批量更新
    public static final String CUSTOMER_ACCOUNT_BATCH = "/common/customerAccounts/v2/batch";
    // 客户账号分页查询
    public static final String CUSTOMER_ACCOUNT_FIND_BY_PAGE = "/common/customerAccounts/v2/batch";

    /** 审批流 */
    // 员工待办
    public static final String WORKFLOW_TODO_FIND_BY_PAGE = "/common/workflow/emp/v2";

    /** 凭证 */
    // 凭证回传
    public static final String VOUCHER_BACK = "/common/voucher/back";
    // 凭证查询
    public static final String VOUCHER_QUERY = "/common/voucher/";
    // 凭证更新
    public static final String VOUCHER_UPDATE = "/common/voucher/update";

    /** 单据 */
    // 单据分页查询
    public static final String CLAIM_FIND_BY_PAGE = "/plugin/claim";
    // 单据更新
    public static final String CLAIM_UPDATE = "/common/document/update";




    /** 单据查询 */
    // 单张单据详情查询
    public static final String DOCUMENT_UNIQUE = "/common/document/unique";

    /** 投递 */
    // 投递状态批量同步
    public static final String DELIVERY_BATCH_SYNC = "/common/deliveries/batch";

}
