package com.cloudpense.common.vo.delivery;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 投递状态批量同步请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class DeliveryBatchSyncReq {

    /**
     * 当前接口同步的唯一标识
     */
    @NotBlank(message = "bizId不能为空")
    @Size(max = 36, message = "bizId长度不能超过36个字符")
    private String bizId;

    /**
     * 当前接口同步的对应时间戳
     */
    @NotNull(message = "timestamp不能为空")
    private Long timestamp;

    /**
     * 当前接口同步的数据集合
     */
    @NotEmpty(message = "data不能为空")
    @Valid
    private List<DeliveryDataItem> data;
}