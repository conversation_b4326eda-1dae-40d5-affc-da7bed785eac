package com.cloudpense.common.vo.delivery;

import lombok.Data;

import java.util.List;

/**
 * 投递错误项DTO
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class DeliveryErrorItem {

    /**
     * 投递纸单号
     */
    private String code;

    /**
     * 错误信息列表
     */
    private List<String> messages;

    public DeliveryErrorItem() {
    }

    public DeliveryErrorItem(String code, List<String> messages) {
        this.code = code;
        this.messages = messages;
    }
}