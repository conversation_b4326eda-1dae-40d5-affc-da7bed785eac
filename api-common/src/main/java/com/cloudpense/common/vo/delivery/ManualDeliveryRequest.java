package com.cloudpense.common.vo.delivery;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 纸单手动创建请求参数
 *
 * <AUTHOR>
 * @Date 2025/01/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ManualDeliveryRequest {
    
    /**
     * 当前请求的唯一标识
     */
    private String bizId;
    
    /**
     * 当前请求的对应时间戳
     */
    private Long timestamp;
    
    /**
     * 请求数据
     */
    private ManualDeliveryData data;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ManualDeliveryData {
        
        /**
         * 批次号
         */
        private String code;
        
        /**
         * 单据号
         */
        private String document_num;
        
        /**
         * 外部系统单据号  
         */
        private String external_id;
        
        /**
         * 删除标识Y/N
         */
        private String delete_flag;
    }
}