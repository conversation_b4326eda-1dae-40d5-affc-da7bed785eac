package com.cloudpense.common.vo.delivery;

import lombok.Data;

import java.util.List;

/**
 * 投递状态批量同步响应数据DTO
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class DeliveryBatchSyncResData {

    /**
     * 更新成功的条数
     */
    private String updated;

    /**
     * 字段强校验不合格的数据
     */
    private List<DeliveryErrorItem> validatorErrors;

    /**
     * 字段弱校验不合格的数据，仍会做同步处理
     */
    private List<DeliveryErrorItem> warnings;

    /**
     * 数据持久化时出现错误的数据
     */
    private List<DeliveryErrorItem> dbErrors;
}