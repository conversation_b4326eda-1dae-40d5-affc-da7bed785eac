package com.cloudpense.common.vo.delivery;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 纸单手动创建响应参数
 *
 * <AUTHOR>
 * @Date 2025/01/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ManualDeliveryResponse {
    
    /**
     * 响应状态码
     */
    private Integer resCode;
    
    /**
     * 返回信息描述
     */
    private String resMsg;
    
    /**
     * 当前请求的唯一标识
     */
    private String bizId;
    
    /**
     * 返回数据
     */
    private ManualDeliveryResponseData data;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ManualDeliveryResponseData {
        
        /**
         * 校验失败信息
         */
        private List<String> validatorErrors;
    }
}