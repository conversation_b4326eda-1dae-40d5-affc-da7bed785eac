package com.cloudpense.common.vo.delivery;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 投递数据项DTO
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class DeliveryDataItem {

    /**
     * 投递纸单号
     */
    @NotBlank(message = "投递纸单号不能为空")
    @Size(max = 64, message = "投递纸单号长度不能超过64个字符")
    private String code;

    /**
     * 单据号
     */
    @NotBlank(message = "单据号不能为空")
    @Size(max = 64, message = "单据号长度不能超过64个字符")
    @JsonProperty("document_num")
    private String documentNum;

    /**
     * 纸单投递状态
     * delivered(已投递)/received(已收单)/rejected(已退单)
     */
    @NotBlank(message = "投递状态不能为空")
    @Size(max = 64, message = "投递状态长度不能超过64个字符")
    @JsonProperty("delivery_status")
    private String deliveryStatus;
}