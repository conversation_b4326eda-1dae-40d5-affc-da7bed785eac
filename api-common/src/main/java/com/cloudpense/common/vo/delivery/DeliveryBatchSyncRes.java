package com.cloudpense.common.vo.delivery;

import lombok.Data;

/**
 * 投递状态批量同步响应DTO
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
public class DeliveryBatchSyncRes {

    /**
     * 响应状态码
     */
    private Integer resCode;

    /**
     * 返回信息描述
     */
    private String resMsg;

    /**
     * 当前接口同步的唯一标识
     */
    private String bizId;

    /**
     * 当前接口同步的数据集合
     */
    private DeliveryBatchSyncResData data;

    public DeliveryBatchSyncRes() {
    }

    public DeliveryBatchSyncRes(Integer resCode, String resMsg, String bizId, DeliveryBatchSyncResData data) {
        this.resCode = resCode;
        this.resMsg = resMsg;
        this.bizId = bizId;
        this.data = data;
    }

    public static DeliveryBatchSyncRes success(String bizId, DeliveryBatchSyncResData data) {
        return new DeliveryBatchSyncRes(200000, "success", bizId, data);
    }

    public static DeliveryBatchSyncRes error(Integer resCode, String resMsg, String bizId) {
        return new DeliveryBatchSyncRes(resCode, resMsg, bizId, null);
    }
}