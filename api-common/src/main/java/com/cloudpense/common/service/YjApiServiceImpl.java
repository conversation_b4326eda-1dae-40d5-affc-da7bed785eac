package com.cloudpense.common.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cloudpense.common.config.YjApiConfig;
import com.cloudpense.common.constants.ApiPathConstant;
import com.cloudpense.common.util.YjUtil;
import com.cloudpense.common.vo.ServiceRes;
import com.cloudpense.common.vo.cproxy.CProxyRes;
import com.cloudpense.common.vo.yj.GetTokenReq;
import com.cloudpense.common.vo.yj.GetTokenRes;
import com.cloudpense.common.vo.yj.YjRes;
//import com.cmonitor.api.aop.annotation.ApiLog;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * 云简openapi服务实现
 *
 * <AUTHOR>
 * @Date 2022/1/13
 */
@Service
@Slf4j
public class YjApiServiceImpl implements YjApiService {

    /**缓存token*/
    private TokenCache  tokenCache = new TokenCache();

    @Value("${yj.company-id}")
    String companyId;

    @Resource
    YjApiConfig yjApiConfig;
    @Resource
    RestTemplate restTemplate;

    @Override
    @ApiOperation("获取云简token")
    public ServiceRes token() {
        long currentTime = System.currentTimeMillis();
        Long  expireTime = Optional.ofNullable(tokenCache.getExpire()).orElse(Long.MIN_VALUE);
        if(expireTime > currentTime) {
            log.info("获取云简token缓存仍有效：expireTime={}", DateUtil.date(expireTime));
            return ServiceRes.success(tokenCache.getValue());
        }
        GetTokenReq tokenReq = GetTokenReq.builder()
                .client_id(yjApiConfig.getClientId())
                .client_secret(yjApiConfig.getClientSecret())
                .build();
        String url = yjApiConfig.getDomainUrl() + ApiPathConstant.GET_TOKEN + "?" + YjUtil.getUrlParameter(tokenReq);
        try {
            String resStr = restTemplate.getForObject(url, String.class);
            YjRes yjRes = JSONUtil.toBean(resStr, YjRes.class);
            if(!YjRes.isSuccess(yjRes)) {
                return ServiceRes.failure("获取云简token失败：" + yjRes.getResMsg());
            }
            GetTokenRes tokenRes = JSONUtil.toBean((JSONObject) yjRes.getData(), GetTokenRes.class);
            tokenCache.setValue(tokenRes.getAccess_token());
            tokenCache.setExpire(currentTime + tokenRes.getExpires_in() * 900);// 90%误差防御
        } catch (Exception e) {
            log.error("获取云简token异常：url={}", url, e);
            return ServiceRes.failure("获取云简token异常：" + e.getMessage());
        }

        return ServiceRes.success(tokenCache.getValue());
    }

    @Override
    @ApiOperation("调用云简API")
    public ServiceRes api(String path, HttpMethod httpMethod, String req, Class beanClass) {
        try {
            String url = yjApiConfig.getDomainUrl() + path;
            HttpHeaders headers = new HttpHeaders();
            headers.add("company_id",companyId);
            headers.add("Content-Type", ContentType.APPLICATION_JSON.toString());
            ServiceRes tokenRes = token();
            if(!ServiceRes.isSuccess(tokenRes)) {
                return ServiceRes.failure(tokenRes.getMsg());
            }
            headers.add("access_token", tokenRes.getData().toString());
            HttpEntity entity = new HttpEntity<>(req, headers);
            log.info("调用云简openapi请求：url={}，req={}", url, req);
            String resStr = restTemplate.exchange(url, httpMethod, entity, String.class).getBody();
            log.info("调用云简openapi响应：res={}", resStr);
            Object data;
            if(path.startsWith("/cproxy")) {
                CProxyRes cProxyRes = JSONUtil.toBean(resStr, CProxyRes.class);
                if(!CProxyRes.isSuccess(cProxyRes)) {
                    return ServiceRes.failure("调用云简openapi失败：" + cProxyRes.getMsg());
                }
                data = cProxyRes.getData();
            } else {
                YjRes yjRes = JSONUtil.toBean(resStr, YjRes.class);
                if(!YjRes.isSuccess(yjRes)) {
                    ServiceRes serviceRes = new ServiceRes<>();
                    serviceRes.setCode(String.valueOf(yjRes.getResCode()));
                    serviceRes.setMsg("调用云简openapi失败：" + yjRes.getResMsg());
                    return serviceRes;
                }
                data = yjRes.getData();
            }
//            log.info(data.toString());
            if(data instanceof JSONArray) {
                return ServiceRes.success(JSONUtil.toList((JSONArray) data, beanClass));
            } else if(data instanceof JSONObject){
                return ServiceRes.success(JSONUtil.toBean((JSONObject) data, beanClass));
            } else {
                return ServiceRes.success(data);
            }
        } catch (Exception e) {
            log.error("调用云简openapi异常：{}", e.getMessage(), e);
            return ServiceRes.failure("调用云简openapi异常：" + e.getMessage());
        }
    }

    @Override
    @ApiOperation("调用云简API")
    public ServiceRes api1(String path, HttpMethod httpMethod, String req, Class beanClass) {
        try {
            String url = yjApiConfig.getDomainUrl() + path;
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", ContentType.APPLICATION_JSON.toString());
            ServiceRes tokenRes = token();
            if(!ServiceRes.isSuccess(tokenRes)) {
                return ServiceRes.failure(tokenRes.getMsg());
            }
            headers.add("access_token", tokenRes.getData().toString());
            HttpEntity entity = new HttpEntity<>(req, headers);
            log.info("调用云简openapi请求：url={}，req={}", url, req);
            String resStr = restTemplate.exchange(url, httpMethod, entity, String.class).getBody();
            log.info("调用云简openapi响应：res={}", resStr);
            Object data;
            if(path.startsWith("/cproxy")) {
                CProxyRes cProxyRes = JSONUtil.toBean(resStr, CProxyRes.class);
                if(!CProxyRes.isSuccess(cProxyRes)) {
                    return ServiceRes.failure("调用云简openapi失败：" + cProxyRes.getMsg());
                }
                data = cProxyRes.getData();
            } else {
                YjRes yjRes = JSONUtil.toBean(resStr, YjRes.class);
                if(!YjRes.isSuccess(yjRes)) {
                    return ServiceRes.failure("调用云简openapi失败：" + yjRes.getResMsg());
                }
                data = yjRes.getData();
            }
             return ServiceRes.success(data);
//            if(data instanceof JSONArray) {
//                return ServiceRes.success(JSONUtil.toList((JSONArray) data, beanClass));
//            } else if(data instanceof JSONObject){
//                return ServiceRes.success(JSONUtil.toBean((JSONObject) data, beanClass));
//            } else {
//                return ServiceRes.success(data);
//            }
        } catch (Exception e) {
            log.error("调用云简openapi异常：{}", e.getMessage(), e);
            return ServiceRes.failure("调用云简openapi异常：" + e.getMessage());
        }
    }

    // ... existing code ...
    @Override
    @ApiOperation("调用云简API，支持自定义请求头")
    public ServiceRes api(String path, HttpMethod httpMethod, String req, Map<String, String> customHeaders, Class beanClass) {
        try {
            String url = yjApiConfig.getDomainUrl() + path;
            HttpHeaders headers = new HttpHeaders();
            headers.add("company_id", companyId);
            headers.add("Content-Type", ContentType.APPLICATION_JSON.toString());
            
            // 添加自定义请求头
            if (customHeaders != null && !customHeaders.isEmpty()) {
                for (Map.Entry<String, String> entry : customHeaders.entrySet()) {
                    headers.add(entry.getKey(), entry.getValue());
                }
            }
            
            ServiceRes tokenRes = token();
            if(!ServiceRes.isSuccess(tokenRes)) {
                return ServiceRes.failure(tokenRes.getMsg());
            }
            headers.add("access_token", tokenRes.getData().toString());
            HttpEntity entity = new HttpEntity<>(req, headers);
            log.info("调用云简openapi请求：url={}，req={}，customHeaders={}", url, req, customHeaders);
            String resStr = restTemplate.exchange(url, httpMethod, entity, String.class).getBody();
            log.info("调用云简openapi响应：res={}", resStr);
            Object data;
            if(path.startsWith("/cproxy")) {
                CProxyRes cProxyRes = JSONUtil.toBean(resStr, CProxyRes.class);
                if(!CProxyRes.isSuccess(cProxyRes)) {
                    return ServiceRes.failure("调用云简openapi失败：" + cProxyRes.getMsg());
                }
                data = cProxyRes.getData();
            } else {
                YjRes yjRes = JSONUtil.toBean(resStr, YjRes.class);
                if(!YjRes.isSuccess(yjRes)) {
                    ServiceRes serviceRes = new ServiceRes<>();
                    serviceRes.setCode(String.valueOf(yjRes.getResCode()));
                    serviceRes.setMsg("调用云简openapi失败：" + yjRes.getResMsg());
                    return serviceRes;
                }
                data = yjRes.getData();
            }
            log.info(data.toString());
            if(data instanceof JSONArray) {
                return ServiceRes.success(JSONUtil.toList((JSONArray) data, beanClass));
            } else if(data instanceof JSONObject){
                return ServiceRes.success(JSONUtil.toBean((JSONObject) data, beanClass));
            } else {
                return ServiceRes.success(data);
            }
        } catch (Exception e) {
            log.error("调用云简openapi异常：{}", e.getMessage(), e);
            return ServiceRes.failure("调用云简openapi异常：" + e.getMessage());
        }
    }
// ... existing code ...
    /**
     * token数据
     */
    @Data
    @NoArgsConstructor
    public
    class TokenCache {
        String value;//token值
        Long  expire;//token失效时间
    }

}
