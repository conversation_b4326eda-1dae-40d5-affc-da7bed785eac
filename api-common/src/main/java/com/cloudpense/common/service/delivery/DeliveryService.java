package com.cloudpense.common.service.delivery;

import com.cloudpense.common.vo.delivery.DeliveryBatchSyncReq;
import com.cloudpense.common.vo.delivery.DeliveryBatchSyncRes;

/**
 * 投递服务接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface DeliveryService {

    /**
     * 投递状态批量同步
     * 在整批数据中若由于某些原因导致几条数据处理失败，失败数据不会被同步，其他数据仍正常同步
     * 单次数据量过大（超过200条）时会全部转为异步处理
     *
     * @param request 批量同步请求
     * @return 批量同步响应
     */
    DeliveryBatchSyncRes batchSync(DeliveryBatchSyncReq request);
}