package com.cloudpense.common.service.delivery;

import com.cloudpense.carlisle.enumeration.DeliveryStatusEnum;
import com.cloudpense.common.vo.delivery.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 投递服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Slf4j
@Service
public class DeliveryServiceImpl implements DeliveryService {

    private static final int ASYNC_THRESHOLD = 200;

    @Override
    public DeliveryBatchSyncRes batchSync(DeliveryBatchSyncReq request) {
        log.info("开始处理投递状态批量同步，bizId: {}, 数据量: {}", 
                request.getBizId(), request.getData().size());

        try {
            // 判断是否需要异步处理
            if (request.getData().size() > ASYNC_THRESHOLD) {
                log.info("数据量超过{}条，转为异步处理", ASYNC_THRESHOLD);
                return handleAsyncProcess(request);
            }

            // 同步处理
            return handleSyncProcess(request);

        } catch (Exception e) {
            log.error("投递状态批量同步处理异常，bizId: {}", request.getBizId(), e);
            return DeliveryBatchSyncRes.error(500000, "系统内部错误", request.getBizId());
        }
    }

    /**
     * 异步处理
     */
    private DeliveryBatchSyncRes handleAsyncProcess(DeliveryBatchSyncReq request) {
        // TODO: 实现异步处理逻辑，将任务提交到队列
        log.info("异步任务已提交，bizId: {}", request.getBizId());
        
        DeliveryBatchSyncResData resData = new DeliveryBatchSyncResData();
        resData.setUpdated("异步处理中，请稍后查询结果");
        resData.setValidatorErrors(new ArrayList<>());
        resData.setWarnings(new ArrayList<>());
        resData.setDbErrors(new ArrayList<>());

        return DeliveryBatchSyncRes.success(request.getBizId(), resData);
    }

    /**
     * 同步处理
     */
    private DeliveryBatchSyncRes handleSyncProcess(DeliveryBatchSyncReq request) {
        List<DeliveryErrorItem> validatorErrors = new ArrayList<>();
        List<DeliveryErrorItem> warnings = new ArrayList<>();
        List<DeliveryErrorItem> dbErrors = new ArrayList<>();
        
        // 存储成功处理的数据
        List<DeliveryDataItem> successItems = new ArrayList<>();

        // 遍历处理每个投递数据项
        for (DeliveryDataItem item : request.getData()) {
            try {
                // 1. 数据校验
                List<String> validationErrors = validateDeliveryItem(item);
                if (!CollectionUtils.isEmpty(validationErrors)) {
                    validatorErrors.add(new DeliveryErrorItem(item.getCode(), validationErrors));
                    continue;
                }

                // 2. 状态变更校验
                List<String> statusErrors = validateStatusChange(item);
                if (!CollectionUtils.isEmpty(statusErrors)) {
                    validatorErrors.add(new DeliveryErrorItem(item.getCode(), statusErrors));
                    continue;
                }

                // 3. 业务逻辑处理
                processDeliveryItem(item, warnings, dbErrors);
                
                if (dbErrors.stream().noneMatch(error -> error.getCode().equals(item.getCode()))) {
                    successItems.add(item);
                }

            } catch (Exception e) {
                log.error("处理投递数据项异常，code: {}", item.getCode(), e);
                dbErrors.add(new DeliveryErrorItem(item.getCode(), 
                        Collections.singletonList("数据处理异常: " + e.getMessage())));
            }
        }

        // 构建响应数据
        DeliveryBatchSyncResData resData = new DeliveryBatchSyncResData();
        resData.setUpdated("此次请求更新成功了" + successItems.size() + "条数据");
        resData.setValidatorErrors(validatorErrors);
        resData.setWarnings(warnings);
        resData.setDbErrors(dbErrors);

        return DeliveryBatchSyncRes.success(request.getBizId(), resData);
    }

    /**
     * 数据校验
     */
    private List<String> validateDeliveryItem(DeliveryDataItem item) {
        List<String> errors = new ArrayList<>();

        if (StringUtils.isEmpty(item.getCode())) {
            errors.add("投递纸单号不能为空");
        }

        if (StringUtils.isEmpty(item.getDocumentNum())) {
            errors.add("单据号不能为空");
        }

        if (StringUtils.isEmpty(item.getDeliveryStatus())) {
            errors.add("投递状态不能为空");
        } else {
            // 校验投递状态是否有效
            boolean validStatus = Arrays.stream(DeliveryStatusEnum.values())
                    .anyMatch(status -> status.getCode().equals(item.getDeliveryStatus()));
            if (!validStatus) {
                errors.add("无效的投递状态: " + item.getDeliveryStatus());
            }
        }

        return errors;
    }

    /**
     * 状态变更校验
     */
    private List<String> validateStatusChange(DeliveryDataItem item) {
        List<String> errors = new ArrayList<>();

        try {
            // TODO: 获取当前纸单的状态
            String currentStatus = getCurrentDeliveryStatus(item.getCode());
            
            if (StringUtils.hasText(currentStatus)) {
                if (!isValidStatusTransition(currentStatus, item.getDeliveryStatus())) {
                    errors.add("此纸单需要进行更新的投递状态不被允许，请检查纸单当前状态");
                }
            }

        } catch (Exception e) {
            log.error("获取当前投递状态异常，code: {}", item.getCode(), e);
            errors.add("无法获取当前投递状态");
        }

        return errors;
    }

    /**
     * 处理投递数据项
     */
    private void processDeliveryItem(DeliveryDataItem item, 
                                   List<DeliveryErrorItem> warnings, 
                                   List<DeliveryErrorItem> dbErrors) {
        try {
            // 1. 更新纸单投递状态
            updateDeliveryStatus(item);

            // 2. 如果状态为received，生成索引号
            if ("received".equals(item.getDeliveryStatus())) {
                generateIndexNumber(item);
            }

            // 3. 更新单据头状态
            updateDocumentHeaderStatus(item);

        } catch (Exception e) {
            log.error("处理投递数据项失败，code: {}", item.getCode(), e);
            dbErrors.add(new DeliveryErrorItem(item.getCode(), 
                    Collections.singletonList("数据库操作失败: " + e.getMessage())));
        }
    }

    /**
     * 获取当前投递状态
     */
    private String getCurrentDeliveryStatus(String code) {
        // TODO: 实现从数据库获取当前投递状态的逻辑
        return null;
    }

    /**
     * 验证状态转换是否合法
     */
    private boolean isValidStatusTransition(String currentStatus, String targetStatus) {
        if (currentStatus.equals(targetStatus)) {
            return true;
        }

        Integer currentSequence = DeliveryStatusEnum.getSequenceNum(currentStatus);
        Integer targetSequence = DeliveryStatusEnum.getSequenceNum(targetStatus);

        if (currentSequence == null || targetSequence == null) {
            return false;
        }

        // 只允许向前转换（sequence增加）或者同级别转换
        return targetSequence >= currentSequence;
    }

    /**
     * 更新投递状态
     */
    private void updateDeliveryStatus(DeliveryDataItem item) {
        // TODO: 实现更新投递状态的数据库操作
        log.info("更新投递状态，code: {}, status: {}", item.getCode(), item.getDeliveryStatus());
    }

    /**
     * 生成索引号
     */
    private void generateIndexNumber(DeliveryDataItem item) {
        // TODO: 实现生成索引号的逻辑
        log.info("为received状态的纸单生成索引号，code: {}", item.getCode());
    }

    /**
     * 更新单据头状态
     */
    private void updateDocumentHeaderStatus(DeliveryDataItem item) {
        // TODO: 实现单据头状态更新逻辑
        // 若单据与纸单单张关联，同步纸单投递状态时会同步更新单据头上delivery_status字段
        // 若单据与多张纸单关联，当且仅当单据关联的其余全部纸单均为请求中纸单投递状态时，同步更新单据头上delivery_status字段，否则单据头上delivery_status字段为空
        log.info("更新单据头状态，documentNum: {}, deliveryStatus: {}", 
                item.getDocumentNum(), item.getDeliveryStatus());
    }
}