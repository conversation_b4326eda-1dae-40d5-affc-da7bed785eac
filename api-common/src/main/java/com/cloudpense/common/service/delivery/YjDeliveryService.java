package com.cloudpense.common.service.delivery;

import com.cloudpense.common.vo.ServiceRes;

/**
 * 纸单相关服务接口
 *
 * <AUTHOR>
 * @Date 2025/01/24
 */
public interface YjDeliveryService {
    
    /**
     * 纸单手动创建
     * 
     * @param documentNum 单据号
     * @return ServiceRes
     */
    ServiceRes createManualDelivery(String documentNum);
    
    /**
     * 纸单手动创建（指定批次号）
     * 
     * @param code 批次号
     * @param documentNum 单据号
     * @return ServiceRes
     */
    ServiceRes createManualDelivery(String code, String documentNum);
    
    /**
     * 纸单手动删除
     * 
     * @param code 批次号
     * @param documentNum 单据号
     * @return ServiceRes
     */
    ServiceRes deleteManualDelivery(String code, String documentNum);
}