package com.cloudpense.common.service.delivery;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.cloudpense.common.service.YjApiService;
import com.cloudpense.common.vo.ServiceRes;
import com.cloudpense.common.vo.delivery.ManualDeliveryRequest;
import com.cloudpense.common.vo.delivery.ManualDeliveryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 纸单相关服务实现
 *
 * <AUTHOR>
 * @Date 2025/01/24
 */
@Service
@Slf4j
@ConditionalOnExpression("'${yj.api-list}'.contains('delivery')")
public class YjDeliveryServiceImpl implements YjDeliveryService {

    private static final String MANUAL_DELIVERY_PATH = "/common/deliveries/manual";

    @Resource
    YjApiService yjApiService;

    @Override
    public ServiceRes createManualDelivery(String documentNum) {
        log.info("开始创建纸单，单据号: {}", documentNum);
        
        ManualDeliveryRequest.ManualDeliveryData data = ManualDeliveryRequest.ManualDeliveryData.builder()
                .document_num(documentNum)
                .delete_flag("Y")
                .code(documentNum)
                .build();
                
        ManualDeliveryRequest request = ManualDeliveryRequest.builder()
                .bizId(IdUtil.simpleUUID())
                .timestamp(System.currentTimeMillis())
                .data(data)
                .build();

        return callManualDeliveryApi(request);
    }

    @Override
    public ServiceRes createManualDelivery(String code, String documentNum) {
        log.info("开始创建纸单，批次号: {}, 单据号: {}", code, documentNum);
        
        ManualDeliveryRequest.ManualDeliveryData data = ManualDeliveryRequest.ManualDeliveryData.builder()
                .code(code)
                .document_num(documentNum)
                .build();
                
        ManualDeliveryRequest request = ManualDeliveryRequest.builder()
                .bizId(IdUtil.simpleUUID())
                .timestamp(System.currentTimeMillis())
                .data(data)
                .build();

        return callManualDeliveryApi(request);
    }

    @Override
    public ServiceRes deleteManualDelivery(String code, String documentNum) {
        log.info("开始删除纸单，批次号: {}, 单据号: {}", code, documentNum);
        
        ManualDeliveryRequest.ManualDeliveryData data = ManualDeliveryRequest.ManualDeliveryData.builder()
                .code(code)
                .document_num(documentNum)
                .delete_flag("Y")
                .build();
                
        ManualDeliveryRequest request = ManualDeliveryRequest.builder()
                .bizId(IdUtil.simpleUUID())
                .timestamp(System.currentTimeMillis())
                .data(data)
                .build();

        return callManualDeliveryApi(request);
    }

    /**
     * 调用纸单手动创建API
     */
    private ServiceRes callManualDeliveryApi(ManualDeliveryRequest request) {
        try {
            String reqJson = JSONUtil.toJsonStr(request);
            log.info("调用纸单手动创建API，请求参数: {}", reqJson);
            
            ServiceRes serviceRes = yjApiService.api(MANUAL_DELIVERY_PATH, HttpMethod.POST, reqJson, ManualDeliveryResponse.class);
            
            if (ServiceRes.isSuccess(serviceRes)) {
                log.info("纸单创建成功，响应: {}", JSONUtil.toJsonStr(serviceRes.getData()));
                return serviceRes;
            } else {
                log.error("纸单创建失败，错误信息: {}", serviceRes.getMsg());
                return ServiceRes.failure("纸单创建失败: " + serviceRes.getMsg());
            }
            
        } catch (Exception e) {
            log.error("调用纸单手动创建API异常", e);
            return ServiceRes.failure("调用纸单手动创建API异常: " + e.getMessage());
        }
    }
}