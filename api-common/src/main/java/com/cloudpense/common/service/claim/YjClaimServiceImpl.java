package com.cloudpense.common.service.claim;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cloudpense.common.constants.ApiPathConstant;
import com.cloudpense.common.service.YjApiService;
import com.cloudpense.common.util.YjUtil;
import com.cloudpense.common.vo.ServiceRes;
import com.cloudpense.common.vo.yj.YjPageInfo;
import com.cloudpense.common.vo.yj.claim.*;
import com.cloudpense.standard.dao.entity.ExpClaimHeader;
import com.cloudpense.standard.request.BaseRequest;
import com.cloudpense.standard.request.DynamicDocumentQueryCondition;
import com.cloudpense.standard.request.GeneratedCriteria;
import com.cloudpense.standard.request.HeaderTypeQueryCondition;
import com.cloudpense.standard.request.document.CriteriaCondition;
import com.cloudpense.standard.vo.claim.ClaimAttachmentVo;
import com.cloudpense.standard.vo.claim.ClaimVo;
import com.cloudpense.standard.vo.claim.ExpClaimHeaderCommonVo;
import com.cloudpense.standard.vo.claim.ExpClaimHeaderVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 单据相关服务实现
 *
 * <AUTHOR>
 * @Date 2022/1/15
 */
@Service
@Slf4j
@ConditionalOnExpression("'${yj.api-list}'.contains('claim')")
public class YjClaimServiceImpl implements YjClaimService {

    private static final String FIND_CLAIM_LIST         = "/cproxy/claim/findListByCondition";
    private static final String UPDATE_CLAIM            = "/cproxy/claim/updateExpClaimHeader";
    private static final String DOCUMENT_UPDATE            = "/common/document/update";
    private static final String UPDATE_CLAIM_ATTACHMENT = "/cproxy/claim/updateClaimAttachment";
    private static final String QUERY_HEADER_TYPE       = "/cproxy/claim/queryHeaderTypeByCondition";
    // plugin/claim/kangfangGetClaim

    private static final String KANGFANG_GET_CLAIM = "/plugin/claim/kangfangGetClaim";

    @Resource
    YjApiService yjApiService;
    @Resource
    ObjectMapper objectMapper;

    static ObjectMapper om = new ObjectMapper();
    static {
        om.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false);
    }

    @Override
    public JSONObject queryByPage(Integer pageNum, Integer pageSize, JSONObject condition, String code) {
//        String path = "/plugin/claim/" + code;
//        condition.set("page_num", pageNum).set("page_size", pageSize);
//        Object res = yjApiService.api(path, HttpMethod.POST, JSONUtil.toJsonStr(condition));
//        return (JSONObject)res;
        return null;
    }

    @Override
    public ServiceRes createClaim(ExpClaimHeaderVo header) {
        String path = "/common/document";
        BaseRequest<ClaimVo> baseRequest = new BaseRequest<>();
        ClaimVo claimVo = new ClaimVo();
        claimVo.setExpClaimHeaderVo(header);
        baseRequest.setBizId(IdUtil.simpleUUID());
        baseRequest.setTimestamp(System.currentTimeMillis());
        log.info("生成单据参数:"+header);
        baseRequest.setData(claimVo);
        log.info("核报单请求参数：{}", header);
        String req;
        try {
            req = objectMapper.writeValueAsString(baseRequest);
        } catch (JsonProcessingException e) {
            return ServiceRes.failure("参数错误：" + e.getMessage());
        }
       Object res = yjApiService.api(path, HttpMethod.POST, req,null);

        if(res == null) {
            return ServiceRes.failure("调用标口创建单据失败");
        }

        return ServiceRes.success(res);
//        return null;
    }

    @Override
    public ExpClaimHeaderCommonVo findOne(String documentNum, String jsonText) throws Exception {
        // 查询目标单据
        CriteriaCondition condition = new CriteriaCondition();
        DynamicDocumentQueryCondition fields = JSONUtil.toBean(jsonText, DynamicDocumentQueryCondition.class);
        condition.setFields(fields);
        condition.setPageSize(1);
        GeneratedCriteria criteria = new GeneratedCriteria();
        criteria.addCriterion("ech", "document_num", documentNum, "=");
        criteria.addCriterion("ech", "status", "deleted", "<>");
        condition.setCriteriaList(Collections.singletonList(criteria));
        condition.setPageNum(1);
        condition.setPageNum(1);
        List<ExpClaimHeaderCommonVo> result = findListByCondition(condition);

        return CollUtil.isEmpty(result) ? null : result.get(0);
    }

    @Override
    public ExpClaimHeaderCommonVo findById(Integer headerId, String jsonText) throws Exception {
        // 查询目标单据
        CriteriaCondition condition = new CriteriaCondition();
        DynamicDocumentQueryCondition fields = JSONUtil.toBean(jsonText, DynamicDocumentQueryCondition.class);
        condition.setFields(fields);
        condition.setPageSize(1);
        GeneratedCriteria criteria = new GeneratedCriteria();
        criteria.addCriterion("ech", "header_id", headerId, "=");
        criteria.addCriterion("ech", "status", "deleted", "<>");
        condition.setCriteriaList(Collections.singletonList(criteria));
        condition.setPageNum(1);
        condition.setPageNum(1);
        List<ExpClaimHeaderCommonVo> result = findListByCondition(condition);

        return CollUtil.isEmpty(result) ? null : result.get(0);
    }

    @Override
    public List<ExpClaimHeaderCommonVo> findListByCondition(CriteriaCondition condition) throws Exception {
        // 查询目标单据
//        String reqStr = objectMapper.writeValueAsString(condition);
////        Object resObj = yjApiService.api(FIND_CLAIM_LIST, HttpMethod.POST, reqStr);
////        List<ExpClaimHeaderCommonVo> result = new ArrayList<>();
////        if(resObj != null && resObj instanceof JSONArray) {
////            JSONArray jsonArray = (JSONArray)resObj;
////            for(int i = 0; i < jsonArray.size(); i++) {
////                ExpClaimHeaderCommonVo claimVo = objectMapper.readValue(jsonArray.getJSONObject(i).toString(), ExpClaimHeaderCommonVo.class);
////                result.add(claimVo);
////            }
////        }
////
////        return result;
        return null;
    }

    @Override
    public ServiceRes updateClaimHeader(ExpClaimHeader claimHeader) throws JsonProcessingException {

        return yjApiService.api(UPDATE_CLAIM, HttpMethod.POST, objectMapper.writeValueAsString(claimHeader),null);
//        return null;
    }

    @Override
    public ServiceRes updateClaimHeader(String jsonText) {
        return yjApiService.api(UPDATE_CLAIM, HttpMethod.POST, jsonText,null);
    }

    @Override
    public ServiceRes updateClaimAttachment(ClaimAttachmentVo attachmentVo) throws Exception {
//        return yjApiService.api(UPDATE_CLAIM_ATTACHMENT, HttpMethod.POST, objectMapper.writeValueAsString(attachmentVo));
        return null;
    }

    @Override
    public ServiceRes queryHeaderTypeByCondition(HeaderTypeQueryCondition condition) throws Exception {
//        return yjApiService.api(QUERY_HEADER_TYPE, HttpMethod.POST, objectMapper.writeValueAsString(condition));
        return null;
    }

    @Override
    public ClaimFindByPageRes findByPage(ClaimFindByPageReq req, String code) {
        ClaimFindByPageRes res = new ClaimFindByPageRes();
        res.setClaims(new ArrayList<>());
        try {
            if(req.getEnd_datetime() == null) {
                req.setEnd_datetime(System.currentTimeMillis());
            }
            if(req.getStart_datetime() == null) {
                req.setStart_datetime(req.getEnd_datetime() - 1 * 365 * 24 * 60 * 60 * 1000);
            }
            if(req.getDate_field_name() == null) {
                req.setDate_field_name("last_update_date");
            }
            String path = "https://taliopenapi.cloudpense.com"+ApiPathConstant.CLAIM_FIND_BY_PAGE + "/" + code;

            ServiceRes apiRes = yjApiService.api(path, HttpMethod.POST, JSONUtil.toJsonStr(req), YjPageInfo.class);
            if(!ServiceRes.isSuccess(apiRes)) {
                return res;
            }
            YjPageInfo yjPageInfo = (YjPageInfo)apiRes.getData();
            if(yjPageInfo == null) {
                return res;
            }
            JSONArray pageInfo = (JSONArray) yjPageInfo.getPage_info();
            if(pageInfo == null) {
                return res;
            }
            List<ExpClaimHeaderCommonVo> claims = om.readValue(JSONUtil.toJsonStr(pageInfo), new TypeReference<List<ExpClaimHeaderCommonVo>>(){});
            res.setClaims(claims);
        } catch (Exception e) {
            log.error("单据分页查询异常：", e);
        }
        return res;
    }

    @Override
    public ClaimUpdateRes updateDocument(ClaimUpdateReq req, String externalId) {
        JSONObject param = new JSONObject();
        param.putOpt("bizId", IdUtil.simpleUUID());
        param.putOpt("timestamp", System.currentTimeMillis());
        param.putOpt("data", req);
        String reqStr = JSONUtil.toJsonStr(param);
        String path = ApiPathConstant.CLAIM_UPDATE + "/" + externalId;
        ServiceRes apiRes = yjApiService.api(path, HttpMethod.POST, reqStr, String.class);
        if(!ServiceRes.isSuccess(apiRes)) {
            return new ClaimUpdateRes();
        }

        return new ClaimUpdateRes();
    }
    @Override
    public ClaimUpdateRes updateDocument(Object jsonText, String externalId) {
        JSONObject param = new JSONObject();
        param.putOpt("bizId", IdUtil.simpleUUID());
        param.putOpt("timestamp", System.currentTimeMillis());
        param.putOpt("data", jsonText);
        String reqStr = JSONUtil.toJsonStr(param);
        String path = ApiPathConstant.CLAIM_UPDATE + "/" + externalId;
        ServiceRes apiRes = yjApiService.api(path, HttpMethod.POST, reqStr, String.class);
        if(!ServiceRes.isSuccess(apiRes)) {
            return new ClaimUpdateRes();
        }

        return new ClaimUpdateRes();
    }


    @Override
    public ClaimUniqueRes findClaimByCode(com.alibaba.fastjson.JSONObject req) {
        String path = ApiPathConstant.DOCUMENT_UNIQUE + "?" + YjUtil.getUrlParameter(req);
        ServiceRes res = yjApiService.api(path, HttpMethod.GET, null, ClaimUniqueRes.class);
        if(!ServiceRes.isSuccess(res)) {
            return null;
        }
        return com.alibaba.fastjson.JSONObject.parseObject(com.alibaba.fastjson.JSONObject.toJSONString(res.getData()), ClaimUniqueRes.class);
    }

    @Override
    public ClaimUniqueRes findDocumentByExternalId(String externalId, String locale) {
        try {
            // 构建请求路径
            String path = ApiPathConstant.DOCUMENT_UNIQUE + "?externalId=" + externalId;
            
            // 设置多语言请求头
            Map<String, String> headers = new HashMap<>();
            if (locale != null && !locale.trim().isEmpty()) {
                headers.put("locale", locale);
            } else {
                headers.put("locale", "zh_CN"); // 默认中文
            }
            
            log.info("调用单据详情查询接口，externalId: {}, locale: {}", externalId, locale);
            
            // 调用API
            ServiceRes res = yjApiService.api(path, HttpMethod.GET, null, headers, ClaimUniqueRes.class);
            
            if (!ServiceRes.isSuccess(res)) {
                log.warn("单据详情查询失败，externalId: {}, 错误信息: {}", externalId, res.getMsg());
                return null;
            }
            
            // 转换返回结果
            ClaimUniqueRes result = com.alibaba.fastjson.JSONObject.parseObject(
                com.alibaba.fastjson.JSONObject.toJSONString(res.getData()), 
                ClaimUniqueRes.class
            );
            
            log.info("单据详情查询成功，externalId: {}, 单据编码: {}", externalId, 
                result != null ? result.getCode() : "null");
                
            return result;
            
        } catch (Exception e) {
            log.error("单据详情查询异常，externalId: " + externalId, e);
            return null;
        }
    }

    @Override
    public ClaimUniqueRes findDocumentByExternalId(String externalId) {
        return findDocumentByExternalId(externalId, "zh_CN");
    }

    @Override
    public String kangfangGetClaim(KangfangGetClaimReq req) throws JsonProcessingException {

//        JSONObject param = new JSONObject();
//        param.putOpt("bizId", IdUtil.simpleUUID());
//        param.putOpt("timestamp", System.currentTimeMillis());
//        param.putOpt("data", objectMapper.writeValueAsString(req));


       ServiceRes res =  yjApiService.api(KANGFANG_GET_CLAIM, HttpMethod.POST,objectMapper.writeValueAsString(req), String.class);
       return JSONUtil.toJsonStr(res);
    }
}
