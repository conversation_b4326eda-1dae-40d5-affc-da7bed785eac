<?xml version="1.0" encoding="UTF-8"?>

<!--
Level orders:
  A log request of level p issued to a logger having an effective level q, is enabled if p >= q
  TRACE < DEBUG < INFO < WARN < ERROR
  e.g.
  log request is debug, logger level is INFO, the log request will not be processed.

IMPORTANT: 日志永久保留配置说明
  为满足财务日志永久保留的需求，已注释掉所有 MaxHistory 配置项。
  这意味着日志文件将不会被自动删除，需要手动管理磁盘空间。
  修改日期：2025-07-14
-->
<configuration>

    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径-->
    <springProperty scope="context" name="LOG_HOME" source="logging.file.path"/>
    <!--定义日志文件保留天数 - 已注释以实现永久保留财务日志-->
    <!--<springProperty scope="context" name="MAX_HISTORY" source="logging.file.max-history" defaultValue="30"/>-->
    <!-- 定义日志上下文的名称 -->
    <contextName>${LOG_CONTEXT_NAME}</contextName>
    <!-- 控制台输出 -->

    <springProfile name="test,dev">
    </springProfile>
    <!-- 生产环境. -->
<!--    【-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%X{requestId}] [%thread]  [%X{TIME}]  [%-5level] [%logger{5}] [%X{HEADER}] [%msg] %n</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <!--info日志统一输出到这里-->
    <appender name="file.info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--true表示不支持压缩-->
        <!--        <Prudent>true</Prudent>-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名 每小时生成日志文件 -->
            <FileNamePattern>${LOG_HOME}/%d{yyyy-MM-dd}/info.%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
            <!--日志文件保留天数 - 已注释以实现永久保留财务日志-->
            <!--<MaxHistory>${MAX_HISTORY}</MaxHistory>-->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!-- 除按日志记录之外，还配置了日志文件不能超过10M(默认)，若超过10M，日志文件会以索引0开始， -->
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %method 方法名  %L 行数 %msg：日志消息，%n是换行符-->
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%X{requestId}] [%thread]  [%X{TIME}]  [%-5level] [%logger{5}] [%X{HEADER}] [%msg] %n</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>
    <!--错误日志统一输出到这里-->
    <appender name="file.error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--true表示不支持压缩-->
        <!--        <Prudent>true</Prudent>-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}/%d{yyyy-MM-dd}/error.%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
            <!--日志文件保留天数 - 已注释以实现永久保留财务日志-->
            <!--<MaxHistory>${MAX_HISTORY}</MaxHistory>-->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!-- 除按日志记录之外，还配置了日志文件不能超过10M(默认)，若超过10M，日志文件会以索引0开始， -->
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %method 方法名  %L 行数 %msg：日志消息，%n是换行符-->
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%X{requestId}] [%thread]  [%X{TIME}]  [%-5level] [%logger{5}] [%X{HEADER}] [%msg] %n</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!-- 此日志文件只记录error级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!--warn日志统一输出到这里-->
    <appender name="file.warn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--true表示不支持压缩-->
        <!--        <Prudent>true</Prudent>-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名 按小时生成日志-->
            <FileNamePattern>${LOG_HOME}/%d{yyyy-MM-dd}/warn.%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
            <!--日志文件保留天数 - 已注释以实现永久保留财务日志-->
            <!--<MaxHistory>${MAX_HISTORY}</MaxHistory>-->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!-- 除按日志记录之外，还配置了日志文件不能超过10M(默认)，若超过10M，日志文件会以索引0开始， -->
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %method 方法名  %L 行数 %msg：日志消息，%n是换行符-->
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%X{requestId}] [%thread]  [%X{TIME}]  [%-5level] [%logger{5}] [%X{HEADER}] [%msg] %n</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!-- 此日志文件只记录warn级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!--debug级别日志统一输出到这里-->
    <appender name="file.debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--true表示不支持压缩-->
        <!--        <Prudent>true</Prudent>-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名 按小时生成日志-->
            <FileNamePattern>${LOG_HOME}/%d{yyyy-MM-dd}/debug.%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
            <!--日志文件保留天数 - 已注释以实现永久保留财务日志-->
            <!--<MaxHistory>${MAX_HISTORY}</MaxHistory>-->
            <!-- 除按日志记录之外，还配置了日志文件不能超过5M，若超过5M，日志文件会以索引0开始，命名日志文件，例如console-debug.2018-08-24-09.1.log -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %method 方法名  %L 行数 %msg：日志消息，%n是换行符-->
            <pattern>[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%X{requestId}] [%thread]  [%X{TIME}]  [%-5level] [%logger{5}] [%X{HEADER}] [%msg] %n</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!-- 此日志文件只记录debug级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY </onMismatch>
        </filter>
    </appender>

    <root level="INFO">
        <appender-ref ref="console"/>
        <appender-ref ref="file.error" />
        <appender-ref ref="file.info" />
        <appender-ref ref="file.warn" />
        <appender-ref ref="file.debug"/>
    </root>
</configuration>