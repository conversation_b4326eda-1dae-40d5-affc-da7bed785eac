package com.cloudpense.boot.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cloudpense.boot.service.OaTodoService;
import com.cloudpense.common.service.YjApiService;
import com.cloudpense.common.vo.ServiceRes;
import com.cloudpense.common.vo.todo.OaTodoDeletePushReq;
import com.cloudpense.common.vo.todo.OaTodoDonePushReq;
import com.cloudpense.common.vo.todo.OaTodoPushReq;
import com.cloudpense.common.vo.todo.OaTodoPushRes;
import com.cloudpense.standard.dao.entity.MqQueue;
import com.cloudpense.standard.vo.claim.ExpClaimHeaderCommonVo;
import com.cloudpense.standard.vo.claim.SimpleFndWorkflowPathVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URI;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/21
 */
@Service
@Slf4j
public class OaTodoServiceImpl implements OaTodoService {

    @Resource
    YjApiService yjApiService;
    @Resource
    RestTemplate restTemplate;

    @Value("${oa.todoUrl}")
    String todoUrl;
    @Value("${oa.todo}")
    String todo;
    @Value("${oa.todoDone}")
    String todoDone;
    @Value("${oa.deleteTodo}")
    String deleteTodo;
    @Value("${oa.redirectUrl}")
    String redirectUrl;
    /**
     * 2024-01-06需要提醒邮寄的报销单类型：
     * YXCL02 FSAB-差旅报销 需要
     * YXFY02 FSBB-费用报销 需要
     * YXDG01 FSHB-对公预付 需要
     * YXDG02 FSHC-对公应付 需要
     * YXJK01 FSJA-员工借款申请 需要
     *
     * YXZR02 FSFB-终端准入核报 需要
     * YXHY02 FSCD-会议核报 需要
     * ZRCL02 FMAB-差旅报销-准入 需要
     * ZRFY02 FMBB-费用报销-准入 需要
     * ZRHY02 FMCC-会议核报-准入 需要
     */
    List<String> reimbursementTypeCodes = Arrays.asList("YXCL02","YXFY02","YXDG01","YXDG02","YXJK01","YXZR02","YXHY02","ZRCL02","ZRFY02","ZRHY02");
    List<String> advanceAmountTypeCodes = Arrays.asList("YXDG01","YXJK01","YXJK03");
    List<String> totalPayAmountTypeCodes = Arrays.asList("YXCL02","YXFY02","YXDG02","YXZR02","YXHY02","ZRCL02","ZRFY02","ZRHY02");
    List<String> totalAmountTypeCodes = Arrays.asList("YXCL01","YXFY01","YXIIT01","YXIIT02","YXZR01","YXPT01","YXHY01","ZRCL01","ZRFY01","YXYS03","YXXC");


    @Override
    public ServiceRes todo(ExpClaimHeaderCommonVo claimVo, MqQueue mqQueue) {
        ServiceRes serviceRes = ServiceRes.success(null);
        if(CollUtil.isEmpty(claimVo.getWorkflowPathVos())) {
            serviceRes.setMsg("无审批流信息，忽略");
            return serviceRes;
        }
        // 获取推送目标用户:员工号、代办类型(审批、抄送)、操作类型(代办、已办、消除)
        List<User> users = getUser(claimVo, mqQueue);
        if(CollUtil.isEmpty(users)) {
            serviceRes.setMsg("无推送目标用户，忽略");
            return serviceRes;
        }
        log.info("{}待办推送oa目标：users={}", claimVo.getCode(), JSONUtil.toJsonStr(users));
        // 推送数据：虽然可以批量推送，但是代办、已办、代办消除是三个不同的接口，因此分别对每个目标用户进行推送
        Map<String, String> messages = new HashMap<>();//推送异常消息
        for (User user : users) {
            ServiceRes res = null;
            if (StrUtil.isEmpty(user.getCode())) {
                continue;//没有审批人信息，过滤掉
            }
            String message = "";
            if (TodoType.push.equals(user.getTodoType())) {
                OaTodoPushReq todeReq = getTodoPushData(claimVo, mqQueue, user);
                res = todoPush(todeReq);//代办推送
            }
            if (TodoType.done.equals(user.getTodoType())) {
                OaTodoDonePushReq toDoneReq = getTodonePushData(claimVo, mqQueue, user);
                res = todoDonePush(toDoneReq);//已办推送
            }
            if (TodoType.del.equals(user.getTodoType())) {
                OaTodoDeletePushReq todoDelReq = getTodoDelData(claimVo, mqQueue, user);
                res = todoDelPush(todoDelReq);//代办清除
            }
            if(!ServiceRes.isSuccess(res)) {
                message = StrUtil.format("推送失败：todoType={},pushType={},error={}",
                        user.getTodoType(), user.getPushType(), res.getMsg());
            } else {
                message = StrUtil.format("推送成功：todoType={},pushType={}",
                        user.getTodoType(), user.getPushType());
            }
            messages.put(user.getCode(), message);
        }

        serviceRes.setData(messages);
        return serviceRes;
    }

    /**
     * 封装代办推送入参
     * @param claimVo
     * @param mqQueue
     * @param user
     * @return
     */
    private OaTodoPushReq getTodoPushData(ExpClaimHeaderCommonVo claimVo, MqQueue mqQueue, User user) {
        OaTodoPushReq todo = new OaTodoPushReq();
        todo.setAppName("云简业财");
        todo.setModelName("云简业财");
        String modelId = claimVo.getCode();//单据-消息ID
        if (mqQueue.getPathId() != null) {
            modelId = modelId + "-" + mqQueue.getPathId();
        }
        modelId = modelId + "-" + user.getCode();//单据号-审批流节点ID-员工号
        todo.setModelId(modelId);
        String subject = getSubject(user, mqQueue, claimVo);//代办信息
        todo.setSubject(subject);

        String linkUrl = getUrl(user, mqQueue, claimVo);//跳转链接

        todo.setLink(linkUrl);
        todo.setMobileLink(linkUrl);
        todo.setPadLink(linkUrl);
        if (PushType.TODO.equals(user.getPushType())) {//审批类
            todo.setType("1");//审批
        } else {//抄送或通知
            todo.setType("2");//抄送或通知
        }
        JSONObject target = new JSONObject();
        target.put("PersonNo", user.getCode());
        todo.setTargets(JSONUtil.toJsonStr(target));//当前代办审批人
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        todo.setCreateTime(sdf.format(new Date()));//当前代办触发时间
        todo.setLevel("3");//优先级：默认传3
        return todo;
    }

    /**
     * 获取代办通知消息
     * @param user
     * @param mqQueue
     * @param claimVo
     * @return
     */
    private String getSubject(User user, MqQueue mqQueue, ExpClaimHeaderCommonVo claimVo) {
        StringBuilder sb = new StringBuilder();
        String eventCode = mqQueue.getEventCode();
        String fullName = claimVo.getSubmitUserVo().getFullName();
        String code = claimVo.getCode();
        String headerTypeCode = claimVo.getHeaderTypeVo().getCode();//单据类型编码
        String headerType = claimVo.getHeaderTypeVo().getType();//单据类型文本
        //#114529 不同单据类型，金额取值不一样
        BigDecimal amount = BigDecimal.ZERO;
        if (advanceAmountTypeCodes.contains(headerTypeCode)) {//金额字段取advance_amount
            amount = amount.add(claimVo.getAdvanceAmount());
        } else if (totalPayAmountTypeCodes.contains(headerTypeCode)) {//金额字段取total_pay_amount
            amount = amount.add(claimVo.getTotalPayAmount());
        } else if (totalAmountTypeCodes.contains(headerTypeCode)) {//金额字段取total_amount
            amount = amount.add(claimVo.getTotalAmount());
        } else {//其它取
            amount = amount.add(claimVo.getTotalAmount());
        }

        String typeAndCodeAndTotalPayAmount = "【" + headerType + "】-【" + code + "】-【" + amount + "】";
        if(EventCode.wfp.name().equals(eventCode)) {//审批
            sb.append("您收到一条【").append(fullName).append("】的").append(typeAndCodeAndTotalPayAmount).append(",请尽快审批。");
        } else if (EventCode.wfq.name().equals(eventCode)) {//询问
            //您有一条【单据类型】-【单据号】-【单据金额】需要回复相关信息，请尽快回复
            sb.append("您有一条").append(typeAndCodeAndTotalPayAmount).append("需要回复相关信息，请尽快回复");
        } else if (EventCode.wfqa.name().equals(eventCode)) {//答复：【提交人】提交的单据【单据号】已收到回复，请尽快确认。
            //【提交人】提交的【单据类型】-【单据号】-【单据金额】已收到回复，请尽快确认。
            sb.append("【").append(fullName).append("】提交的").append(typeAndCodeAndTotalPayAmount).append("已收到回复，请尽快确认。");
        } else if(EventCode.wfa.name().equals(eventCode)) {//财务审核
            sb.append("您收到一条【").append(fullName).append("】的").append(typeAndCodeAndTotalPayAmount).append(",请尽快审批。");
        } else if (EventCode.exprejected.name().equals(eventCode)) {//您的单据【单据号】被【审批人】审批拒绝，请尽快处理。
            //获取拒绝的审批人
            String rejectBy = "";
            for (SimpleFndWorkflowPathVo workflowPathVo : claimVo.getWorkflowPathVos()) {
                if ("rejected".equals(workflowPathVo.getStatus())) {
                    rejectBy = workflowPathVo.getUser().getFullName();
                }
            }
            //您的【单据类型】-【单据号】-【单据金额】被审批拒绝，请尽快处理。
            sb.append("您有一条").append(typeAndCodeAndTotalPayAmount).append("被审批拒绝，请尽快处理。");
        } else if(EventCode.expapproved.name().equals(eventCode)) {//审批通过：您有一条单据【单据号】已审批通过，请打印邮寄。
            //申请类表单不需要提醒打印邮寄，报销单需要
            if (reimbursementTypeCodes.contains(headerTypeCode)) {//报销单
                //您有一条【单据类型】-【单据号】-【单据金额】已审批通过，请打印报销单，如有合同原件、纸质发票和签到表等请一并邮寄至指定财务BP处。
                sb.append("您有一条").append(typeAndCodeAndTotalPayAmount).append("已审批通过，请打印报销单，如有合同原件、纸质发票和签到表等请一并邮寄至指定财务BP处。");
            } else {
                //您有一条【单据类型】-【单据号】-【单据金额】已审批通过，如需通过TMC平台消费请尽快使用。
                sb.append("您有一条").append(typeAndCodeAndTotalPayAmount).append("已审批通过，如需通过TMC平台消费请尽快使用。");
            }
        } else if(EventCode.expclosed.name().equals(eventCode)) {
            //您有一条【单据类型】-【单据号】-【单据金额】已支付。（报销款实际到账时间以银行通知为准。）
            sb.append("您有一条").append(typeAndCodeAndTotalPayAmount).append("已支付。(报销款实际到账时间以银行通知为准。)");
        } else if(EventCode.dlv_received.name().equals(eventCode)) {//单据收单：您有一条单据【单据号】已被财务收单，请知晓。
            sb.append("您有一条").append(typeAndCodeAndTotalPayAmount).append("已被财务收单，请知晓。");
        } else if(EventCode.wfc.name().equals(eventCode)) {//抄送：您收到一条【提交人】的单据【单据号】抄送，请尽快查看。
            sb.append("您收到一条【").append(fullName).append("】的").append(typeAndCodeAndTotalPayAmount).append("抄送，请尽快查看。");
        }  else if(EventCode.wfm.name().equals(eventCode)) {//打回修改：您有一条单据【单据号】被【审批人】打回修改，请尽快处理。
            sb.append("您有一条").append(typeAndCodeAndTotalPayAmount).append("被打回修改，请尽快处理。");
        } else {
            sb.append(typeAndCodeAndTotalPayAmount);
        }
        return sb.toString();
    }

    /**
     * 构建待办跳转目标地址
     * @param user
     * @return
     */
    private String getUrl(User user, MqQueue mqQueue, ExpClaimHeaderCommonVo claimVo) {
        Integer  pathId  = user.getPathId();
        String eventCode = mqQueue.getEventCode();
        if(EventCode.wfp.name().equals(eventCode) || EventCode.wfq.name().equals(eventCode) || EventCode.wfqa.name().equals(eventCode)) {
            return redirectUrl + "position=approve&pathId=" + pathId + "&loginId=" + user.getCode();
        } else if(EventCode.wfa.name().equals(eventCode)) {
            return redirectUrl + "position=financeApproval&pathId=" + pathId + "&loginId=" + user.getCode();
        } else {
            return redirectUrl + "position=claim&documentId=" + claimVo.getDocumentId() + "&loginId=" + user.getCode();
        }
    }

    /**
     * 封装已办推送入参
     * @param claimVo
     * @param mqQueue
     * @param user
     * @return
     */
    private OaTodoDonePushReq getTodonePushData(ExpClaimHeaderCommonVo claimVo, MqQueue mqQueue,  User user) {
        OaTodoDonePushReq todone = new OaTodoDonePushReq();
        todone.setAppName("云简业财");
        todone.setModelName("云简业财");
        String modelId = claimVo.getCode();//单据-消息ID
        Integer pathId = user.getPathId() == null ? mqQueue.getPathId() : user.getPathId();
        if (pathId != null) {
            modelId = modelId + "-" + pathId;
        }
        modelId = modelId + "-" + user.getCode();//单据号-审批流节点ID-员工号
        todone.setModelId(modelId);
        todone.setOptType(1);//1:已办操作
        JSONObject target = new JSONObject();
        target.put("PersonNo", user.getCode());
        todone.setTargets(JSONUtil.toJsonStr(target));//当前代办审批人
        return todone;
    }

    /**
     * 封装代办消除入参
     * @param claimVo
     * @param mqQueue
     * @param user
     * @return
     */
    private OaTodoDeletePushReq getTodoDelData(ExpClaimHeaderCommonVo claimVo, MqQueue mqQueue, User user) {
        OaTodoDeletePushReq toDel = new OaTodoDeletePushReq();
        toDel.setAppName("云简业财");
        toDel.setModelName("云简业财");
        String modelId = claimVo.getCode();//单据-消息ID
        if (user.getPathId() != null) {//path_id不能通过mq_queue获取，要通过审批节点获取将其封装在user中
            modelId = modelId + "-" + user.getPathId();
        }
        modelId = modelId + "-" + user.getCode();//单据号-审批流节点ID-员工号
        toDel.setModelId(modelId);
        toDel.setOptType(1);//1:删除代办操作
        JSONObject target = new JSONObject();
        target.put("PersonNo", user.getCode());
        toDel.setTargets(JSONUtil.toJsonStr(target));//当前代办审批人
        return toDel;
    }

    @Override
    public ServiceRes todoPush(OaTodoPushReq req) {
        ServiceRes serviceRes = ServiceRes.success(null);
        try {
            String url = todoUrl + todo;//代办推送url
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", ContentType.APPLICATION_JSON.toString());
            headers.add("Connection", "keep-alive");
            HttpEntity entity = new HttpEntity<>(req, headers);
            log.info("调用OA代办推送请求：url={}，req={}，entity={}", url, req,JSONUtil.toJsonStr(entity));
            ResponseEntity<String> responseEntity = restTemplate.exchange(URI.create(url), HttpMethod.POST, entity, String.class);
            String response = responseEntity.getBody();
            log.info("{}调用oa待办推送接口响应：response={}", response);
            OaTodoPushRes res = JSONUtil.toBean(response, OaTodoPushRes.class);
            if(!OaTodoPushRes.isSuccess(res)) {
                serviceRes.setCode(ServiceRes.CodeEnum.F.getCode());
                serviceRes.setMsg("调用oa待办推送接口返回失败：" + OaTodoPushRes.getMessage(res));
            }
        } catch (Exception e) {
            log.error("{}调用OA待办推送接口异常：{}", req.getModelId(), e.getMessage(), e);
            String errorMsg = req.getModelId() + "调用OA待办推送接口异常:" + e.getMessage();
            serviceRes.setCode(ServiceRes.CodeEnum.F.getCode());
            serviceRes.setMsg(errorMsg);
        }
        return serviceRes;
    }

    @Override
    public ServiceRes todoDonePush(OaTodoDonePushReq req) {
        ServiceRes serviceRes = ServiceRes.success(null);
        try {
            String url = todoUrl + todoDone;//已办办推送url
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", ContentType.APPLICATION_JSON.toString());
            headers.add("Connection", "keep-alive");
            HttpEntity entity = new HttpEntity<>(req, headers);
            log.info("调用OA已办推送请求：url={}，req={}，entity={}", url, req,JSONUtil.toJsonStr(entity));
            ResponseEntity<String> responseEntity = restTemplate.exchange(URI.create(url), HttpMethod.POST, entity, String.class);
            String response = responseEntity.getBody();
            log.info("{}调用oa待办推送接口响应：response={}", response);
            OaTodoPushRes res = JSONUtil.toBean(response, OaTodoPushRes.class);
            if(!OaTodoPushRes.isSuccess(res)) {
                serviceRes.setCode(ServiceRes.CodeEnum.F.getCode());
                serviceRes.setMsg("调用OA已办推送接口返回失败：" + OaTodoPushRes.getMessage(res));
            }
        } catch (Exception e) {
            log.error("{}调用OA已办推送接口异常：{}", req.getModelId(), e.getMessage(), e);
            String errorMsg = req.getModelId() + "调用oOA已办推送接口异常:" + e.getMessage();
            serviceRes.setCode(ServiceRes.CodeEnum.F.getCode());
            serviceRes.setMsg(errorMsg);
        }
        return serviceRes;
    }

    @Override
    public ServiceRes todoDelPush(OaTodoDeletePushReq req) {
        ServiceRes serviceRes = ServiceRes.success(null);
        try {
            String url = todoUrl + deleteTodo;//消除代办推送url
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", ContentType.APPLICATION_JSON.toString());
            headers.add("Connection", "keep-alive");
            HttpEntity entity = new HttpEntity<>(req, headers);
            log.info("调用OA代办消除推送请求：url={}，req={}，entity={}", url, req,JSONUtil.toJsonStr(entity));
            ResponseEntity<String> responseEntity = restTemplate.exchange(URI.create(url), HttpMethod.POST, entity, String.class);
            String response = responseEntity.getBody();
            log.info("{}调用oa待办推送接口响应：response={}", response);
            OaTodoPushRes res = JSONUtil.toBean(response, OaTodoPushRes.class);
            if(!OaTodoPushRes.isSuccess(res)) {
                serviceRes.setCode(ServiceRes.CodeEnum.F.getCode());
                serviceRes.setMsg("调用OA代办消除推送接口返回失败：" + OaTodoPushRes.getMessage(res));
            }
        } catch (Exception e) {
            log.error("{}调用OA代办消除推送接口异常：{}", req.getModelId(), e.getMessage(), e);
            String errorMsg = req.getModelId() + "调用oOA代办消除推送接口异常:" + e.getMessage();
            serviceRes.setCode(ServiceRes.CodeEnum.F.getCode());
            serviceRes.setMsg(errorMsg);
        }
        return serviceRes;
    }

    /**
     * 获取推送目标用户
     * @param claimVo
     * @param mqQueue
     * @return employee_number -> FlowEnum.getCode
     */
    private List<User> getUser(ExpClaimHeaderCommonVo claimVo, MqQueue mqQueue) {
        List<User> users = new ArrayList<>();
        List<SimpleFndWorkflowPathVo> flowVos = claimVo.getWorkflowPathVos();
        Integer   pathId = mqQueue.getPathId();
        String  exchange = mqQueue.getExchange();
        String eventCode = mqQueue.getEventCode();
        if(pathId != null) {
            Optional<SimpleFndWorkflowPathVo> flowVo1 = getFlowByPathId(flowVos, pathId);
            if(!flowVo1.isPresent()) {
                return users;
            }
            // 触发点
            String code1 = flowVo1.get().getUser().getEmployeeNumber();
            String fullname = flowVo1.get().getUser().getFullName();//审批人姓名
            List<String> userCodes = new ArrayList<>();
            // 按岗位审批的场景：岗位中有一人审批之后，已办的审批流中会用审批人信息吗？
            //if (StrUtil.isEmpty(code1) && flowVo1.get().getPosition() != null) {
            if (flowVo1.get().getPosition() != null) {
                String positionCode = flowVo1.get().getPosition().getPositionCode();
                userCodes = getPositionUser(claimVo.getCompanyId(), positionCode);
                if (userCodes != null && userCodes.size() != 0) {
                    //岗位审批找到了对应的审批人
                    for (String userCode : userCodes) {
                        User user = User.builder().todoType(TodoType.push).code(userCode).pathId(flowVo1.get().getPathId()).build();
                        if(Exchange.wf_todo.name().equals(exchange)) {
                            user.setTodoType(TodoType.push);
                            if (EventCode.wfc.name().equals(eventCode)) {//抄送
                                user.setPushType(PushType.COPY);
                            } else {
                                user.setPushType(PushType.TODO);
                            }
                        } else {
                            user.setTodoType(TodoType.done);
                            user.setPushType(PushType.DONE);
                        }
                        users.add(user);
                    }
                }
            }
            //指定用户审批
            User user1 = User.builder().todoType(TodoType.push).code(code1).pathId(flowVo1.get().getPathId()).build();
            user1.setTodoType(TodoType.push);
            if(Exchange.wf_todo.name().equals(exchange)) {
                user1.setTodoType(TodoType.push);
                if (EventCode.wfc.name().equals(eventCode)) {//抄送
                    user1.setPushType(PushType.COPY);
                } else {
                    user1.setPushType(PushType.TODO);
                }
            } else {
                user1.setTodoType(TodoType.done);
                user1.setPushType(PushType.DONE);
            }
            user1.setFullname(fullname);
            users.add(user1);

            // 当发起新的询问时对询问取消历史增加推送已办
            if(EventCode.wfq.name().equals(eventCode) && Exchange.wf_todo.name().equals(exchange)) {
                Integer parentId = Optional.ofNullable(flowVo1.get().getParentId()).orElse(-1);
                // 答复中的人员
                List<Integer> answeringUsers = flowVos
                        .stream()
                        .filter(v -> v.getParentId() != null && v.getParentId().equals(parentId))
                        .filter(v -> StrUtil.equals(ApprStatus.answering.name(), v.getStatus()))
                        .map(SimpleFndWorkflowPathVo::getUserId)
                        .collect(Collectors.toList());
                // 补推目标人员
                flowVos.stream()
                        .filter(v -> v.getParentId() != null && v.getParentId().equals(parentId))
                        .filter(v -> StrUtil.equals(ApprStatus.aborted.name(), v.getStatus()))
                        .filter(v -> !answeringUsers.contains(v.getUserId()))
                        .forEach(v -> {
                            String code = v.getUser().getEmployeeNumber();
                            User user = User.builder().todoType(TodoType.del).code(code).pathId(v.getPathId()).pushType(PushType.DONE).build();
                            users.add(user);
                        });
            }
            // 补偿推送已办
            if(((EventCode.wfp.name().equals(eventCode) || EventCode.wfa.name().equals(eventCode)) && Exchange.wf_done.name().equals(exchange))) {// 已办补偿
                // 并签取消历史
                flowVos.stream()
                        .filter(v -> StrUtil.equals(v.getSequenceNum(), flowVo1.get().getSequenceNum()))
                        .filter(v -> StrUtil.equals(ApprStatus.aborted.name(), v.getStatus()))
                        .forEach(v -> {
                            String code = v.getUser().getEmployeeNumber();
                            User user = User.builder().todoType(TodoType.del).code(code).pathId(v.getPathId()).pushType(PushType.DONE).build();
                            users.add(user);
                        });
                // 当询问未答复但询问人强制做了审批增加推送已办
                flowVos.stream()
                        .filter(v -> v.getParentId() != null && v.getParentId().equals(pathId))
                        .filter(v -> StrUtil.equals(ApprStatus.answering.name(), v.getStatus()))
                        .forEach(v -> {
                            String code = v.getUser().getEmployeeNumber();
                            User user = User.builder().todoType(TodoType.del).code(code).pathId(v.getPathId()).pushType(PushType.DONE).build();
                            users.add(user);
                        });
            }
            //审批人收到回复
            if(EventCode.wfqa.name().equals(eventCode)) {
                Integer parentId = flowVo1.get().getParentId().intValue();
                for (SimpleFndWorkflowPathVo workflowPathVo : claimVo.getWorkflowPathVos()) {
                    int pathNum = workflowPathVo.getPathId().intValue();
                    if (pathNum == parentId) {
                        user1.setCode(workflowPathVo.getUser().getEmployeeNumber());
                        //如果是收到答复的话，需要将path_id设置成审批节点，而不是询问答复的节点
                        if (Exchange.wf_todo.name().equals(exchange)) {
                            user1.setPathId(workflowPathVo.getPathId());
                            user1.setPushType(PushType.DONE);
                        }
                    }
                }
            }
            //打回修改代办，给审批人推送已办
            if (EventCode.wfm.name().equals(eventCode) && Exchange.wf_todo.name().equals(exchange)) {
                //判断当前审批流中是否有returned节点，如果有，则给它推送已办
                for (SimpleFndWorkflowPathVo flowVo : flowVos) {
                    String workFlowStatus = flowVo.getStatus();
                    if ("returned".equals(workFlowStatus)) {
                        String wfmUserCode = flowVo.getUser().getEmployeeNumber();
                        User user = User.builder().todoType(TodoType.done).code(wfmUserCode).pathId(flowVo.getPathId()).pushType(PushType.DONE).build();
                        log.info("单据:{},推送打回修改代办时，给returned节点的审批人推送已办,returned审批节点:{},封装的user信息:{}", claimVo.getCode(), JSONUtil.toJsonStr(flowVo), JSONUtil.toJsonStr(user));
                        users.add(user);
                    }
                }
            }
            //单据审批或审核代办wfa、wfp:判断是否有退回节点，如果有，则给对应的path_id推送已办
            if (Exchange.wf_todo.name().equals(exchange) && (EventCode.wfp.name().equals(eventCode) || EventCode.wfa.name().equals(eventCode))) {
                //判断当前审批流中是否有reverted节点，如果有，则给它推送已办
                for (SimpleFndWorkflowPathVo flowVo : flowVos) {
                    String workFlowStatus = flowVo.getStatus();
                    if ("reverted".equals(workFlowStatus)) {
                        String revertUserCode = flowVo.getUser().getEmployeeNumber();
                        User user = User.builder().todoType(TodoType.done).code(revertUserCode).pathId(flowVo.getPathId()).pushType(PushType.DONE).build();
                        log.info("单据:{},退回上个节点审批时，给reverted节点的审批人推送已办,reverted审批节点:{},封装的user信息:{}", claimVo.getCode(), JSONUtil.toJsonStr(flowVo), JSONUtil.toJsonStr(user));
                        users.add(user);
                    }
                }
            }

        } else {
            if(EventCode.expcancelled.name().equals(eventCode)) {// 单据删除特殊场景
                Optional<SimpleFndWorkflowPathVo> pathVo = flowVos.stream()
                        .filter(v -> ApprStatus.open.name().equals(v.getStatus()))
                        .sorted(Comparator.comparing(SimpleFndWorkflowPathVo::getSequenceNum)).findFirst();
                if(pathVo.isPresent()) {
                    flowVos.stream()
                            .filter(v -> pathVo.get().getSequenceNum().equals(v.getSequenceNum()))
                            .forEach( v -> {
                                String code = v.getUser().getEmployeeNumber();
                                User user = User.builder().todoType(TodoType.del).code(code).pathId(v.getPathId()).build();//path_id用于清除代办
                                user.setPushType(PushType.DONE);
                                users.add(user);
                            });
                }
            } else if(EventCode.expwithdrawed.name().equals(eventCode)) {// 单据撤回特殊场景
                //撤回后找到审批中的节点，审批中的说明已经推送代办，则需要取消代办
                List<String> targetStatus = Arrays.asList(ApprStatus.aborted.name(), ApprStatus.approving.name());
                List<SimpleFndWorkflowPathVo> pathVos = flowVos.stream()
                        .filter(v -> targetStatus.contains(v.getStatus()))
                        .collect(Collectors.toList());
                if(CollUtil.isNotEmpty(pathVos)) {
                    pathVos.stream()
                            .forEach( v -> {
                                String code = v.getUser().getEmployeeNumber();
                                User user = User.builder().todoType(TodoType.del).code(code).pathId(v.getPathId()).build();//path_id用于清除代办
                                user.setPushType(PushType.DONE);
                                users.add(user);
                            });
                }
            } else if (EventCode.exprejected.name().equals(eventCode)){// 审批拒绝，给提单人推送通知
                String employeeNumber = claimVo.getSubmitUserVo().getEmployeeNumber();//提单人
                User user = User.builder().todoType(TodoType.push).code(employeeNumber).pushType(PushType.NOTIFICATION).pathId(null).build();
                users.add(user);
                //审批拒绝时，如果审批人之前发起了询问且提单人未回复，那么则消除提单人的询问代办("answering"状态的审批流节点)
                insertAnsweringUser(flowVos, users);
            } else if (EventCode.expapproved.name().equals(eventCode)){// 审批通过，给提单人推送通知
                String employeeNumber = claimVo.getSubmitUserVo().getEmployeeNumber();//提单人
                User user = User.builder().todoType(TodoType.push).code(employeeNumber).pushType(PushType.NOTIFICATION).pathId(null).build();
                users.add(user);
            } else if (EventCode.exppreapproved.name().equals(eventCode)){// 业务审批通过：您有一条单据【单据号】已业务审批通过，请打印单据封面。
                String employeeNumber = claimVo.getSubmitUserVo().getEmployeeNumber();//提单人
                User user = User.builder().todoType(TodoType.push).code(employeeNumber).pushType(PushType.NOTIFICATION).pathId(null).build();
                users.add(user);
            } else if (EventCode.expclosed.name().equals(eventCode)){// 单据已支付：您有一条单据【单据号】已支付。（报销款实际到账时间以银行通知为准。）
                //判断是都为报销单，只有报销单才推送"已支付"的提示
                String headerTypeCode = claimVo.getHeaderTypeVo().getCode();
                //if (reimbursementTypeCodes.contains(headerTypeCode) && !"YXJK01".equals(headerTypeCode)) {
                if (reimbursementTypeCodes.contains(headerTypeCode)) {
                    String employeeNumber = claimVo.getSubmitUserVo().getEmployeeNumber();//提单人
                    User user = User.builder().todoType(TodoType.push).code(employeeNumber).pushType(PushType.NOTIFICATION).pathId(null).build();
                    users.add(user);
                }
            } else if (EventCode.dlv_received.name().equals(eventCode)){// 单据收单：您有一条单据【单据号】已被财务收单，请知晓。
                String employeeNumber = claimVo.getSubmitUserVo().getEmployeeNumber();//提单人
                User user = User.builder().todoType(TodoType.push).code(employeeNumber).pushType(PushType.NOTIFICATION).pathId(null).build();
                users.add(user);
            } else {// 其他场景，给提单人发待办或已办
                String employeeNumber = claimVo.getSubmitUserVo().getEmployeeNumber();
                User user = User.builder().todoType(TodoType.push).code(employeeNumber).pathId(null).build();
                user.setPushType(Exchange.wf_todo.name().equals(exchange) ?
                        PushType.TODO : PushType.DONE);
                users.add(user);
            }
        }

        return users;
    }

    /**
     * 获取审批流中的询问节点，状态为答复中
     * @return
     */
    private void insertAnsweringUser(List<SimpleFndWorkflowPathVo> flowVos,List<User> users) {
        //找到询问状态为answering状态的节点，消除代办
        for (SimpleFndWorkflowPathVo flowVo : flowVos) {
            if (StrUtil.equals(ApprStatus.answering.name(), flowVo.getStatus())) {
                //询问节点的path_id
                Integer pathId = flowVo.getPathId();
                //询问对象
                String employeeNumber = flowVo.getUser().getEmployeeNumber();
                User user = new User.UserBuilder().code(employeeNumber).pathId(pathId).todoType(TodoType.del).build();
                users.add(user);
            }
        }
    }

    /**
     * 获取指定pathId的审批流
     * @param flowVos
     * @param pathId
     * @return
     */
    private Optional<SimpleFndWorkflowPathVo> getFlowByPathId(List<SimpleFndWorkflowPathVo> flowVos, Integer pathId) {
        return flowVos.stream().filter(v -> v.getPathId().equals(pathId)).findAny();
    }

    List<String> getPositionUser(Integer companyId, String positionCode) {
        log.info("查询岗位人员信息:{}",positionCode);
        String path = "/common/positions/v2/findByCode"
                + "?code=" + positionCode
                + "&bizId=" + IdUtil.simpleUUID()
                + "&timestamp=" + System.currentTimeMillis();
        Object res = yjApiService.api(path, HttpMethod.GET, null, null);
        JSONArray datas = JSONUtil.parseObj(res).getJSONArray("data");
        if (datas == null || datas.size() == 0) {
            log.info("岗位{}未查询到对应的人员信息");
            return null;
        }
        JSONObject data = datas.getJSONObject(0);
        if (data == null || data.getJSONArray("user_vos") == null || data.getJSONArray("user_vos").size() == 0) {
            log.info("岗位{}未查询到对应的人员信息");
            return null;
        }
        List<String> userList = new ArrayList<>();
        JSONArray userVos = data.getJSONArray("user_vos");
        for (Object userVo : userVos) {
            String code = JSONUtil.parseObj(userVo).getStr("code");
            if (StrUtil.isNotEmpty(code)) {
                userList.add(code);
            }
        }
        log.info("岗位{}查询到对应的人员信息:{}",positionCode,JSONUtil.toJsonStr(userVos));
        return userList;
    }

    enum EventCode {
        wfp, wfa, wfq, wfqa, wfm, expwithdrawed, expsubmitted,expcancelled, exprejected, expapproved, exppreapproved, expclosed, dlv_received, wfc,
    }

    enum Exchange {
        wf_todo, wf_done
    }

    enum ApprStatus {
        aborted,
        approved,
        approving,
        confirmed,
        copied,
        forwarded,
        modified,
        open,
        rejected,
        withdrawed,
        answering,
        answered;
    }

    enum TodoType {
        push, // 推送待办
        done, // 推送已办
        del   // 清除待办
    }

    @Getter
    @AllArgsConstructor
    enum PushType {
        TODO("0"),// 待办
        DONE("2"),// 已办
        NOTIFICATION("4"),// 通知
        COPY("8");// 抄送
        String code;
    }

    @Builder
    @Data
    static class User {
        TodoType  todoType;// 类型
        String        code;// 员工号
        Integer     pathId;// 审批流id
        PushType  pushType;// 推送类型
        String  fullname;// 审批人姓名
    }

}
