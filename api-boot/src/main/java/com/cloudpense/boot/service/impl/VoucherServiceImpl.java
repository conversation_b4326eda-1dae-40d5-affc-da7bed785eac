package com.cloudpense.boot.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cloudpense.akesobio.dto.oapiclaim.VcBackOapiDto;
import com.cloudpense.boot.config.sap.JConnDataProvider;
import com.cloudpense.boot.config.sap.SapClient;
import com.cloudpense.boot.config.sap.SapConnProperties;
import com.cloudpense.boot.service.VoucherService;
import com.cloudpense.boot.vo.SapCreateVoucherReq;
import com.cloudpense.boot.vo.SapCreateVoucherRes;
import com.cloudpense.common.constants.ApiPathConstant;
import com.cloudpense.common.service.BaseOneDto;
import com.cloudpense.common.service.YjApiService;
import com.cloudpense.common.service.YjLovService;
import com.cloudpense.common.service.claim.YjClaimService;
import com.cloudpense.common.service.user.YjUserService;
import com.cloudpense.common.vo.ServiceRes;
import com.cloudpense.common.vo.todo.OaTodoPushRes;
import com.cloudpense.common.vo.yj.YjPageInfo;
import com.cloudpense.common.vo.yj.claim.ClaimUniqueRes;
import com.cloudpense.common.vo.yj.claim.HeaderBacksDto;
import com.cloudpense.common.vo.yj.claim.HeaderLinksDto;
import com.cloudpense.common.vo.yj.supplierAccount.SupplierAccountFindByPageRes;
import com.cloudpense.common.vo.yj.user.UserFindByCodeReq;
import com.cloudpense.common.vo.yj.user.UserFindByCodeRes;
import com.cloudpense.standard.dao.entity.ExpClaimHeader;
import com.cloudpense.standard.enumeration.voucher.GlStatusEnum;
import com.cloudpense.standard.vo.claim.ExpClaimHeaderCommonVo;
import com.cloudpense.standard.vo.claim.ExpClaimLineCommonVo;
import com.cloudpense.standard.vo.claim.SimpleFndWorkflowPathVo;
import com.cloudpense.standard.vo.document.base.ExpHeaderTypeVo;
import com.cloudpense.standard.vo.masterdata.DepartmentVo;
import com.cloudpense.standard.vo.masterdata.ProjectVo;
import com.cloudpense.standard.vo.masterdata.SupplierAccountVo;
import com.cloudpense.standard.vo.masterdata.UserApiVo;
import com.sap.conn.jco.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/10/24
 * @redmine 101118
 * @description 凭证接口实现
 */
@Slf4j
@Service
public class VoucherServiceImpl implements VoucherService {

    @Resource
    YjUserService yjUserService;
    @Autowired
    YjClaimService yjClaimService;
    @Resource
    RestTemplate restTemplate;
    @Resource
    YjApiService yjApiService;
    @Autowired
    YjLovService yjLovService;
    @Autowired
    private SapClient sapClient;//sap客户端连接
    private final String sapMethodName006 = "ZFI_FM_OA_SEND_006C";
    private final String sapMethodName035 = "ZFI_FM_OA_SEND_035";

    @Value("${sap.ashost}")
    String ashost;
    @Value("${sap.sysnr}")
    String sysnr;
    @Value("${sap.client}")
    String client;
    @Value("${sap.user}")
    String user;
    @Value("${sap.passwd}")
    String passwd;
    @Value("${sap.lang}")
    String lang;
    @Value("${yj.common.vcBackUrl}")
    String vcBackUrl;
    @Value("${sap.voucherBackUrl}")
    String vcOaBackUrl;
    @Value("${yj.cproxy.updateClaimUrl}")
    String updateClaimUrl;

    List<String> headerTypeCodes = Arrays.asList("YXCL02","ZRCL02","YXFY02","ZRFY02","YXHY02","ZRHY02","YXZR02","YXDG02");
    //可能自动生成报销单类型
    List<String> autoCreateHeaderTypeCodes = Arrays.asList("YXCL02","ZRCL02","YXFY02","ZRFY02","YXHY02","ZRHY02","YXZR02");
    //内部订单号必填单据：差旅报销：YXCL02、费用报销：YXFY02、对公预付：YXDG01、对公应付：YXDG02、终端准入核报：YXZR02、会议核报：YXHY02、差旅报销-准入：ZRCL02、费用报销-准入：ZRFY02、会议核报-准入：ZRHY02
    List<String> column102HeaderTypeCodes = Arrays.asList("YXCL02","YXFY02","YXDG01","YXDG02","YXZR02","YXHY02","ZRCL02","ZRFY02","ZRHY02");
    //YXJK01:员工借款、YXJK03:员工还款、YXDG01:对公预付、YXDG02:对公应付
    List<String> advanceHeaderTypeCodes = Arrays.asList("YXJK01","YXDG01");//2024-12-04:员工还款不在费控推送凭证
    //记账29、39、31 填lifnr,不用填HKONT
    List<String> isExistHKONT = Arrays.asList("29","31","39");
    //单据头包含支付对象的单据类型:差旅报销&准入、预付和应付、员工借款是没有分不同支付对象的，都按表头 submit_user 提交人 （核报单提交人） 支付，其他单按 费用行的支付对象支付
    List<String> containPayObjectTypes = Arrays.asList("YXCL02","YXDG01","YXDG02","ZRCL02","YXJK01");
    //开户行标识
    private static Map<String, String> bankIndexMap = new HashMap<>();
    //账户标识
    private static Map<String, String> accountIndexMap = new HashMap<>();
    //片区与凭证类型的对应关系<片区编码，类型编码>
    Map<String, String> voucherTypes = new HashMap<>();

    static {
        //开户行标识
        bankIndexMap.put("1000", "CMB01");
        bankIndexMap.put("1030", "CMB07");
        bankIndexMap.put("1050", "CMB08");
        bankIndexMap.put("1060", "CMB08");
        //账户标识
        accountIndexMap.put("1000", "10901");
        accountIndexMap.put("1030", "10901");
        accountIndexMap.put("1050", "10803");
        accountIndexMap.put("1060", "01686");
    }
    /**
     * 支付凭证推送
     * @param claim
     * @return
     */
    @Override
    public ServiceRes voucherPush(ExpClaimHeaderCommonVo claim) {
        log.info("报销单:{},推送凭证开始", claim.getCode());

        //0.过滤数据
        String glMsg = "";
        String journalNum = "";
        String bukrs = "";//公司代码
        String gjahr = "";//会计年度
        String glStatus = GlStatusEnum.GENERATED.getCode();
        long glDate = new Date().getTime();
        Integer headerId = claim.getHeaderId();

        String documentNum = claim.getCode();
        //如果报销单符合凭证推送条件，那么需要判断是否为"手动创建"；"手工创建"推凭证，"自动创建"直接将状态更新为已生成
        boolean isManualCreate = createMethod(claim);

        if (!isManualCreate) {//符合推送条件，又不是手工创建，那就是自动创建，直接将其状态改为"已生成"
            String message = "报销单:" + documentNum + "自动创建,不推送凭证,状态改为已生成";
            log.info(message);
            //更新凭证状态
            return updateClaimGlStatus(headerId);
        }

        //先判断是否自动核销，不是自动生成的，才在接口推送凭证
        boolean isPush = filterClaim(claim);
        if (!isPush) {
            String message = "报销单:" + documentNum + "不满足凭证推送条件";
            log.info(message);
            return ServiceRes.failure(message);
        }


        SapCreateVoucherRes voucherRes = null;
        try {

            //1.查询片区与凭证类对应的值列表：2024.12.02,所有凭证类型都为"YX"，不再获取片区对应的凭证类型
            /*voucherTypes = getVoucherTypeFromLov();
            if (voucherTypes == null) {
                log.info("查询片区代码对应凭证类型的值列表(CBZXDX)时出现异常！", documentNum);
                return null;
            }*/

            //2.封装数据
            SapCreateVoucherReq requestData = packageData(claim);
            bukrs = requestData.getSapVoucherHeader().getBUKRS();

            //3.推送SAP
            log.info("报销单:{},推送SAP==>开始", claim.getCode());
            if (requestData.is035()) {
                //3.1 对公预付接口及对公预付冲抵
                voucherRes = pushSap035(requestData);
                //voucherRes = null;
            } else {
                //3.2 普通凭证接口
                voucherRes = pushSap006(requestData);
                //voucherRes = null;
            }
            log.info("报销单:{},推送SAP==>结束", claim.getCode());

            if ("S".equals(voucherRes.getEV_TYPE())) {
                journalNum = voucherRes.getAC_DOC_NO();
                gjahr = voucherRes.getFISC_YEAR();
                //5.推送成功后，将凭证号回传至康方的OA系统
                log.info("报销单:{},凭证号回传OA系统==>开始", claim.getCode());
                pushOA(claim, voucherRes);
                log.info("报销单:{},凭证号回传OA系统==>结束", claim.getCode());
            } else {
                glStatus = GlStatusEnum.ERROR.getCode();
                glMsg = voucherRes.getEV_MESSAGE();
            }
        } catch (Exception e) {
            log.error("报销单:{}推送凭证出现异常,异常信息:{}", documentNum, e.getMessage(), e);
            if (StrUtil.isNotEmpty(e.getMessage())) {
                glMsg = e.getMessage();
            } else {
                glMsg = "推送凭证出现异常!";
            }
            glStatus = GlStatusEnum.ERROR.getCode();
            log.error("voucherPush,异常:{}",e.getMessage(),e);
        }
        //4.回写凭证号
        int count = 0;
        log.info("报销单:{},回写凭证号==>开始", claim.getCode());
        //ServiceRes api = null;
        //回传的凭证号更改取值逻辑为公司代码+年度+凭证号，字段为（BUKRS + GJAHR + BELNR）,数据样例 比如105020255500000001，共18位
        if (StrUtil.isNotEmpty(journalNum)) {//如果凭证推送成功，获取到SAP凭证后，则需要将其拼接之后传回费控
            journalNum = bukrs + gjahr + journalNum;
        }
        ServiceRes api = writeBackVc(documentNum, journalNum, glStatus, glMsg, glDate, headerId, count);
        log.info("报销单:{},回写凭证号==>结束", claim.getCode());
        return ServiceRes.success(api);

    }

    /**
     * 报销单：判断报销单手工创建还是自动创建
     * @param claim
     * @return
     */
    private boolean createMethod(ExpClaimHeaderCommonVo claim) {
        //在这里要再判断一下报销单的状态，因为只7中单子会自动生成报销单:autoCreateHeaderTypeCodes
        String typeCode = claim.getHeaderTypeVo().getCode();
        if (!autoCreateHeaderTypeCodes.contains(typeCode)) {
            return true;//如果单子一定不是自动创建的，那就返回true,通过接口推送
        }
        //column140 : 手工创建
        String columnJson = claim.getColumnJson();
        if (StrUtil.isEmpty(columnJson)) {
            return false;
        }

        JSONObject jsonObject = JSONUtil.parseObj(columnJson);
        String column140 = jsonObject.getStr("column140");
        if ("手工创建".equals(column140)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 将凭证号回传至QA系统
     * @param claim
     * @param voucherRes
     */
    private void pushOA(ExpClaimHeaderCommonVo claim, SapCreateVoucherRes voucherRes) {
        String documentNum = claim.getCode();
        log.info("单据:{}已生成凭证号:{}，回传OA系统开始...", documentNum, voucherRes.getAC_DOC_NO());
        //1.封装参数
        JSONObject requestData = new JSONObject();
        JSONObject fromInfo = new JSONObject();
        fromInfo.set("form_id", documentNum);//支付单据号
        fromInfo.set("applicant_number", claim.getChargeUserVo().getCode());//支付单申请人工号
        fromInfo.set("applicant_name", claim.getChargeUserVo().getFullName());//支付单申请人姓名

        JSONObject financeVoucher = new JSONObject();
        financeVoucher.set("form_id", documentNum);//支付单单号
        financeVoucher.set("voucher_number", voucherRes.getAC_DOC_NO());//支付单对应凭证号
        financeVoucher.set("voucher_year", voucherRes.getFISC_YEAR());//支付单对应凭证号的凭证年度
        financeVoucher.set("company_code", claim.getBranchVo().getCode());//支付单对应凭证号的凭证公司代码

        requestData.set("form_info",fromInfo);
        requestData.set("finance_voucher", financeVoucher);
        //推送OA
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", ContentType.APPLICATION_JSON.toString());
            headers.add("Connection", "keep-alive");
            HttpEntity entity = new HttpEntity<>(requestData, headers);
            log.info("单据:{},调用OA凭证回传接口：url={},entity={}",documentNum, vcOaBackUrl, requestData,JSONUtil.toJsonStr(entity));
            ResponseEntity<String> responseEntity = restTemplate.exchange(URI.create(vcOaBackUrl), HttpMethod.POST, entity, String.class);
            String response = responseEntity.getBody();
            log.info("单据:{},调用OA凭证回传接口响应：response={}",documentNum, response);
            if (StrUtil.isEmpty(response)) {
                log.info("单据:{},回传凭证号{}失败！",documentNum,voucherRes.getAC_DOC_NO());
                return;
            }
            //处理结果
            JSONObject responseObject = JSONUtil.parseObj(response);
            String code = responseObject.getStr("code");
            String message = responseObject.getStr("message");
            if ("200".equals(code)) {
                log.info("单据:{},回传凭证号{}成功！", documentNum, voucherRes.getAC_DOC_NO());
            } else {
                log.info("单据:{},回传凭证号{}失败，原因:{}", documentNum, voucherRes.getAC_DOC_NO(), message);
            }
        } catch (Exception e) {
            log.error("{}调用OA凭证回传接口异常：{}",documentNum, e.getMessage(), e);
        }
    }

    private boolean filterClaim(ExpClaimHeaderCommonVo claim) {
        /**
         * 过滤条件
         * 1.YXJK03-员工还款：不管还多少，都不会生成支付单(只用公司需要往外支付时，才会生成支付单)；
         * 2.YXJK01:员工借款申请会生成支付单，不在财务凭证接口推送,需要在headerTypeCodes中将单据类型剔除(2024-11-27)
         * 3.YXDG01:对公预付会生成支付单，不在财务凭证接口推送，在支付凭证页面推送，需要在headerTypeCodes中将单据类型剔除(2024-11-27)
         * 4.其它报销单：只要total_pay_amount = 0,完全冲抵的情况；因为公司不需要支付金额，所以也不会生成支付单
         */
        String documentNum = claim.getCode();//当前报销单号
        String headerTypeCode = claim.getHeaderTypeVo().getCode();//当前报销单类型

        //2024-12-04:员工还款不在费控推送凭证
        /*if ("YXJK03".equals(headerTypeCode)) {//员工还款
            log.info("员工还款({}):{},需要推送凭证", headerTypeCode, documentNum);
            return true;
        }*/

        BigDecimal totalPayAmount = claim.getTotalPayAmount();//公司需要对员工或供应商支付的金额
        if (BigDecimal.ZERO.compareTo(totalPayAmount) == 0 && headerTypeCodes.contains(headerTypeCode)) {
            //完全冲抵时，需要以当前报销单推送凭证
            log.info("报销单({}):{},金额完全冲抵,需要推送凭证", headerTypeCode, documentNum);
            return true;
        }

        log.info("当前报销单({}):{},totalPayAmount:{},不需要推送凭证", headerTypeCode, documentNum, totalPayAmount);
        return false;
    }

    /**
     * 建立SAP连接
     * @return
     */
/*    public SapClient sapClient() {
        SapConnProperties settings = SapConnProperties.builder()
                .ashost(ashost)
                .sysnr(sysnr)
                .client(client)
                .user(user)
                .passwd(passwd)
                .lang(lang)
                .build();

        JConnDataProvider.registerInEnvironment();
        SapClient sapClient = new SapClient(settings);
        return sapClient;
    }*/

    /**
     * 推送SAP-006-普通凭证
     * @param requestData
     * @return
     */
    private SapCreateVoucherRes pushSap006(SapCreateVoucherReq requestData) throws JCoException {
        log.info("SAP执行方法{},入参：{}", sapMethodName006, JSONUtil.toJsonStr(requestData));
        JCoFunction function = null;
        try {
            //DestinationDataProvider already registered
            //0.建立连接
            //1.获取SAP的接口对象
            function = sapClient.getDestination().getRepository().getFunctionTemplate(sapMethodName006).getFunction();
        } catch (Exception e) {
            String message = e.getMessage();
            if (StrUtil.isNotEmpty(message) || message.contains("DestinationDataProvider already registered")) {
                log.info("DestinationDataProvider already registered");
            } else {
                throw new RuntimeException(e);
            }
        }

        //2.封装头行参数
        SapCreateVoucherReq.SapVoucherHeader header = requestData.getSapVoucherHeader();
        JCoTable head = function.getImportParameterList().getTable("IT_HEAD");
        head.appendRow();
        head.setValue("BUKRS", header.getBUKRS());//必填：公司代码
        head.setValue("BLART", header.getBLART());//必填：凭证类型
        head.setValue("BLDAT", header.getBLDAT());//必填：凭证日期
        head.setValue("BUDAT", header.getBUDAT());//必填：过账日期
        if (StrUtil.isNotEmpty(header.getBKTXT())) {
            head.setValue("BKTXT", header.getBKTXT());//凭证抬头文本
        }
        head.setValue("WAERS", header.getWAERS());//必填：货币码
        head.setValue("XBLNR", header.getXBLNR());//必填：单据号
        head.setValue("XREF1_HD",header.getXREF1_HD());//系统来源
        if (StrUtil.isNotEmpty(header.getUNAME())){
            head.setValue("UNAME",header.getUNAME());//单据财务BP审核人工号
        }
        if (StrUtil.isNotEmpty(header.getXBLNR_ALT())){
            head.setValue("XBLNR_ALT",header.getXBLNR_ALT());//备选参考编号
        }

        //3.封装明细行参数
        JCoTable item = function.getImportParameterList().getTable("IT_ITEM");
        int BUZEI = 1;
        for (SapCreateVoucherReq.SapVoucherItem sapVoucherItem : requestData.getSapVoucherItems()) {
            item.appendRow();
            if (BUZEI < 10) {
                item.setValue("BUZEI", "00" + BUZEI);//X:行序号
            } else if (BUZEI >= 10 && BUZEI < 100) {
                item.setValue("BUZEI", "0" + BUZEI);
            } else {
                item.setValue("BUZEI", "" + BUZEI);
            }
            item.setValue("BSCHL", sapVoucherItem.getBSCHL());//X:过账码
            if (StrUtil.isNotEmpty(sapVoucherItem.getHKONT())) {
                item.setValue("HKONT", sapVoucherItem.getHKONT());//X:会计科目
            }
            if (StrUtil.isNotEmpty(sapVoucherItem.getLIFNR())) {
                item.setValue("LIFNR", sapVoucherItem.getLIFNR());//X:供应商
            }
            item.setValue("WRBTR", sapVoucherItem.getWRBTR());//X:凭证货币金额(保留两位小数)
            item.setValue("SGTXT", sapVoucherItem.getSGTXT());//X:行项目文本
            if (StrUtil.isNotEmpty(sapVoucherItem.getKOSTL())) {
                item.setValue("KOSTL", sapVoucherItem.getKOSTL());//成本中心
            }
            if (StrUtil.isNotEmpty(sapVoucherItem.getRSTGR())) {
                item.setValue("RSTGR", sapVoucherItem.getRSTGR());//现金流量原因代码：当会计科目类别为银行时，传line.column21
            }
            if (StrUtil.isNotEmpty(sapVoucherItem.getUMSKZ())) {
                item.setValue("UMSKZ", sapVoucherItem.getUMSKZ());//特别总账标识
            }
            if (StrUtil.isNotEmpty(sapVoucherItem.getMWSKZ())) {
                item.setValue("MWSKZ", sapVoucherItem.getMWSKZ());//税码：【进项税特殊处理逻辑】sheet
            }
            if (StrUtil.isNotEmpty(sapVoucherItem.getAUFNR())) {
                item.setValue("AUFNR", sapVoucherItem.getAUFNR());//内部订单号：项目号选择的是IIT或AK112或AK104才需要传
            }
            item.setValue("ZUONR", sapVoucherItem.getZUONR());//分配：当HKONT传的会计科目类别=进项税科目时，传行上的发票号；其他情况传单据号
            item.setValue("ZFBDT", sapVoucherItem.getZFBDT());//付款起算日：申请付款的日期
            if (StrUtil.isNotEmpty(sapVoucherItem.getHBKID())) {
                item.setValue("HBKID", sapVoucherItem.getHBKID());//开户行：康方公司的账户开户行，有银行科目必填，目前不涉及
            }
            if (StrUtil.isNotEmpty(sapVoucherItem.getHKTID())) {
                item.setValue("HKTID",sapVoucherItem.getHKTID());//账户标识：康方公司的账户开户行账号的后5位，有银行科目必填，目前不涉及
            }
            item.setValue("BVTYP", sapVoucherItem.getBVTYP());//对方开户行类型：供应商账号的尾号4位
            item.setValue("ZLSCH", sapVoucherItem.getZLSCH());//付款方式：默认传T
            if (StrUtil.isNotEmpty(sapVoucherItem.getRSTGR())) {
                //item.setValue("ZFBDT","20241022");
            }
            if (StrUtil.isNotEmpty(sapVoucherItem.getRSTGR())) {
                //item.setValue("ZFBDT","20241022");
            }
            //发票号超过18位的部门
            item.setValue("XREF2", sapVoucherItem.getXREF2());
            BUZEI++;
        }

        //4.调用并获取返回值
        JCoResponse response = null;
        try {
            response = new DefaultRequest(function).execute(sapClient.getDestination());
        } catch (JCoException e) {
            throw new RuntimeException(e);
        }

        //5.封装返回值
        SapCreateVoucherRes sapResult = new SapCreateVoucherRes();
        for (JCoField jCoField : response) {
            String fieldName = jCoField.getName();
            String value = (String) function.getExportParameterList().getValue(fieldName);
            if ("AC_DOC_NO".equals(fieldName)) {
                sapResult.setAC_DOC_NO(value);
            }
            if ("FISC_YEAR".equals(fieldName)) {
                sapResult.setFISC_YEAR(value);
            }
            if ("EV_TYPE".equals(fieldName)) {
                sapResult.setEV_TYPE(value);
            }
            if ("EV_MESSAGE".equals(fieldName)) {
                sapResult.setEV_MESSAGE(value);
            }
            if ("FIS_PERIOD".equals(fieldName)) {
                sapResult.setFIS_PERIOD(value);
            }
        }
        log.info("SAP执行方法{},返回结果:{}", sapMethodName006, JSONUtil.toJsonStr(sapResult));
        return sapResult;
    }

    /**
     * 推送SAP-035-对公预付凭证
     * @param requestData
     * @return
     */
    private SapCreateVoucherRes pushSap035(SapCreateVoucherReq requestData) throws JCoException {
        log.info("SAP执行方法{},入参：{}", sapMethodName035, JSONUtil.toJsonStr(requestData));
        JCoFunction function = null;
        try {
            //DestinationDataProvider already registered
            //0.建立连接
            //1.获取SAP的接口对象
            function = sapClient.getDestination().getRepository().getFunctionTemplate(sapMethodName035).getFunction();
        } catch (Exception e) {
            String message = e.getMessage();
            if (StrUtil.isNotEmpty(message) || message.contains("DestinationDataProvider already registered")) {
                log.info("DestinationDataProvider already registered");
            } else {
                throw new RuntimeException(e);
            }
        }

        //2.封装头行参数
        SapCreateVoucherReq.SapVoucherHeader header = requestData.getSapVoucherHeader();
        JCoTable head = function.getImportParameterList().getTable("IT_HEAD");
        head.appendRow();
        head.setValue("BUKRS", header.getBUKRS());//必填：公司代码
        head.setValue("BLART", header.getBLART());//必填：凭证类型
        head.setValue("BLDAT", header.getBLDAT());//必填：凭证日期
        head.setValue("BUDAT", header.getBUDAT());//必填：过账日期
        if (StrUtil.isNotEmpty(header.getBKTXT())) {
            head.setValue("BKTXT", header.getBKTXT());//凭证抬头文本
        }
        head.setValue("WAERS", header.getWAERS());//必填：货币码
        head.setValue("XBLNR", header.getXBLNR());//必填：单据号
        head.setValue("XREF1_HD",header.getXREF1_HD());//系统来源
        if (StrUtil.isNotEmpty(header.getUNAME())){
            head.setValue("UNAME",header.getUNAME());//单据财务BP审核人工号
        }
        if (StrUtil.isNotEmpty(header.getXBLNR_ALT())){
            head.setValue("XBLNR_ALT",header.getXBLNR_ALT());//备选参考编号
        }

        //3.封装明细行参数
        JCoTable item = function.getImportParameterList().getTable("IT_ITEM");
        int BUZEI = 1;
        for (SapCreateVoucherReq.SapVoucherItem sapVoucherItem : requestData.getSapVoucherItems()) {
            item.appendRow();
            if (BUZEI < 10) {
                item.setValue("BUZEI", "00" + BUZEI);//X:行序号
            } else if (BUZEI >= 10 && BUZEI < 100) {
                item.setValue("BUZEI", "0" + BUZEI);
            } else {
                item.setValue("BUZEI", "" + BUZEI);
            }
            item.setValue("BSCHL", sapVoucherItem.getBSCHL());//X:过账码
            if (StrUtil.isNotEmpty(sapVoucherItem.getHKONT())) {
                item.setValue("HKONT", sapVoucherItem.getHKONT());//X:会计科目
            }
            if (StrUtil.isNotEmpty(sapVoucherItem.getLIFNR())) {
                item.setValue("LIFNR", sapVoucherItem.getLIFNR());//X:供应商
            }
            item.setValue("WRBTR", sapVoucherItem.getWRBTR());//X:凭证货币金额(保留两位小数)
            item.setValue("SGTXT", sapVoucherItem.getSGTXT());//X:行项目文本
            if (StrUtil.isNotEmpty(sapVoucherItem.getKOSTL())) {
                item.setValue("KOSTL", sapVoucherItem.getKOSTL());//成本中心
            }
            if (StrUtil.isNotEmpty(sapVoucherItem.getRSTGR())) {
                item.setValue("RSTGR", sapVoucherItem.getRSTGR());//现金流量原因代码：当会计科目类别为银行时，传line.column21
            }
            if (StrUtil.isNotEmpty(sapVoucherItem.getUMSKZ())) {
                item.setValue("UMSKZ", sapVoucherItem.getUMSKZ());//特别总账标识
            }
            if (StrUtil.isNotEmpty(sapVoucherItem.getMWSKZ())) {
                item.setValue("MWSKZ", sapVoucherItem.getMWSKZ());//税码：【进项税特殊处理逻辑】sheet
            }
            if (StrUtil.isNotEmpty(sapVoucherItem.getAUFNR())) {
                item.setValue("AUFNR", sapVoucherItem.getAUFNR());//内部订单号：项目号选择的是IIT或AK112或AK104才需要传
            }
            item.setValue("ZUONR", sapVoucherItem.getZUONR());//分配：当HKONT传的会计科目类别=进项税科目时，传行上的发票号；其他情况传单据号
            item.setValue("ZFBDT", sapVoucherItem.getZFBDT());//付款起算日：申请付款的日期
            if (StrUtil.isNotEmpty(sapVoucherItem.getHBKID())) {
                item.setValue("HBKID", sapVoucherItem.getHBKID());//开户行：康方公司的账户开户行，有银行科目必填，目前不涉及
            }
            if (StrUtil.isNotEmpty(sapVoucherItem.getHKTID())) {
                item.setValue("HKTID",sapVoucherItem.getHKTID());//账户标识：康方公司的账户开户行账号的后5位，有银行科目必填，目前不涉及
            }
            item.setValue("BVTYP", sapVoucherItem.getBVTYP());//对方开户行类型：供应商账号的尾号4位
            item.setValue("ZLSCH", sapVoucherItem.getZLSCH());//付款方式：默认传T
            if (StrUtil.isNotEmpty(sapVoucherItem.getRSTGR())) {
                //item.setValue("ZFBDT","20241022");
            }
            if (StrUtil.isNotEmpty(sapVoucherItem.getRSTGR())) {
                //item.setValue("ZFBDT","20241022");
            }
            //创建预付款必输,必填默认C，对应科目预付账款-其他-非关联方
            if (StrUtil.isNotEmpty(sapVoucherItem.getZUMSK())) {
                item.setValue("ZUMSK", sapVoucherItem.getZUMSK());
            }
            //发票号超过18位的部门
            //item.setValue("XREF2", sapVoucherItem.getXREF2());
            BUZEI++;
        }

        //4.调用并获取返回值
        JCoResponse response = null;
        try {
            response = new DefaultRequest(function).execute(sapClient.getDestination());
        } catch (JCoException e) {
            throw new RuntimeException(e);
        }

        //5.封装返回值
        SapCreateVoucherRes sapResult = new SapCreateVoucherRes();
        for (JCoField jCoField : response) {
            String fieldName = jCoField.getName();
            String value = (String) function.getExportParameterList().getValue(fieldName);
            if ("AC_DOC_NO".equals(fieldName)) {
                sapResult.setAC_DOC_NO(value);
            }
            if ("FISC_YEAR".equals(fieldName)) {
                sapResult.setFISC_YEAR(value);
            }
            if ("EV_TYPE".equals(fieldName)) {
                sapResult.setEV_TYPE(value);
            }
            if ("EV_MESSAGE".equals(fieldName)) {
                sapResult.setEV_MESSAGE(value);
            }
            if ("FIS_PERIOD".equals(fieldName)) {
                sapResult.setFIS_PERIOD(value);
            }
        }
        log.info("SAP执行方法{},返回结果:{}", sapMethodName035, JSONUtil.toJsonStr(sapResult));
        return sapResult;
    }

    /**
     * 封装凭证推送SAP的数据
     * @param claim
     * @return
     */
    private SapCreateVoucherReq packageData(ExpClaimHeaderCommonVo claim) {
        SapCreateVoucherReq request = new SapCreateVoucherReq();

        log.info("报销单:{},封装凭证头数据==>开始", claim.getCode());
        SapCreateVoucherReq.SapVoucherHeader sapVoucherHeader = packageHeaderData(claim);
        request.setSapVoucherHeader(sapVoucherHeader);
        log.info("报销单:{},封装凭证头数据==>结束", claim.getCode());

        log.info("报销单:{},封装凭证行数据==>开始", claim.getCode());
        List<SapCreateVoucherReq.SapVoucherItem> sapVoucherItems = packageItemData(claim, request);
        log.info("报销单:{},封装凭证行数据==>结束", claim.getCode());

        request.setSapVoucherItems(sapVoucherItems);
        return request;
    }

    /**
     * 封装行数据
     * @param claim
     * @return
     */
    private List<SapCreateVoucherReq.SapVoucherItem> packageItemData(ExpClaimHeaderCommonVo claim,SapCreateVoucherReq requestData) {
        /*
            当前单据:EXP0000001412-YXDG02-对公应付
            前序单据有：EXP0000001409-YXDG01-对公预付、REQ0000002568-YXFY01-费用申请
        */
        //根据报销单单据类型，判断单据是否为预付款、或借款；显然YXDG02-对公应付，应该走【费用科目】封装的逻辑
        String headerTypeCode = claim.getHeaderTypeVo().getCode();//当前报销单类型
        //凭证行信息
        List<SapCreateVoucherReq.SapVoucherItem> sapVoucherItems = new ArrayList<>();
        //借款、预付款
        if (advanceHeaderTypeCodes.contains(headerTypeCode)) {
            //封装员工还款行
            sapVoucherItems = getAdvanceItems(claim,requestData);
        } else {//费用科目
            sapVoucherItems = getFineItems(claim, requestData);
        }
        return sapVoucherItems;
    }

    /**
     * 获取费用行的凭证行信息
     * @param claim
     * @return
     */
    private List<SapCreateVoucherReq.SapVoucherItem> getFineItems(ExpClaimHeaderCommonVo claim,SapCreateVoucherReq requestData) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        Date date = new Date();
        String documentNum = claim.getCode();
        UserApiVo chargeUserVo = claim.getSubmitUserVo();//对私报销的话，单据提交人是支付对象
        //查询员工对应的供应商编码
        UserFindByCodeRes userRes = getUserByCode(chargeUserVo.getEmployeeNumber());
        log.info("支付对象信息{}:{},{}", chargeUserVo.getEmployeeNumber(), JSONUtil.toJsonStr(chargeUserVo),JSONUtil.toJsonStr(userRes));
        DepartmentVo branchVo = claim.getBranchVo();
        ProjectVo projectVo = claim.getProjectVo();
        ExpHeaderTypeVo headerTypeVo = claim.getHeaderTypeVo();
        String headerTypeCode = headerTypeVo.getCode();//单据类型
        //凭证行信息
        List<SapCreateVoucherReq.SapVoucherItem> sapVoucherItems = new ArrayList<>();
        //先找到报销单的一个费用行
        ExpClaimLineCommonVo payLine = null;
        for (ExpClaimLineCommonVo lineCommonVo : claim.getExpClaimLineVoList()) {
            String internalType = lineCommonVo.getInternalType();
            if (!"advance".equals(internalType)) {
                payLine = lineCommonVo;
            }
        }
        //查询支付单行对应的报销单行
        List<ExpClaimLineCommonVo> linkLines = getLinkLines(payLine, claim);
        //判断是否存在借款、预付款冲抵行，如果存在，过账码、特别总账标识以及新增贷方
        //2024-12-04对公预付、对公应付的支付对象信息都在单据头上
        String payObject = StrUtil.isNotEmpty(payLine.getPayObject()) ? payLine.getPayObject() : claim.getPayObject();
        ExpClaimLineCommonVo advanceLine = getAdvanceLine(claim, payObject);
        //115522: 获取单据行中对私支付对象
        if ("U".equals(payObject)) {//员工报销
            if (containPayObjectTypes.contains(headerTypeCode)) {//前序报销单类型
                log.info("当前报销单{}的支付对象取单据头的submitUser,支付对象信息为:{}",documentNum,JSONUtil.toJsonStr(chargeUserVo));
            } else {
                chargeUserVo = payLine.getPayUserVo();
                log.info("当前报销单{}的支付对象取单据行的payUser,支付对象信息为:{}",documentNum,JSONUtil.toJsonStr(chargeUserVo));
                userRes = getUserByCode(chargeUserVo.getEmployeeNumber());

                //5.BKTXT:凭证抬头文本：表单类型名称前两个字+申请人工号+申请人名称+报销金额（total_amount）
                String linkTypeName = claim.getHeaderTypeVo().getType().split("-")[1];
                //凭证头全部先取单据提交人，随后再进行支付对象的更新
                BigDecimal totalAmount = BigDecimal.ZERO;//支付单的total_amount
                if ("YXJK03".equals(headerTypeCode)) {//员工还款-KZ
                    totalAmount = claim.getAdvanceAmount();
                } else {
                    totalAmount = claim.getTotalAmount();
                }
                String BKTXT = linkTypeName.substring(0, 2) + chargeUserVo.getEmployeeNumber() + chargeUserVo.getFullName() + totalAmount;
                requestData.getSapVoucherHeader().setBKTXT(BKTXT);
                log.info("支付对象信息{}:{},{}", chargeUserVo.getEmployeeNumber(), JSONUtil.toJsonStr(chargeUserVo),JSONUtil.toJsonStr(userRes));
            }
        }

        //115522: 获取单据行中对私支付对象
        if ("U".equals(payObject)) {//员工报销
            if (containPayObjectTypes.contains(headerTypeCode)) {//前序报销单类型
                log.info("当前报销单{}的支付对象取单据头的submitUser,支付对象信息为:{}",documentNum,JSONUtil.toJsonStr(chargeUserVo));
            } else {
                chargeUserVo = payLine.getPayUserVo();
                log.info("当前报销单{}的支付对象取单据行的payUser,支付对象信息为:{}",documentNum,JSONUtil.toJsonStr(chargeUserVo));
                userRes = getUserByCode(chargeUserVo.getEmployeeNumber());

                //5.BKTXT:凭证抬头文本：表单类型名称前两个字+申请人工号+申请人名称+报销金额（total_amount）
                String linkTypeName = claim.getHeaderTypeVo().getType().split("-")[1];
                //凭证头全部先取单据提交人，随后再进行支付对象的更新
                BigDecimal totalAmount = BigDecimal.ZERO;//支付单的total_amount
                if ("YXJK03".equals(headerTypeCode)) {//员工还款-KZ
                    totalAmount = claim.getAdvanceAmount();
                } else {
                    totalAmount = claim.getTotalAmount();
                }
                String BKTXT = linkTypeName.substring(0, 2) + chargeUserVo.getEmployeeNumber() + chargeUserVo.getFullName() + totalAmount;
                requestData.getSapVoucherHeader().setBKTXT(BKTXT);
                log.info("支付对象信息{}:{},{}", chargeUserVo.getEmployeeNumber(), JSONUtil.toJsonStr(chargeUserVo),JSONUtil.toJsonStr(userRes));
            }
        }

        //贷方金额(含税金额)
        BigDecimal crTotalPayAmount = BigDecimal.ZERO;
        String sapCC = "";//成本中心
        boolean isExistSapCC = true;//贷方是否传SAP成本中心，当费用行科目是********** /********** 合并的贷方行也不传
        for (ExpClaimLineCommonVo linkLine : linkLines) {
            log.info("报销单封装凭证行数据:{}({}):{}==>开始", claim.getCode(), documentNum, linkLine.getLineId());
            //获取SAP成本中心信息
            JSONObject sapCostCenter = getLovByName("SAP_Cost_Center", linkLine.getColumn30());
            if (sapCostCenter == null) {
                log.info("报销单:{},封装凭证行数据时，获取SAP成本中心异常！", claim.getCode());
                throw new RuntimeException("获取SAP成本中心异常！");
            }
            sapCC = branchVo.getCode() + sapCostCenter.getStr("code");
            String sapCCName = sapCostCenter.getStr("value_meaning");
            String column41 = claim.getColumn41();//产品管线
            //判断当前行是否含税:根据财务调整后的税额fin_tax_amount判断(#110600)
            boolean isExistTax = isExistTax(linkLine);
            //20250321: AK104默认税额为0,即默认不含税
            isExistTax = "AK104".equals(column41) ? false : isExistTax;

            //判断当前行是否需要排除进项税(只要有税额的都是进项税)，判断条件：单据类型、产品管线、费用类型
            String lineTypeCode = linkLine.getExpTypeVo().getCode();
            String lineType = linkLine.getExpTypeVo().getType();
            //进项税是否抵扣
            boolean isExcludeInputTax = excludeInputTax(headerTypeCode, column41, lineTypeCode, lineType, linkLine.getLineId());
            log.info("报销单:{},单据行:{},是否含有进项税{},是否抵扣进项税:{}", documentNum, linkLine.getLineId(), isExistTax, isExcludeInputTax);

            //当税额不得抵扣，系统识别出税额有调整值，不含税金额fin_net_amount和报销金额fin_claim_amount不相等时不生成凭证
            if (!isExcludeInputTax) {//不可抵扣进项税 && 财务调整后税额不为0
                log.info("报销单:{}行{}中进项税不可抵扣isExcludeInputTax:{},不含税金额为{}元,实际报销金额为{}元"
                        ,claim.getCode(),linkLine.getLineId(),isExcludeInputTax,linkLine.getFinNetAmount(),linkLine.getFinClaimAmount());
                if (linkLine.getFinNetAmount().compareTo(linkLine.getFinClaimAmount()) != 0) {
                    String errorMsg = "报销单" + claim.getCode() + "中存在不可抵扣进行税的行,且该行不含税金额"
                            + linkLine.getFinNetAmount() + "和实际报销金额"+linkLine.getFinClaimAmount()+"不相等";
                    log.info("单据中存在进项税不可抵扣，但调整后税额不为0的费用行!");
                    throw new RuntimeException(errorMsg);
                }
            }
            if (linkLine.getDrAccountId() != null) {//借方
                log.info("报销单封装凭证行数据:{}({}):{}==>借方开始", claim.getCode(), documentNum, linkLine.getLineId());
                SapCreateVoucherReq.SapVoucherItem item = new SapCreateVoucherReq.SapVoucherItem();
                //1.String BSCHL = "40";
                String BSCHL = getBSCHL(headerTypeCode, 1);//过账码

                //2.LIFNR:供应商
                String LIFNR = "";
                if ("S".equals(payObject)) {
                    LIFNR = linkLine.getSupplierVo() != null ? linkLine.getSupplierVo().getCode() : claim.getSupplierVo().getCode();
                } else {
                    LIFNR = userRes.getColumn1();
                }
                String HKONT = linkLine.getDrAccount().getCode();
                if (!isExistHKONT.contains(BSCHL)) {
                    //3.借方取值dr_account_id(报销单行):供应商的借方会计科目不取值
                    item.setHKONT(HKONT);
                }

                //4.WRBTR金额(报销单行不含税金额)
                BigDecimal finNetAmount = linkLine.getFinNetAmount();//#110600：如果不可抵扣进项税,那fin_tax_amount ==0,也就不用再累计到借方金额
                /*if (isExistTax && !isExcludeInputTax) {//不抵扣进项税
                    finNetAmount = finNetAmount.add(linkLine.getFinTaxAmount());
                }*/
                crTotalPayAmount = crTotalPayAmount.add(linkLine.getFinClaimAmount());////#110600：贷方金额取财务调整后报销金额fin_claim_amount
                BigDecimal WRBTR = finNetAmount.setScale(2, RoundingMode.HALF_UP);
                //5.SGTXT:借方会计科目行：SAP成本中心名字（line.column21）+申请人姓名+“报销”+会计科目名称
                String SGTXT = documentNum + sapCCName + chargeUserVo.getFullName()+"报销"+linkLine.getDrAccount().getAccountName();
                //6.KOSTL:成本中心(SAP成本中心代码)(报销单行)
                //6.KOSTL:成本中心(SAP成本中心代码)(报销单行)
                String KOSTL = "";
                if (!"**********".equals(HKONT) && !"**********".equals(HKONT)) {
                    KOSTL = sapCC;//#113716当费用行科目是********** /********** 不传输 成本中心字段
                } else {
                    isExistSapCC = false;
                }
                //7.RSTGR:现金流量原因代码
                String RSTGR = "";
                //UMSKZ:特别总账标识
                String UMSKZ = "";
                //MWSKZ:税码
                String MWSKZ = "";
                if (isExistTax && isExcludeInputTax) {
                    MWSKZ = linkLine.getTaxCode();
                }
                //AUFNR:内部订单号:当单据head.column46有值时，传head.column102
                /*String projectCode = projectVo == null? "" : projectVo.getCode();
                String AUFNR = "";
                if ("IIT".equals(projectCode) || "AK112".equals(projectCode) || "AK104".equals(projectCode)) {
                    if (StrUtil.isNotEmpty(claim.getColumnJson())){
                        AUFNR = JSONUtil.parseObj(claim.getColumnJson()).getStr("column102");
                    }
                }*/
                String AUFNR = "";
                if (StrUtil.isNotEmpty(claim.getColumn46()) && StrUtil.isNotEmpty(claim.getColumnJson())) {
                    AUFNR = JSONUtil.parseObj(claim.getColumnJson()).getStr("column102");
                }
                if (StrUtil.isEmpty(AUFNR) && column102HeaderTypeCodes.contains(headerTypeCode)){
                    //内部订单号必填
                    log.info("报销单封装凭证行数据:{}({}):{}==>内部订单号必填", claim.getCode(), documentNum, linkLine.getLineId());
                    throw new RuntimeException("内部订单号必填！");
                }
                //ZUONR:当凭证行为进项税科目时，传发票号；其他为单号(支付单)
                String ZUONR = claim.getCode();
                //ZFBDT:付款起算日 推单日期
                String ZFBDT = sdf.format(date);
                //HBKID:开户行-康方公司的账户开户行，有银行科目必填，目前不涉及
                String HBKID = "";
                //HKTID:账户标识-康方公司的账户开户行账号的后5位，有银行科目必填，目前不涉及
                String HKTID = "";
                //BVTYP:对方开户行类型-单据头或行supplier_account_id哪个有值传哪个-供应商账号的尾号4位
                String BVTYP = "";
                SupplierAccountVo supplierAccountVo = payLine.getSupplierAccountVo();
                if (supplierAccountVo != null) {
                    BVTYP = getSupplierAccountByCode(supplierAccountVo.getCode());//获取供应商序号
                    if (StrUtil.isEmpty(BVTYP)) {
                        String errorMsg = "查询供应商账号:"+supplierAccountVo.getCode()+"对应的SAP序号失败!";
                        log.info("报销单封装凭证行数据:{}({}):{}==>异常:{}", claim.getCode(), documentNum, linkLine.getLineId(), errorMsg);
                        throw new RuntimeException(errorMsg);
                    }
                } else if (claim.getSupplierAccountVo() != null){
                    BVTYP = getSupplierAccountByCode(claim.getSupplierAccountVo().getCode());//获取供应商序号
                    if (StrUtil.isEmpty(BVTYP)) {
                        String errorMsg = "查询供应商账号:"+claim.getSupplierAccountVo().getCode()+"对应的SAP序号失败!";
                        log.info("报销单封装凭证行数据:{}({}):{}==>异常:{}", claim.getCode(), documentNum, linkLine.getLineId(), errorMsg);
                        throw new RuntimeException(errorMsg);
                    }
                }

                item.setBSCHL(BSCHL);
                item.setLIFNR(LIFNR);
                item.setWRBTR(WRBTR);
                item.setSGTXT(SGTXT);
                item.setKOSTL(KOSTL);
                item.setRSTGR(RSTGR);
                item.setUMSKZ(UMSKZ);
                item.setMWSKZ(MWSKZ);
                item.setAUFNR(AUFNR);
                item.setZUONR(ZUONR);
                item.setZFBDT(ZFBDT);
                item.setHKTID(HKTID);
                item.setHBKID(HBKID);
                item.setBVTYP(BVTYP);
                item.setZLSCH("T");
                sapVoucherItems.add(item);
                log.info("支付单封装凭证行数据:{}({}):{}==>借方结束", claim.getCode(), documentNum, linkLine.getLineId());
            }

            if (isExistTax && isExcludeInputTax) {//#110600：可抵扣进项税，且有税额
                log.info("支付单封装凭证行数据:{}({}):{}==>税方开始", claim.getCode(), documentNum, linkLine.getLineId());
                SapCreateVoucherReq.SapVoucherItem item = new SapCreateVoucherReq.SapVoucherItem();
                //1.BSCHL:过账码
                //String BSCHL = "40";
                String BSCHL = getBSCHL(headerTypeCode, 2);//过账码
                //2.HKONT:会计科目税方会计科目tax_account_id（判断是否传值逻辑：fin_tax_amount为0或空不传;
                //以及满足【进项税特殊处理逻辑】sheet的逻辑也不传）
                String HKONT = "";
                if (linkLine.getFinTaxAmount() != null && linkLine.getFinTaxAmount().compareTo(BigDecimal.ZERO) != 0) {
                    HKONT = linkLine.getTaxAccount() == null ? "**********" : linkLine.getTaxAccount().getCode();
                }
                //3.LIFNR:供应商
                String LIFNR = "";
                if ("S".equals(payObject)) {
                    LIFNR = linkLine.getSupplierVo() != null ? linkLine.getSupplierVo().getCode() : claim.getSupplierVo().getCode();
                } else {
                    LIFNR = userRes.getColumn1();
                }

                //4.WRBTR:金额(税额)
                BigDecimal finTaxAmount = linkLine.getFinTaxAmount();
                //crTotalPayAmount = crTotalPayAmount.add(finTaxAmount);
                BigDecimal WRBTR = finTaxAmount.setScale(2, RoundingMode.HALF_UP);
                //5.SGTXT:借方会计科目行：SAP成本中心名字（line.column21）+申请人姓名+“报销”+会计科目名称
                String SGTXT = documentNum + sapCCName + chargeUserVo.getFullName()+"报销"+linkLine.getDrAccount().getAccountName();
                //6.KOSTL:成本中心(SAP成本中心代码)
                String KOSTL = sapCC;
                //RSTGR:现金流量原因代码
                String RSTGR = "";
                //7.MWSKZ:税码
                String MWSKZ = linkLine.getTaxCode();
                //AUFNR:内部订单号
                //8.ZUONR:当凭证行为进项税科目时，传发票号；其他为单号
                //发票号码有可能超过18位，若超过,SAP将会报错，因此需要将超过的部分存入XREF2字段
                String invoiceNum = linkLine.getInvoiceNum();
                String ZUONR = invoiceNum;
                String XREF2 = "";
                if (StrUtil.isNotEmpty(invoiceNum) && invoiceNum.length() > 18) {//行发票号不为空，且发票号码超过18位
                    ZUONR = invoiceNum.substring(0, 18);
                    XREF2 = invoiceNum.substring(18);
                }
                //ZFBDT:付款起算日 推单日期
                String ZFBDT = sdf.format(date);
                //HBKID:开户行-康方公司的账户开户行，有银行科目必填，目前不涉及
                String HBKID = "";
                //HKTID:账户标识-康方公司的账户开户行账号的后5位，有银行科目必填，目前不涉及
                String HKTID = "";
                //BVTYP:对方开户行类型-单据头或行supplier_account_id哪个有值传哪个-供应商账号的尾号4位
                String BVTYP = "";
                SupplierAccountVo supplierAccountVo = payLine.getSupplierAccountVo();
                if (supplierAccountVo != null) {
                    BVTYP = getSupplierAccountByCode(supplierAccountVo.getCode());//获取供应商序号
                    if (StrUtil.isEmpty(BVTYP)) {
                        String errorMsg = "查询供应商账号:"+supplierAccountVo.getCode()+"对应的SAP序号失败!";
                        log.info("报销单封装凭证行数据:{}({}):{}==>异常:{}", claim.getCode(), documentNum, linkLine.getLineId(), errorMsg);
                        throw new RuntimeException(errorMsg);
                    }
                } else if (claim.getSupplierAccountVo() != null){
                    BVTYP = getSupplierAccountByCode(claim.getSupplierAccountVo().getCode());//获取供应商序号
                    if (StrUtil.isEmpty(BVTYP)) {
                        String errorMsg = "查询供应商账号:"+claim.getSupplierAccountVo().getCode()+"对应的SAP序号失败!";
                        log.info("报销单封装凭证行数据:{}({}):{}==>异常:{}", claim.getCode(), documentNum, linkLine.getLineId(), errorMsg);
                        throw new RuntimeException(errorMsg);
                    }
                }

                item.setBSCHL(BSCHL);
                item.setHKONT(HKONT);
                item.setLIFNR(LIFNR);
                item.setWRBTR(WRBTR);
                item.setSGTXT(SGTXT);
                item.setKOSTL(KOSTL);
                item.setRSTGR(RSTGR);
                item.setMWSKZ(MWSKZ);
                item.setZUONR(ZUONR);
                item.setXREF2(XREF2);
                item.setZFBDT(ZFBDT);
                item.setHKTID(HKTID);
                item.setHBKID(HBKID);
                item.setBVTYP(BVTYP);
                item.setZLSCH("T");
                sapVoucherItems.add(item);
            }
            log.info("支付单封装凭证行数据:{}({}):{}==>税方结束", claim.getCode(), documentNum, linkLine.getLineId());
        }
        ExpClaimLineCommonVo supplierLine = linkLines.get(0);
        //冲抵行新增一行贷方:供应商的贷方不传会计科目
        if (advanceLine != null) {
            log.info("支付单封装凭证行数据:{}({}):{}==>冲抵行新增贷方开始", claim.getCode(), documentNum, advanceLine.getLineId());
            SapCreateVoucherReq.SapVoucherItem advanceItem = new SapCreateVoucherReq.SapVoucherItem();
            //1.BSCHL:过账码
            String advancedTypeCode = advanceLine.getExpTypeVo().getCode();
            if ("J001".equals(advancedTypeCode)) {//员工借款还款
                advanceItem.setBSCHL("39");
                //2.UMSKZ:特别总账标识
                advanceItem.setUMSKZ("G");
                //3.HKONT会计科目：贷方取值 cr_account_id
                advanceItem.setHKONT("**********");//其它应收款员工
                //6.SGTXT:借方会计科目行：SAP成本中心（line.column21）+申请人姓名+“报销”+会计科目名称
                String typeName = claim.getHeaderTypeVo().getType().split("-")[1];
                String SGTXT = documentNum + typeName.substring(0,2) + chargeUserVo.getFullName()+"员工借款还款";
                advanceItem.setSGTXT(SGTXT);
                //4.LIFNR:供应商
                String LIFNR = userRes.getColumn1();
                advanceItem.setLIFNR(LIFNR);
            }
            if ("A001".equals(advancedTypeCode)) {//对公预付款(供应商贷方，不传会计科目)
                advanceItem.setBSCHL("39");
                //2.UMSKZ:特别总账标识
                //advanceItem.setUMSKZ("F");
                advanceItem.setUMSKZ("C");
                //advanceItem.setZUMSK("C");
                //3.HKONT会计科目：贷方取值 cr_account_id
                //advanceItem.setHKONT("**********");//预付账款-非关联方
                //6.SGTXT:借方会计科目行：SAP成本中心（line.column21）+申请人姓名+“报销”+会计科目名称
                String typeName = claim.getHeaderTypeVo().getType().split("-")[1];
                String SGTXT = documentNum + typeName.substring(0,2) + chargeUserVo.getFullName()+"对公预付冲抵";
                advanceItem.setSGTXT(SGTXT);
                //4.LIFNR:供应商
                if ("YXDG02".equals(headerTypeCode)) {//对公应付
                    advanceItem.setLIFNR(claim.getSupplierVo().getCode());
                } else {
                    advanceItem.setLIFNR(supplierLine.getSupplierVo().getCode());
                }
            }

            //ZUONR:存在预付或者借款冲抵的，需要传关联冲抵的单号
            String ZUONR = "";
            for (ExpClaimHeaderCommonVo headerLink : claim.getHeaderLinks()) {
                String headerLinkTypeCode = headerLink.getHeaderTypeVo().getCode();
                if (advanceHeaderTypeCodes.contains(headerLinkTypeCode)) {
                    ZUONR = getAdvanceClaim(headerLink.getCode());
                }
            }
            if (StrUtil.isEmpty(ZUONR)) {
                String errorMsg = "查询报销单:"+claim.getCode()+"关联的前序冲抵单据异常!";
                log.info("报销单封装凭证行数据:{}({}):{}==>异常:{}", claim.getCode(), documentNum, advanceLine.getLineId(), errorMsg);
                throw new RuntimeException(errorMsg);
            }
            advanceItem.setZUONR(ZUONR);
            //5.WRBTR:金额
            BigDecimal advanceAmount = claim.getAdvanceAmount();
            crTotalPayAmount = crTotalPayAmount.subtract(advanceAmount);
            BigDecimal WRBTR = advanceAmount.setScale(2, RoundingMode.HALF_UP);
            advanceItem.setWRBTR(WRBTR);
            //7.KOSTL:成本中心
            //8.AUFNR:内部订单号
            //9.ZFBDT:付款起算日 推单日期
            advanceItem.setZFBDT(sdf.format(date));
            //10.HBKID:开户行-康方公司的账户开户行，有银行科目必填，目前不涉及
            //11.HKTID:账户标识-康方公司的账户开户行账号的后5位，有银行科目必填，目前不涉及
            //12.BVTYP:对方开户行类型-单据头或行supplier_account_id哪个有值传哪个-供应商账号的尾号4位
            String BVTYP = "";
            SupplierAccountVo supplierAccountVo = claim.getSupplierAccountVo();
            if (supplierAccountVo != null) {
                BVTYP = getSupplierAccountByCode(supplierAccountVo.getCode());//获取供应商序号
                if (StrUtil.isEmpty(BVTYP)) {
                    String errorMsg = "查询供应商账号:"+supplierAccountVo.getCode()+"对应的SAP序号失败!";
                    log.info("报销单封装凭证行数据:{}({}):{}==>异常:{}", claim.getCode(), documentNum, advanceLine.getLineId(), errorMsg);
                    throw new RuntimeException(errorMsg);
                }
            } else if (claim.getSupplierAccountVo() != null){
                BVTYP = getSupplierAccountByCode(claim.getSupplierAccountVo().getCode());//获取供应商序号
                if (StrUtil.isEmpty(BVTYP)) {
                    String errorMsg = "查询供应商账号:"+claim.getSupplierAccountVo().getCode()+"对应的SAP序号失败!";
                    log.info("报销单封装凭证行数据:{}({}):{}==>异常:{}", claim.getCode(), documentNum, advanceLine.getLineId(), errorMsg);
                    throw new RuntimeException(errorMsg);
                }
            }
            advanceItem.setBVTYP(BVTYP);
            advanceItem.setZLSCH("T");
            sapVoucherItems.add(advanceItem);
            log.info("支付单封装凭证行数据:{}({}):{}==>冲抵行新增贷方结束", claim.getCode(), documentNum, advanceLine.getLineId());
        }

        //贷方科目合并:金额合并(贷方不含税码)
        ExpClaimLineCommonVo crAccountLine = linkLines.get(0);
        log.info("支付单封装凭证行数据:{}==贷方开始", claim.getCode() + documentNum);
        if (crAccountLine.getCrAccountId() != null && BigDecimal.ZERO.compareTo(crTotalPayAmount) != 0) {//贷方
            log.info("支付单封装凭证行数据:{}({}):{}==>贷方开始", claim.getCode(), documentNum, crAccountLine.getLineId());
            SapCreateVoucherReq.SapVoucherItem item = new SapCreateVoucherReq.SapVoucherItem();
            //1.BSCHL:过账码
            //String BSCHL = "31";
            String BSCHL = getBSCHL(headerTypeCode, 3);//过账码
            //3.LIFNR:供应商
            String LIFNR = "";
            if ("S".equals(payObject)) {//供应商贷方不取会计科目
                LIFNR = crAccountLine.getSupplierVo() != null ? crAccountLine.getSupplierVo().getCode() : claim.getSupplierVo().getCode();
            } else {
                LIFNR = userRes.getColumn1();
            }
            if (!isExistHKONT.contains(BSCHL)) {
                //2.会计科目：贷方取值 cr_account_id
                String HKONT = crAccountLine.getCrAccount().getCode();
                item.setHKONT(HKONT);
            }
            //4.WRBTR:金额
            BigDecimal totalPayAmount = crTotalPayAmount;
            BigDecimal WRBTR = totalPayAmount.setScale(2, RoundingMode.HALF_UP);
            //5.SGTXT:借方会计科目行：SAP成本中心（line.column21）+申请人姓名+“报销”+会计科目名称
            String typeName = claim.getHeaderTypeVo().getType().split("-")[1];
            String SGTXT = documentNum + typeName.substring(0, 2) + claim.getCode();
            //6.KOSTL:成本中心
            String KOSTL = isExistSapCC ? sapCC : "";
            //7.RSTGR:现金流量原因代码
            String RSTGR = "";
            //8.MWSKZ:税码(贷方不传)
            //9.AUFNR:内部订单号
            //ZUONR:当凭证行为进项税科目时，传发票号；其他为单号
            String ZUONR = claim.getCode();
            //ZFBDT:付款起算日 推单日期
            String ZFBDT = sdf.format(date);
            //HBKID:开户行-康方公司的账户开户行，有银行科目必填，目前不涉及
            String HBKID = "";
            //HKTID:账户标识-康方公司的账户开户行账号的后5位，有银行科目必填，目前不涉及
            String HKTID = "";
            //BVTYP:对方开户行类型-单据头或行supplier_account_id哪个有值传哪个-供应商账号的尾号4位
            String BVTYP = "";
            SupplierAccountVo supplierAccountVo = crAccountLine.getSupplierAccountVo();
            if (supplierAccountVo != null) {
                BVTYP = getSupplierAccountByCode(supplierAccountVo.getCode());//获取供应商序号
                if (StrUtil.isEmpty(BVTYP)) {
                    String errorMsg = "查询供应商账号:"+supplierAccountVo.getCode()+"对应的SAP序号失败!";
                    log.info("报销单封装凭证行数据:{}({}):{}==>异常:{}", claim.getCode(), documentNum, crAccountLine.getLineId(), errorMsg);
                    throw new RuntimeException(errorMsg);
                }
            } else if (claim.getSupplierAccountVo() != null){
                BVTYP = getSupplierAccountByCode(claim.getSupplierAccountVo().getCode());//获取供应商序号
                if (StrUtil.isEmpty(BVTYP)) {
                    String errorMsg = "查询供应商账号:"+claim.getSupplierAccountVo().getCode()+"对应的SAP序号失败!";
                    log.info("报销单封装凭证行数据:{}({}):{}==>异常:{}", claim.getCode(), documentNum, crAccountLine.getLineId(), errorMsg);
                    throw new RuntimeException(errorMsg);
                }
            }

            item.setBSCHL(BSCHL);
            item.setLIFNR(LIFNR);
            item.setWRBTR(WRBTR);
            item.setSGTXT(SGTXT);
            item.setKOSTL(KOSTL);
            item.setRSTGR(RSTGR);
            item.setZUONR(ZUONR);
            item.setZFBDT(ZFBDT);
            item.setHKTID(HKTID);
            item.setHBKID(HBKID);
            item.setBVTYP(BVTYP);
            item.setZLSCH("T");
            sapVoucherItems.add(item);
            //#120599 新增贷方金额与单据总金额的校验
            if (WRBTR.compareTo(claim.getTotalAmount()) != 0) {
                log.info("报销单{}凭证推送的贷方总金额与单据金额不同，贷方总金额：{}元,单据金额:{}元", claim.getCode(), WRBTR, claim.getTotalAmount());
                String errorMsg = "报销单凭证推送的贷方总金额与单据金额不同!";
                throw new RuntimeException(errorMsg);
            }
            log.info("报销单封装凭证行数据:{}({}):{}==>贷方结束", claim.getCode(), documentNum, crAccountLine.getLineId());
        }
        return sapVoucherItems;
    }

    private boolean excludeInputTax(String headerTypeCode, String column41, String lineTypeCode, String lineType, Integer lineId) {
        log.info("单据行:{},费用类型为{}({}),单据类型为{},产品管线是{}", lineId, lineType, lineTypeCode, headerTypeCode, column41);
        //1、产品管线AK112且会议核报（YXHY02-会议核报、ZRHY02-会议核报-准入），
        if ("AK112".equals(column41) || "AK101".equals(column41) || "AK102".equals(column41)) {
            if ("YXFY02".equals(headerTypeCode) || "ZRFY02".equals(headerTypeCode) || "YXZR02".equals(headerTypeCode)) {
                //1.1费用报销（YXFY02-费用报销、ZRFY02-费用报销-准入）、准入核报(YXZR02-准入核报)，除办公费F002外，均不可抵扣
                if ("F002".equals(lineTypeCode)) {
                    return true;//#115522：新增当前三种单据中办公费F002可以抵扣，其它费用均不可抵扣(2025年6月1日)
                }
                return false;
            } else if ("YXHY02".equals(headerTypeCode) || "ZRHY02".equals(headerTypeCode)) {
                //1.2会议核报（YXHY02-会议核报、ZRHY02-会议核报-准入）
                if ("H001".equals(lineTypeCode) || "H003".equals(lineTypeCode) || "H005".equals(lineTypeCode)) {
                    //1.2 费用类别（line_type_code)包含：场租H001、宣传物料H003、会议服务费H005，进项税可以抵扣，其它不可以抵扣
                    return true;
                } else {
                    return false;
                }
            } else {//未明确标注的场景都需要抵扣
                return true;
            }
        }
        //2、产品管线AK104的所有情况均不允许抵扣。
        if ("AK104".equals(column41)) {
            log.info("单据行:{},费用类型为{}({}),单据类型为{},产品管线是{},产品管线AK104的所有情况均不允许抵扣", lineId, lineType, lineTypeCode, headerTypeCode, column41);
            return false;
        }
        return true;
    }

    /**
     * 封装员工借款行(只含借、贷)
     * @param claim 支付单
     * @return
     */
    private List<SapCreateVoucherReq.SapVoucherItem> getAdvanceItems(ExpClaimHeaderCommonVo claim,SapCreateVoucherReq requestData) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        Date date = new Date();
        String documentNum = claim.getCode();//当前报销单号
        UserApiVo chargeUserVo = claim.getChargeUserVo();
        //查询员工对应的供应商编码
        UserFindByCodeRes userRes = getUserByCode(chargeUserVo.getEmployeeNumber());
        DepartmentVo branchVo = claim.getBranchVo();
        ProjectVo projectVo = claim.getProjectVo();
        ExpHeaderTypeVo headerTypeVo = claim.getHeaderTypeVo();
        String headerTypeCode = headerTypeVo.getCode();//当前报销单类型

        List<SapCreateVoucherReq.SapVoucherItem> sapVoucherItems = new ArrayList<>();
        //借款或预付款行
        //ExpClaimLineCommonVo advanceLine = headerLinkClaim.getExpClaimLineVoList().get(0);
        ExpClaimLineCommonVo line = null;

        for (ExpClaimLineCommonVo lineCommonVo : claim.getExpClaimLineVoList()) {
            String internalType = lineCommonVo.getInternalType();
            if (!"advance".equals(internalType)) {
                line = lineCommonVo;
            }
        }

        //获取SAP成本中心信息
        String sapCC = "";
        JSONObject sapCostCenter = getLovByName("SAP_Cost_Center", line.getColumn30());
        if (sapCostCenter == null) {
            log.info("支付单:{},封装凭证行数据时，获取SAP成本中心异常！", claim.getCode());
            //throw new RuntimeException("获取SAP成本中心异常！");
        } else {
            sapCC = branchVo.getCode() + sapCostCenter.getStr("code");
        }

        //借会计科目:记账码29+特别总账标G：其他应收款-员工
        if (line.getDrAccountId() != null) {
            log.info("报销单封装凭证行数据:{}({}):{}==>借款借方开始", claim.getCode(), documentNum, line.getLineId());
            SapCreateVoucherReq.SapVoucherItem item = new SapCreateVoucherReq.SapVoucherItem();
            //员工借款
            if ("YXJK01".equals(headerTypeCode)) {//记账码29+特别总账标G：其他应收款-员工
                item.setBSCHL("29");//过账吗
                item.setUMSKZ("G");//特别总账标识
            }else if ("YXJK03".equals(headerTypeCode)) {//员工还款
                item.setBSCHL("40");//过账吗
                item.setHBKID(bankIndexMap.get(branchVo.getCode()));//银行标识
                item.setHKTID(accountIndexMap.get(branchVo.getCode()));//账户标识
                item.setRSTGR("C13");
            } else {
                item.setBSCHL("40");//过账码
            }
            //2.借方取值dr_account_id(报销单行)
            String HKONT = line.getDrAccount().getCode();
            if (!isExistHKONT.contains(item.getBSCHL())) {
                item.setHKONT(HKONT);
            }

            //3.LIFNR:供应商
            String LIFNR = userRes.getColumn1();
            //4.WRBTR金额(报销单行不含税金额)
            BigDecimal finNetAmount = line.getFinNetAmount();
            BigDecimal WRBTR = finNetAmount.setScale(2, RoundingMode.HALF_UP);
            //5.SGTXT:借方会计科目行：SAP成本中心名字（line.column21）+申请人姓名+“报销”+会计科目名称
            String SGTXT = documentNum  + chargeUserVo.getFullName()+"报销"+line.getDrAccount().getAccountName();
            //6.KOSTL:成本中心(SAP成本中心代码)(报销单行)
            String KOSTL = "";
            if (!"**********".equals(HKONT) && !"**********".equals(HKONT)) {
                KOSTL = sapCC;//#113716当费用行科目是********** /********** 不传输 成本中心字段
            }
            //UMSKZ:特别总账标识
            String UMSKZ = "";
            //MWSKZ:税码
            String MWSKZ = "";
            //AUFNR:内部订单号:当单据head.column46有值时，传head.column102
            /*String projectCode = projectVo == null? "" : projectVo.getCode();
            String AUFNR = "";
            if ("IIT".equals(projectCode) || "AK112".equals(projectCode) || "AK104".equals(projectCode)) {
                if (StrUtil.isNotEmpty(claim.getColumnJson())){
                    AUFNR = JSONUtil.parseObj(claim.getColumnJson()).getStr("column102");
                }
            }*/
            String AUFNR = "";
            if (StrUtil.isNotEmpty(claim.getColumn46()) && StrUtil.isNotEmpty(claim.getColumnJson())) {
                AUFNR = JSONUtil.parseObj(claim.getColumnJson()).getStr("column102");
            }
            if (StrUtil.isEmpty(AUFNR) && column102HeaderTypeCodes.contains(headerTypeCode)){
                //内部订单号必填
                log.info("报销单封装凭证行数据:{}({}):{}==>内部订单号必填", claim.getCode(), documentNum, line.getLineId());
                throw new RuntimeException("内部订单号必填！");
            }

            //ZUONR:当凭证行为进项税科目时，传发票号；其他为单号(支付单)
            String ZUONR = claim.getCode();
            //ZFBDT:付款起算日 推单日期
            String ZFBDT = sdf.format(date);
            //BVTYP:对方开户行类型-单据头或行supplier_account_id哪个有值传哪个-供应商账号的尾号4位
            String BVTYP = "";
            SupplierAccountVo supplierAccountVo = line.getSupplierAccountVo();
            if (supplierAccountVo != null) {
                BVTYP = getSupplierAccountByCode(supplierAccountVo.getCode());//获取供应商序号
                if (StrUtil.isEmpty(BVTYP)) {
                    String errorMsg = "查询供应商账号:"+supplierAccountVo.getCode()+"对应的SAP序号失败!";
                    log.info("报销单封装凭证行数据:{}({}):{}==>异常:{}", claim.getCode(), documentNum, line.getLineId(), errorMsg);
                    throw new RuntimeException(errorMsg);
                }
            } else if (claim.getSupplierAccountVo() != null){
                BVTYP = getSupplierAccountByCode(claim.getSupplierAccountVo().getCode());//获取供应商序号
                if (StrUtil.isEmpty(BVTYP)) {
                    String errorMsg = "查询供应商账号:"+claim.getSupplierAccountVo().getCode()+"对应的SAP序号失败!";
                    log.info("报销单封装凭证行数据:{}({}):{}==>异常:{}", claim.getCode(), documentNum, line.getLineId(), errorMsg);
                    throw new RuntimeException(errorMsg);
                }
            }

            item.setLIFNR(LIFNR);
            item.setWRBTR(WRBTR);
            item.setSGTXT(SGTXT);
            item.setKOSTL(KOSTL);
            item.setMWSKZ(MWSKZ);
            item.setAUFNR(AUFNR);
            item.setZUONR(ZUONR);
            item.setZFBDT(ZFBDT);
            item.setBVTYP(BVTYP);
            item.setZLSCH("T");
            sapVoucherItems.add(item);
            log.info("报销单封装凭证行数据:{}({}):{}==>借款借方结束", claim.getCode(), documentNum, line.getLineId());
        }

        //贷方会计科目(对公预付只有贷方)
        if (line.getCrAccountId() != null) {
            log.info("报销单封装凭证行数据:{}({}):{}==>借款或预付贷方开始", claim.getCode(), documentNum, line.getLineId());
            SapCreateVoucherReq.SapVoucherItem item = new SapCreateVoucherReq.SapVoucherItem();
            //1.BSCHL:过账码
            String BSCHL = "";//过账码
            if ("YXDG01".equals(headerTypeCode)) {//对公预付款
                BSCHL = "39";
                //2.UMSKZ:特别总账标识
                item.setUMSKZ("F");
                //3.LIFNR:供应商编码
                String LIFNR = claim.getSupplierVo().getCode();
                item.setLIFNR(LIFNR);
                //4.MWSKZ:税码(贷方不传)
                item.setMWSKZ("J0");
                //5.ZUMSK:目标特别总帐标志
                item.setZUMSK("C");
                requestData.set035(true);
                //2.会计科目：贷方取值 cr_account_id
                /*String HKONT = line.getCrAccount().getCode();
                item.setHKONT(HKONT);*/
            } else if ("YXJK03".equals(headerTypeCode)) {//员工还款
                //过账码
                BSCHL = "39";
                //2.UMSKZ:特别总账标识
                item.setUMSKZ("G");
                //3.LIFNR:成本中心+员工编码
                String LIFNR = userRes.getColumn1();
                item.setLIFNR(LIFNR);
            } else {
                //过账码
                BSCHL = getBSCHL(headerTypeCode, 3);
                //3.LIFNR:成本中心+员工编码
                String LIFNR = userRes.getColumn1();
                item.setLIFNR(LIFNR);
            }
            if (!isExistHKONT.contains(BSCHL)) {
                //2.会计科目：贷方取值 cr_account_id
                String HKONT = line.getCrAccount().getCode();
                item.setHKONT(HKONT);
            }

            item.setBSCHL(BSCHL);
            //4.WRBTR:金额
            BigDecimal WRBTR = claim.getAdvanceAmount().setScale(2, RoundingMode.HALF_UP);
            item.setWRBTR(WRBTR);//借款金额
            //5.SGTXT:借方会计科目行：SAP成本中心（line.column21）+申请人姓名+“报销”+会计科目名称
            String typeName = claim.getHeaderTypeVo().getType().split("-")[1];
            String SGTXT = documentNum + typeName.substring(0, 2) + claim.getCode();
            item.setSGTXT(SGTXT);
            //6.KOSTL:成本中心
            String KOSTL = sapCC;
            item.setKOSTL(KOSTL);
            //7.RSTGR:现金流量原因代码
            String RSTGR = "";
            item.setRSTGR(RSTGR);
            //8.AUFNR:内部订单号：只传借方(2024-12-26)
            /*String projectCode = projectVo == null? "" : projectVo.getCode();
            String AUFNR = "";
            if ("IIT".equals(projectCode) || "AK112".equals(projectCode) || "AK104".equals(projectCode)) {
                if (StrUtil.isNotEmpty(claim.getColumnJson())){
                    AUFNR = JSONUtil.parseObj(claim.getColumnJson()).getStr("column102");
                }
            }
            item.setAUFNR(AUFNR);*/
            //ZUONR:当凭证行为进项税科目时，传发票号；其他为单号
            String ZUONR = claim.getCode();
            item.setZUONR(ZUONR);
            //ZFBDT:付款起算日 推单日期
            String ZFBDT = sdf.format(date);
            item.setZFBDT(ZFBDT);
            //HBKID:开户行-康方公司的账户开户行，有银行科目必填，目前不涉及
            String HBKID = "";
            item.setHBKID(HBKID);
            //HKTID:账户标识-康方公司的账户开户行账号的后5位，有银行科目必填，目前不涉及
            String HKTID = "";
            item.setHKTID(HKTID);
            //BVTYP:对方开户行类型-单据头或行supplier_account_id哪个有值传哪个-供应商账号的尾号4位
            String BVTYP = "";
            SupplierAccountVo supplierAccountVo = line.getSupplierAccountVo();
            if (supplierAccountVo != null) {
                BVTYP = getSupplierAccountByCode(supplierAccountVo.getCode());//获取供应商序号
                if (StrUtil.isEmpty(BVTYP)) {
                    String errorMsg = "查询供应商账号:"+supplierAccountVo.getCode()+"对应的SAP序号失败!";
                    log.info("报销单封装凭证行数据:{}({}):{}==>异常:{}", claim.getCode(), documentNum, line.getLineId(), errorMsg);
                    throw new RuntimeException(errorMsg);
                }
            } else if (claim.getSupplierAccountVo() != null){
                BVTYP = getSupplierAccountByCode(claim.getSupplierAccountVo().getCode());//获取供应商序号
                if (StrUtil.isEmpty(BVTYP)) {
                    String errorMsg = "查询供应商账号:"+claim.getSupplierAccountVo().getCode()+"对应的SAP序号失败!";
                    log.info("报销单封装凭证行数据:{}({}):{}==>异常:{}", claim.getCode(), documentNum, line.getLineId(), errorMsg);
                    throw new RuntimeException(errorMsg);
                }
            }
            item.setBVTYP(BVTYP);
            item.setZLSCH("T");
            sapVoucherItems.add(item);
            log.info("支付单封装凭证行数据:{}({}):{}==>借款或预付贷方结束", claim.getCode(), documentNum, line.getLineId());
        }

        return sapVoucherItems;
    }

    /**
     * 获取预付款、借款行(每张报销单只能冲抵一次借款)
     * @param headerLinkClaim
     * @return
     */
    private ExpClaimLineCommonVo getAdvanceLine(ExpClaimHeaderCommonVo headerLinkClaim,String payObject) {
        List<ExpClaimLineCommonVo> linkLineVoList = headerLinkClaim.getExpClaimLineVoList();
        for (ExpClaimLineCommonVo linkLine : linkLineVoList) {
            String internalType = linkLine.getInternalType();
            /*if ("advance".equals(internalType)) {//预付或借款
                return linkLine;
            }*/
            if (!"advance".equals(internalType)) {//不是预付款或借款
                continue;
            }
            String advanceCode = linkLine.getExpTypeVo().getCode();
            if ("S".equals(payObject) && "A001".equals(advanceCode)) {//2024.12.03 对公冲抵预付款行类型编码由Y001改为A001
                return linkLine;
            }
            if ("U".equals(payObject) && "J001".equals(advanceCode)) {//对私借款冲抵
                return linkLine;
            }

        }
        return null;
    }

    /**
     * 判断当前费用行是否需要传税方科目
     * @param linkLine
     * @return
     */
    private boolean isExistTax(ExpClaimLineCommonVo linkLine) {
        //根据财务调整后的税额fin_tax_amount判断(不存在税方科目，有可能是系统没有带出来;如果此时含税就会出现少付的情况)
        /*//不存在税方科目
        if (linkLine.getTaxAccountId() == null) {
            return false;
        }*/
        //税额为0:税是否会出现负值的情况？
        if (linkLine.getFinTaxAmount() == null || linkLine.getFinTaxAmount().compareTo(BigDecimal.ZERO) == 0) {
            return false;
        }
        return true;
    }

    /**
     * 根据支付单信息去查询报销单对应的行信息
     * @param line
     * @param claim
     * @return
     */
    private List<ExpClaimLineCommonVo> getLinkLines(ExpClaimLineCommonVo line, ExpClaimHeaderCommonVo claim) {

        //报销单推送凭证
        List<ExpClaimLineCommonVo> returnedLinkList = new ArrayList<>();
        for (ExpClaimLineCommonVo linkLine : claim.getExpClaimLineVoList()) {
            String internalType = linkLine.getInternalType();
            if ("advance".equals(internalType)) {
                log.info("当前行是借款或预付款冲抵行lineId:{},{}", linkLine.getLineId(), JSONUtil.toJsonStr(linkLine.getExpTypeVo()));
                continue;
            }
            returnedLinkList.add(linkLine);
        }
        return returnedLinkList;
    }

    /**
     * 根据单据类型获取过账码
     * @param headerTypeCode
     * @param lineType
     * @return
     */
    private String getBSCHL(String headerTypeCode, int lineType) {
        String BSCHL = "";
        List<String> headerTypeCodes2 = Arrays.asList("YXCL02", "ZRCL02", "YXFY02", "ZRFY02","YXHY02", "ZRHY02", "YXZR02", "YXDG02");
        //YXCL02:差旅报销、ZRCL02:差旅报销准入、YXFY02:费用报销、ZRFY02：费用报销准入
        //YXHY02-会议核报、
        if (headerTypeCodes2.contains(headerTypeCode)) {
            if (lineType == 1 || lineType == 2) {//借、税
                BSCHL = "40";
            }
            if (lineType == 3) {//贷
                BSCHL = "31";
            }
        }
        //YXJK01	员工借款
        if ("YXJK01".equals(headerTypeCode)) {
            if (lineType == 1) {//借:记账码29+特别总账标G：其他应收款-员工
                return "29";
            }
            if (lineType == 3) {//贷：记账码31，无特别总账标，其他应付款-员工
                return "31";
            }
        }
        return BSCHL;
    }

    /**
     * 封装头数据
     * @param claim
     * @return
     */
    private SapCreateVoucherReq.SapVoucherHeader packageHeaderData(ExpClaimHeaderCommonVo claim) {
        SapCreateVoucherReq.SapVoucherHeader sapVoucherHeader = new SapCreateVoucherReq.SapVoucherHeader();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        Date date = new Date();
        String documentNum = claim.getCode();//当前报销单号
        ExpHeaderTypeVo headerTypeVo = claim.getHeaderTypeVo();
        String headerTypeCode = headerTypeVo.getCode();//当前报销单类型
        //1.BUKRS:公司代码：branch_code(支付单和报销单一致)
        String BUKRS = claim.getBranchVo().getCode();
        sapVoucherHeader.setBUKRS(BUKRS);
        //2.BLART:凭证类型：取申请人所在部门对应的片区，对应SAP凭证类型，参考sheet【片区对应凭证类型】(支付单和报销单一致)
        //2.所有凭证类型都推：YX，2024-12-02修改
        sapVoucherHeader.setBLART("YX");
        //3.BLDAT:凭证日期：必输，格式yyyymmdd，推单日期(程序执行时间)
        //4.BUDAT:过账日期：必输，格式yyyymmdd，推单日期(程序执行时间)
        String BLDAT = sdf.format(date);
        sapVoucherHeader.setBLDAT(BLDAT);
        String BUDAT = sdf.format(date);
        sapVoucherHeader.setBUDAT(BUDAT);
        //5.BKTXT:凭证抬头文本：表单类型名称前两个字+申请人工号+申请人名称+报销金额（total_amount）
        String typeName = headerTypeVo.getType().split("-")[1];
        //凭证头全部先取单据提交人，随后再进行支付对象的更新
        UserApiVo submitUserVo = claim.getSubmitUserVo();
        String employeeNumber = submitUserVo.getEmployeeNumber();
        String fullName = submitUserVo.getFullName();
        BigDecimal totalAmount = BigDecimal.ZERO;//支付单的total_amount
        if ("YXJK03".equals(headerTypeCode)) {//员工还款-KZ
            totalAmount = claim.getAdvanceAmount();
        } else {
            totalAmount = claim.getTotalAmount();
        }
        String BKTXT = typeName.substring(0, 2) + employeeNumber + fullName + totalAmount;
        sapVoucherHeader.setBKTXT(BKTXT);
        //6.WAERS:货币码：必输,默认CNY
        String WAERS = "CNY";
        sapVoucherHeader.setWAERS(WAERS);
        //7.KURSF:汇率：不传
        //8.XBLNR:OA单据编号(参考）：费控据编号(支付单)
        String XBLNR = documentNum;
        sapVoucherHeader.setXBLNR(XBLNR);
        //9.XREF1_HD:系统来源：默认FK
        String XREF1_HD = "FK";
        sapVoucherHeader.setXREF1_HD(XREF1_HD);
        //10.UNAME:用户名：单据财务BP(03)审核人工号(前序报销单审批流)
        List<SimpleFndWorkflowPathVo> workflowPathVos = claim.getWorkflowPathVos();
        if (workflowPathVos != null && workflowPathVos.size() >0) {
            String UNAME = getBPEmployeeNumber(workflowPathVos);
            sapVoucherHeader.setUNAME(UNAME);
        }
        //11.ZZJSBH1:发票编号1：单不传
        //12.XBLNR_ALT:备选参考编号(前序报销单)：head.type_code=YXHY02(会议核报)或ZRHY02(会议核报准入)，传head.column120
        if ("YXHY02".equals(headerTypeCode) || "ZRHY02".equals(headerTypeCode)|| "YXDG01".equals(headerTypeCode)) {
            if (StrUtil.isNotEmpty(claim.getColumnJson())){
                String XBLNR_ALT = JSONUtil.parseObj(claim.getColumnJson()).getStr("column120");
                sapVoucherHeader.setXBLNR_ALT(XBLNR_ALT);
            }
        }
        log.info("单据{}推送凭证，封装的头数据:{}", documentNum, JSONUtil.toJsonStr(sapVoucherHeader));
        return sapVoucherHeader;
    }

    /**
     * 获取审批流节点中财务BP审核人的员工号
     * @param workflowPathVos
     * @return
     */
    private String getBPEmployeeNumber(List<SimpleFndWorkflowPathVo> workflowPathVos) {
        for (SimpleFndWorkflowPathVo workflowPathVo : workflowPathVos) {
            String workflowStep = workflowPathVo.getWorkflowStep();
            if ("03".equals(workflowStep)) {
                //03:对应的是财务审核
                String employeeNumber = workflowPathVo.getUser().getEmployeeNumber();
                return employeeNumber;
            }
        }
        return "";
    }

    /**
     * 查询员工所在部门对应的片区(column2)
     * @param employeeNumber
     * @return
     */
    private String getBLART(String employeeNumber) {
        /*UserFindByCodeReq req = UserFindByCodeReq.builder().code(employeeNumber).bizId(UUID.randomUUID().toString()).timestamp(System.currentTimeMillis()).build();
        UserFindByCodeRes res = yjUserService.findByCode(req);*/
        String path = "/common/users/v2/findByCode?code=" + employeeNumber + "&bizId=" + UUID.randomUUID() + "&timestamp=" + System.currentTimeMillis();
        ServiceRes response = yjApiService.api(path, HttpMethod.GET, null, UserApiVo.class);

        JSONObject user = JSONUtil.parseArray(response.getData()).getJSONObject(0);
        JSONObject departmentVo = user.getJSONObject("departmentVo");
        String column2 = departmentVo.getStr("column2");
        return voucherTypes.get(column2);
    }


    @Override
    public SapCreateVoucherRes executeFunction(String name) throws JCoException {

        //1.获取函数RFC函数对象
        JCoFunction function = sapClient.getDestination().getRepository().getFunctionTemplate(name).getFunction();

        log.info("SAP方法{}的信息:{}",name, JSONUtil.toJsonStr(function));
        //2.设置输入参数（如果有）
        JCoTable head = function.getImportParameterList().getTable("IT_HEAD");
        head.appendRow();
        head.setValue("BUKRS","1050");
        head.setValue("BLART","Y3");
        head.setValue("BLDAT","20241022");
        head.setValue("BUDAT","20241022");
        head.setValue("BKTXT","差旅1000A02244_李萍347.0元");
        head.setValue("WAERS","CNY");
        head.setValue("XBLNR","YXCL-20241022TS1");
        head.setValue("XREF1_HD","FK");
        head.setValue("UNAME","B00586");


        JCoTable item = function.getImportParameterList().getTable("IT_ITEM");
        item.appendRow();
        item.setValue("BUZEI","001");
        item.setValue("BSCHL","40");
        item.setValue("HKONT","8003051400");
        item.setValue("WRBTR",167);
        item.setValue("SGTXT","东中国区李萍报销跨市交通费");
        item.setValue("KOSTL","1050S45110");
        item.setValue("MWSKZ","J0");
        item.setValue("ZUONR","YXCL-20241022TS1");
        item.setValue("ZFBDT","20241022");

        item.appendRow();
        item.setValue("BUZEI","002");
        item.setValue("BSCHL","40");
        item.setValue("HKONT","8002070100");
        item.setValue("WRBTR",180);
        item.setValue("SGTXT","东中国区李萍报销办公费");
        item.setValue("KOSTL","1050S45110");
        item.setValue("MWSKZ","J0");
        item.setValue("AUFNR","1050SAK10400");
        item.setValue("ZUONR","YXCL-20241022TS1");
        item.setValue("ZFBDT","20241022");

        item.appendRow();
        item.setValue("BUZEI","003");
        item.setValue("BSCHL","31");
        item.setValue("HKONT","2241040100");
        item.setValue("LIFNR","1000A02244");
        item.setValue("WRBTR",347);
        item.setValue("SGTXT","差旅YXCL-20241022TS1");
        item.setValue("ZUONR","YXCL-20241022TS1");
        item.setValue("ZFBDT","20241022");
        item.setValue("ZLSCH","T");

        //3.调用并获取返回值
        log.info("SAP执行方法{},入参：{}", name);
        JCoResponse response = new VoucherServiceImpl.DefaultRequest(function).execute(sapClient.getDestination());

        //4.封装返回值
        SapCreateVoucherRes sapResult = new SapCreateVoucherRes();
        for (JCoField jCoField : response) {
            String fieldName = jCoField.getName();
            String value = (String) function.getExportParameterList().getValue(fieldName);
            if ("AC_DOC_NO".equals(fieldName)) {
                sapResult.setAC_DOC_NO(value);
            }
            if ("FISC_YEAR".equals(fieldName)) {
                sapResult.setFISC_YEAR(value);
            }
            if ("EV_TYPE".equals(fieldName)) {
                sapResult.setEV_TYPE(value);
            }
            if ("EV_MESSAGE".equals(fieldName)) {
                sapResult.setEV_MESSAGE(value);
            }
            if ("FIS_PERIOD".equals(fieldName)) {
                sapResult.setFIS_PERIOD(value);
            }
        }
        log.info("SAP执行方法{},返回结果:{}", name, JSONUtil.toJsonStr(sapResult));
        return sapResult;
    }

    JSONObject getLovByName(String lovName, String valueCode) {
        log.info("getLovByName值列表查询,入参: {}", lovName);
        String path = "/common/fndLovValues/v2/findByLovNameAndCode?bizId=" + UUID.randomUUID()
                + "&timestamp=" + System.currentTimeMillis()
                + "&lov_name=" + lovName
                + "&code=" + valueCode;
        ServiceRes res = yjApiService.api(path, HttpMethod.GET, null, String.class);
        if (res.getData() == null) {
            log.info("值列表lovName:{},valueCode:{}查询结果为空！", lovName, valueCode);
            return null;
        }
        JSONArray values = JSONUtil.parseObj(res.getData()).getJSONArray("page_info");
        if (values == null || values.size() == 0) {
            log.info("值列表lovName:{},valueCode:{}查询结果为空！", lovName, valueCode);
            return null;
        }
        JSONObject value = values.getJSONObject(0);
        return value;
    }

    public String getSupplierAccountByCode(String accountCode) {
        //common/supplierAccounts/v2/findByPage?page_num=2&page_size=20&bizId=7171d721-1d60-447c-930f-787eb64781ab&timestamp=*************
        String path = ApiPathConstant.SUPPLIER_ACCOUNT_FIND_BY_PAGE + "?page_num=1&page_size=20&bizId="
                + UUID.randomUUID() + "&timestamp=" + System.currentTimeMillis() + "&codes=" + accountCode;

        ServiceRes serviceRes = yjApiService.api(path, HttpMethod.GET, null, YjPageInfo.class);
        if(!ServiceRes.isSuccess(serviceRes)) {
            return null;
        }
        YjPageInfo yjPageInfo = (YjPageInfo)serviceRes.getData();
        List<SupplierAccountFindByPageRes> supplierAccounts = JSONUtil.toList((JSONArray) yjPageInfo.getPage_info(), SupplierAccountFindByPageRes.class);

        if (supplierAccounts == null || supplierAccounts.size() == 0) {
            return null;
        }
        SupplierAccountFindByPageRes supplierAccount = supplierAccounts.get(0);
        return supplierAccount.getState();
    }

    private ServiceRes writeBackVc(String docNum, String glNum, String glStatus, String glMsg,long glDate,Integer headerId,int count) {
        count++;
        glMsg = StringUtils.isEmpty(glMsg) ? glMsg : org.apache.commons.lang.StringUtils.substring(glMsg, 0, 200);
        VcBackOapiDto backOapiDto = VcBackOapiDto.builder()
                .unique(docNum).journalNum(glNum).glStatus(glStatus).glDate(glDate).glMessage(glMsg)
                .build();
/*        if (GlStatusEnum.GENERATED.getCode().equals(glStatus)){
            backOapiDto.setAttachmentUrl(fileDto.getFileUrl());
            backOapiDto.setFileName(fileDto.getFileName());
        }*/
        BaseOneDto<VcBackOapiDto> baseOneDto = new BaseOneDto<>();
        baseOneDto.setData(backOapiDto);
        String writeBackVcParam = com.alibaba.fastjson.JSONObject.toJSONString(baseOneDto);
        log.info("回写凭证状态入参: {}", writeBackVcParam);
        ServiceRes serviceRes = yjApiService.api(vcBackUrl, HttpMethod.POST, writeBackVcParam, null);
        log.info("修改凭证状态返参: {}", com.alibaba.fastjson.JSONObject.toJSONString(serviceRes));
        if (!ServiceRes.isSuccess(serviceRes)){
            String msg = serviceRes.getMsg();
            Integer resCode = Integer.parseInt(serviceRes.getCode());
            /*com.alibaba.fastjson.JSONObject object = com.alibaba.fastjson.JSONObject.parseObject(msg);
            Integer resCode = object.getInteger("resCode");*/
            if (210001 == resCode){
                //凭证号重复,直接更新凭证状态
                updateClaimGlStatus(headerId, backOapiDto.getJournalNum());
            }
            if (500429 == resCode){
                //TOO MANY REQUESTS 线程等待5S重试
                try {
                    if (count > 3){
                        return serviceRes;
                    }
                    Thread.sleep(15000);
                    serviceRes = writeBackVc(docNum,glNum,glStatus,glMsg,glDate, headerId,count);
                    return serviceRes;
                }catch (Exception e){
                    log.error("writeBackVc,异常:{}",msg,e);
                }

            }

        }
        return serviceRes;
    }

    private void updateClaimGlStatus(Integer headerId,String journalNum) {
        JSONObject object = new JSONObject();
        object.put("id",headerId);
        object.put("gl_status",GlStatusEnum.GENERATED.getCode());
        object.put("gl_message","推送成功");
        if (StringUtils.isNotEmpty(journalNum)){
            object.put("journal_num",journalNum);
        }
        String jsonString = JSONUtil.toJsonStr(object);
        yjApiService.api(updateClaimUrl, HttpMethod.POST, jsonString, ExpClaimHeader.class);
    }

    private ServiceRes updateClaimGlStatus(Integer headerId) {
        JSONObject object = new JSONObject();
        object.put("id",headerId);
        object.put("gl_status",GlStatusEnum.GENERATED.getCode());
        object.put("gl_message","推送成功");
        object.put("gl_date",System.currentTimeMillis());
        String jsonString = JSONUtil.toJsonStr(object);
        return yjApiService.api(updateClaimUrl, HttpMethod.POST, jsonString, ExpClaimHeader.class);
    }

    public Map<String, String> getVoucherTypeFromLov() {
        Map<String, String> resultMap = new HashMap<>();

        JSONArray lovObjects = yjLovService.lovValues("CBZXDX");
        if (lovObjects == null || lovObjects.size() == 0) {
            return null;
        }
        for (Object lovObject : lovObjects) {
            JSONObject lovValue = JSONUtil.parseObj(lovObject);
            String code = lovValue.getStr("code");
            String description = lovValue.getStr("description");
            resultMap.put(code, description);
        }
        return resultMap;
    }
    @Override
    public UserFindByCodeRes getUserByCode(String empolyeeNumber) {
        UserFindByCodeReq req = UserFindByCodeReq.builder().timestamp(System.currentTimeMillis()).bizId(UUID.randomUUID().toString()).code(empolyeeNumber).build();
        UserFindByCodeRes res = yjUserService.findByCode(req);
        return res;
    }

    private String getAdvanceClaim(String linkDocumentNum) {
        com.alibaba.fastjson.JSONObject req = new com.alibaba.fastjson.JSONObject();
        req.put("documentNum",linkDocumentNum);
        ClaimUniqueRes reqClaimUniqueRes = yjClaimService.findClaimByCode(req);
        if (reqClaimUniqueRes == null) {
            return null;
        }
        List<HeaderBacksDto> headerBackLinks = reqClaimUniqueRes.getHeaderBackLinks();
        if (headerBackLinks == null || headerBackLinks.size() == 0) {
            return null;
        }
        for (HeaderBacksDto headerBackLink : headerBackLinks) {
            String internalType = headerBackLink.getInternalType();
            if ("payment".equals(internalType)) {
                return headerBackLink.getCode();//返回借款申请、预付款申请对应的支付单
            }

        }
        return null;
    }

    static class DefaultRequest extends com.sap.conn.jco.rt.DefaultRequest {
        DefaultRequest(JCoFunction function) {
            super(function);
        }
    }
}
