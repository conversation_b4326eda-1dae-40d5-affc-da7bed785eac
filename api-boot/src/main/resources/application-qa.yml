spring:
  redis:
    host: **********
    port: 6379
    timeout: 3000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: *************************************************************************************
          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
          username: admin.dev
          password: CJjzyNiMDp9Z
          type: com.alibaba.druid.pool.DruidDataSource
          druid: # 以下参数针对每个库可以重新设置druid参数
            validation-query: select 1 #比如oracle就需要重新设置这个


# 云简相关配置
yj:
  api:
    domainUrl: https://taliopenapi.cloudpense.com
    grantType: client_credentials
    clientId: cloudpense2483631363b
    clientSecret: 44fd9f67faf8bf5f44b73494f8c00c7848f0b5c1474314c7e2164d68afc3fccbde45ae98
  push:
    appId: cli_nyktXvt46rAlSezB
    token: iZgBUkwrAFCWbCpysEHCCHmc9Hoill6T
    aesToken: MAKAxLiDfOZwvSfqLg3e2tSOeHlmZxkQ6dHqKcgsIfp
# API监控
cmonitor:
  clientCode: ${yj.company-id}-${spring.profiles.active}

ext:
  oauth:
    #    url: http://sso.akesobio.com:8099
    url: https://sso.akesobio.com
    redirectUrl: /oauth/ssoYj/unAuth
    ssoUrl: /sso/oauth2.0/authorize?response_type=code&client_id=
    accesstTokenUrl: /sso/oauth2.0/accessToken?grant_type=authorization_code&client_id=
    profileUrl: /sso/oauth2.0/profile?access_token=
    clientId: 18c145d211ft32a54e2092c04551ba10
    clientSecret: 87iit7sZgyOLMHVSfDdBR6FitQbyptYFTJpFq4qCNyELxHfDAoRFt74UPiQkuCHr
  oa:
    #采购申请、IIT立项申请同步OA
    url: http://***********:9936/api/sys-modeling/appModelRestService/addModel
    createModelUrl: http://***********:9936/api/extend/modeling/createModeling
    fdModelId: 191e0125c5c885f3a88c2654e3381045
    fdFlowId: 191e047c2a0ae88004dc3584cc5a3b00
    modelFdModelId: 19371ef52bff9b39c5f35bc423d80a47
    modelFdFlowId: 193757097ad4a77a18226bc4e9aa8c4c
inside:
  common:
    customDomainUrl: http://121.8.102.229:8329/kangfang
  oauth:
    companyKey: 2cbb1520-33b7-44
    companyCode: akesobio
    ssoUrl: https://qa.cloudpense.com/tmp/sso.html?source=new&platform=cloudpense&position=main&
  type:
    I010: 628372
    I011: 628373
  # 内部接口url
oa:
  todoUrl: http://***********:9936/
  todo: api/sys-notify/sysNotifyTodoRestService/sendTodo
  todoDone: api/sys-notify/sysNotifyTodoRestService/setTodoDone
  deleteTodo: api/sys-notify/sysNotifyTodoRestService/deleteTodo
  ssoUrl: https://qa.cloudpense.com/tmp/sso.html?source=new&companyCode=akesobio
  redirectUrl: http://**********:8329/kangfang/oauth/todo/unAuth?

#费控内部接口url
fk:
  #审批流 单条更新
  updateWorkFlowUrl: /common/workflow/status
  #值列表查询
  selectValueUrl: https://taliopenapi.cloudpense.com/common/fndLovValues/v2/findByLovNameAndCode
  #订单查询
  findByUrl: https://taliopenapi.cloudpense.com/common/order/findByPage
  #订单修改
  updateStatus: /common/order/updateStatus
  #单据查询
  findListByConditionUrl: /cproxy/claim/findClaimListByParam
  #单据关闭
  updateCloseUrl: /common/document/close
  #单据创建
  insertDocumentsUrl: /common/document
  #单据更新
  updateClaimUrl: /cproxy/claim/updateExpClaimHeader
  #单据批量查询
  batchQueryUrl: /plugin/claim/kangfangGetClaim
  #员工号查询
  employeeInfoUrl: https://taliopenapi.cloudpense.com/common/users/v2/findByCode
  #单据共享人员新增
  documentSharingAddUrl: /common/document/share/user
  #结算单查询
  statements: https://taliopenapi.cloudpense.com/common/statements/findByPage
  #员工信息查询
  users: /common/users/v2/findByCode
  #单据电子发票数量查询
  #https://openapi-doc.cloudpense.com/bill/invoice_einvoiceCount.html
  queryInvoiceCountUrl: /common/receiptType/config
#  #结算单查询
#  statements: https://aliopenapi.cloudpense.com/common/statements/findByPage
#  #员工信息查询
#  users: /common/users/v2/findByCode
#SAP配置
sap:
  ashost: **************
  sysnr: "00"
  client: 120
  user: OAUSER
  passwd: 7654321Zxc@
  lang: ZH
  voucherBackUrl: http://**********:7000/open_api/api/CloudpenseCreateFormInfo
  ZFK_FI_001_url: http://**********:7000/sap/api/ZFK_FI_001

#驳回单据定时通知
rejectDocumentInfo:
  appName: 驳回单据定时通知
  modelName: 系统管理
  modelId: 17a8ef6b3d63bc836e9db0f403399cba
  subject: 驳回单据定时通知
  link: http://**********:8329/kangfang/oauth/todo/unAuth?
  mobileLink: /
  padLink: /
  type: 2
  url: http://***********:9936/api/sys-notify/sysNotifyTodoRestService/sendTodo

#报表系统
ReportingSystemApi:
  isTerminalNode: http://**********:9966/dev-api/costControl/YXHTUploadController/isTerminalNode

#会议系统配置文件
client:
  auth:
    token: 343074a2-7387-4336-a068-b462f95430c7
    IP: https://akesobiouat.recloud.com.cn
    GetToken: /t/akesobiouat/token
    grant_type: password
    username: demo2
    password: Jcyv2ori#
#考勤系统配置
hr:
  addBusinessTripUrl: http://*********:5566/api/AddBusinessTrip
#宿舍系统配置 (原智慧园区)
dormitorySystem:
  handleDormitoryCheckInUrl: http://**********/v-gateway-dormitory-background/dormitory/applicationManagement/guestRoomCheckIn
  dormitoryAuthKey: YE59xWWssaokb4Xf4R52ySR8yHcqvKoL # 宿舍系统认证密钥


# 电子档案 API 配置
archive:
  api:
    base-url: https://aliqaapi.cloudpense.com
    client-id: cloudpense2483631363b
    client-secret: 44fd9f67faf8bf5f44b73494f8c00c7848f0b5c1474314c7e2164d68afc3fccbde45ae98
    company-id: 23049
    source-name: SAP