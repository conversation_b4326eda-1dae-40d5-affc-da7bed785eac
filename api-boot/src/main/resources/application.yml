server:
  port: 8329
  servlet:
    context-path: /kangfang
spring:
  application:
    name: kangfang-api
  profiles:
    active: qa
  main:
    allow-bean-definition-overriding: true
#  datasource:
#    dynamic:
#      primary: master
#      datasource:
#        master:
#          url: *************************************************************************************
#          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#          username: sa
#          password: Sa123456@
#          type: com.alibaba.druid.pool.DruidDataSource
#          druid: # 以下参数针对每个库可以重新设置druid参数
#            validation-query: select 1 #比如oracle就需要重新设置这个
#        ekp:
#          url: **********************************************************************
#          username: sa
#          password: Sa123456@
#          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#          type: com.alibaba.druid.pool.DruidDataSource
#          druid: # 以下参数针对每个库可以重新设置druid参数
#            validation-query: select 1 #比如oracle就需要重新设置这个
#        self_service:
#          url: ******************************************************************************
#          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#          username: sa
#          password: Sa123456@
#          type: com.alibaba.druid.pool.DruidDataSource
#          druid: # 以下参数针对每个库可以重新设置druid参数
#            validation-query: select 1 #比如oracle就需要重新设置这个
#  datasource:
#    type: com.alibaba.druid.pool.DruidDataSource
#    driver-class-name: org.sqlite.JDBC
#    url: jdbc:sqlite:/home/<USER>/project/${spring.application.name}/data1.db
#    hikari:
#      # 避免出现多线程执行报 [SQLITE_BUSY] The database file is locked(database is locked)
#      maximum-pool-size: 1
#    druid:
#      test-while-idle: false
#      validation-query: select 1
#  sql:
#    init:
#      mode: always
#      schema-locations: classpath:retry_log.sql
#mybatis-plus

mybatis-plus:
#  mapper-locations: classpath*:/com/cloudpense/*/mapper/xml/*.xml

  mapper-locations: classpath*:mapper/**/*Mapper.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  #  config-location: classpath:mybatis/mybatis-config.xml
  #实体扫描，多个package用逗号或者分号分隔
#  typeAliasesPackage: com.cloudpense.**.domain;
logging:
  file:
    path: /home/<USER>/project/${spring.application.name}/logs
# 云简相关配置
yj:
  company-id: 23049 # todo 唯一公司id
  api-list: lov,department,claim,delivery  # todo 需要引入的的云简openapi接口，请不要随便更改，多个值逗号分割
  common:

    findBudgetDetailsUrl: /common/budgets/v2/findDetails
    documentUrl: /common/document
    getDocNumUrl: /plugin/claim/hansonGetClaim
    documentUniqueUrl: /common/document/unique?documentNum=
    expensesUpdateStatusUrl: /common/expenses/v2/update/status
    receiptTransferUrl: /common/invoices/receiptTransfer
    claimOwnerTransferUrl: /common/header/owner
    findExpenseByPage: /common/invoice/findExpenseByPage
    filesUpload: /common/files/url
    filesUploadUrlV3: /common/files/v3/uploadUrl
    syncLovUrl: /common/lovs/v2/batch
    lovUserStaffsUrl: /common/lovUserStaffs/v2/batch
    findRuleConditionUrl: /common/budgets/v2/glBudgetRuleJson/findRuleCondition
    budgetBatchLimitUrl: /common/budgets/v2/batchLimit
    findReceiptsUrl: /common/invoice/findReceiptsByPage
    findGlBudgetPlanByCodesUrl: /common/budgets/v2/glBudgetPlan/findByCodes?
    budgetUrl: /common/budgets/v2/findDetails
    cidUrl: /common/budgets/v2/glBudgetCombination/findByCombinations
    periodUrl: /common/budgets/v2/glPeriod/findByCodes?bizId=
    cidPeriodCombUrl: /common/budgets/v2/glBudgetAmount/findByCidsAndPeriods?bizId=
    lovByNameUrl: /common/fndLovValues/v2/findByPage?bizId=
    updateWorkFlowUrl: /common/workflow/status
    userAccBatchUrl: /common/userAccounts/v2/batch
    vcBackUrl: /common/voucher/back
    findByCodesUrl: /common/users/v2/findByCodes?codes=
    closedClaimUrl: /common/document/close
    userFindByPageUrl: /common/users/v2/findByPage
    updateOrderStatusUrl: /common/order/updateStatus
    workFlowBatchUrl: /common/workflow/batch
    lovFindUrl: /common/v2/fndLovValues/findByLovNameAndCodes
  cproxy:
    findDeptsByIdUrl: /cproxy/department/findListByParam
    findProjectsByIdUrl: /cproxy/project/findList
    findUsersUrl: /cproxy/user/findList
    updateClaimUrl: /cproxy/claim/updateExpClaimHeader
    updateClaimEsUrl: /cproxy/claim/updateClaimFlag
    findListByConditionUrl: /cproxy/claim/findClaimListByParam
    findCityUrl: /cproxy/city/findCityById?cityId=
# 本应用相关配置
app:


