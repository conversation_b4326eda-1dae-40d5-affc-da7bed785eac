package com.cloudpense.boot;



import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudpense.akesobio.domain.IitProject;
import com.cloudpense.akesobio.service.IitProjectService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


// ... existing code ...

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApiBootApplication.class)
@Slf4j
@ComponentScan("com.cloudpense")
@MapperScan("com.cloudpense.akesobio.mapper")
public class ExcelReader {
    @Resource
    private IitProjectService iitProjectService;

    @Test
    public void test() {
        // Excel文件路径
        String fileName = "C:\\Users\\<USER>\\Downloads\\存量IIT项目对公支付整理4.30.xlsx";

        // 创建监听器
        ProjectDataListener listener = new ProjectDataListener();

        // 读取Excel
        EasyExcel.read(fileName, ProjectData.class, listener).sheet().doRead();

        // 获取项目编号列表
        List<String> projectNumbers = listener.getProjectNumbers();
        List<String> notExistProjects = new ArrayList<>();

        // 批量检查数据库中不存在的项目编号
        for (String projectNumber : projectNumbers) {
            LambdaQueryWrapper<IitProject> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(IitProject::getProjectCode, projectNumber);
            if (iitProjectService.count(queryWrapper) == 0) {
                notExistProjects.add(projectNumber);
                IitProject iitProject = new IitProject();
                iitProject.setProjectCode(projectNumber);
                iitProject.setType("01");
                iitProject.setProjectName(projectNumber);
                iitProject.setResult(2);
                iitProjectService.save(iitProject);
            }
        }

//        LambdaQueryWrapper<IitProject> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(IitProject::getResult, 2);
//        List<IitProject> iitProjects = iitProjectService.list(queryWrapper);
//        // 转换成ProjectData列表
//        List<ProjectData> projectDataList = new ArrayList<>();
//        for(IitProject project : iitProjects) {
//            ProjectData projectData = new ProjectData();
//            projectData.setProjectNumber(project.getProjectName());
//            projectDataList.add(projectData);
//        }
// 生成Excel文件
//        String fileName1 = "C:\\Users\\<USER>\\Desktop\\项目编号列表.xlsx";
//        EasyExcel.write(fileName1, ProjectData.class)
//                .sheet("项目编号")
//                .doWrite(projectDataList);
//
//        System.out.println("Excel生成完成,共导出 " + projectDataList.size() + " 条数据");
//
////        for(String projectNumber : notExistProjects){
////
////        }
//
        // 统一输出结果
        System.out.println("===== 数据处理结果 =====");
        System.out.println("Excel中的所有项目编号：");
        System.out.println(String.join(", ", projectNumbers));

        System.out.println("\n数据库中不存在的项目编号：");
        if (notExistProjects.isEmpty()) {
            System.out.println("所有项目编号都存在于数据库中");
        } else {
            System.out.println(String.join(", ", notExistProjects));
            System.out.println("共计 " + notExistProjects.size() + " 个项目编号不存在");
        }
    }
}