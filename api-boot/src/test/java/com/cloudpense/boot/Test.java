package com.cloudpense.boot;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudpense.akesobio.domain.IitProject;
import com.cloudpense.akesobio.dto.LinkageLovs;
import com.cloudpense.akesobio.dto.LovsDto;
import com.cloudpense.akesobio.mapper.IitProjectMapper;
import com.cloudpense.akesobio.service.*;
import com.cloudpense.akesobio.timeTask.ApiMethod;
import com.cloudpense.akesobio.util.CalendarAdjust;
import com.cloudpense.akesobio.util.ReflectionUtil;
import com.cloudpense.akesobio.util.http.HttpUtil;
import com.cloudpense.boot.service.OaTodoService;
import com.cloudpense.boot.service.impl.OaTodoServiceImpl;
import com.cloudpense.common.service.YjApiService;
import com.cloudpense.common.service.YjLovService;
import com.cloudpense.common.service.budget.BudgetService;
import com.cloudpense.common.service.budget.param.BudgetParamDto;
import com.cloudpense.common.service.budget.res.BudgetResDto;
import com.cloudpense.common.service.budgets.KfYjBudgetsService;
import com.cloudpense.common.service.claim.KangfangGetClaimReq;
import com.cloudpense.common.service.claim.YjClaimService;
import com.cloudpense.common.service.department.YjDepartmentService;
import com.cloudpense.common.service.project.YjProjectService;
import com.cloudpense.common.sign.SignUtils;
import com.cloudpense.common.vo.ExcelData;
import com.cloudpense.common.vo.ServiceRes;
import com.cloudpense.common.vo.todo.OaTodoPushReq;
import com.cloudpense.common.vo.todo.OaTodoPushRes;
import com.cloudpense.common.vo.yj.claim.ClaimUniqueRes;
import com.cloudpense.common.vo.yj.project.ProjectDTO;
import com.cloudpense.standard.dao.entity.ExpClaimHeader;
import com.cloudpense.standard.vo.claim.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.junit.runner.RunWith;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.net.URI;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApiBootApplication.class)
@Slf4j
@ComponentScan("com.cloudpense")
@MapperScan("com.cloudpense.akesobio.mapper")
public class Test {

    @Resource
    private  YjClaimService yjClaimService;

    @Autowired
    private BudgetService budgetService;
    @Resource
    private  YjProjectService yjProjectService;

    public static String generateSerialNumber(String prefix, int number) {
        return prefix + "-" + String.format("%04d", number);
    }
    @Resource
    private IitProjectService iitProjectService;
    @Resource
    private IitProjectMapper iitProjectMapper;
    @Resource
    private ReimbursementService reimbursementService;
    @Resource
    private MeetingApiService meetingApiService;
    @Resource
    private KfYjBudgetsService kfYjBudgetsService;

    @Resource
    private ApiMethod apiMethod;
    @Resource
    private CloudpenseApiService cloudpenseApiService;

    @Resource
    private YjLovService yjLovService;

    @Resource
    private YjDepartmentService yjDepartmentService;

    @Resource
    private SapApiService sapApiService;

    @Resource
    private OaTodoService oaTodoService;



    @Resource
    private YjApiService yjApiService;

    @Value("${fk.batchQueryUrl}")
    private String batchQueryUrl;

    public static void main(String[] args) {
        //电子档案凭证文件上传与回传备份接口测试

            log.info("开始测试电子档案凭证文件上传与回传备份接口");

            // 准备测试数据
            String baseUrl = "http://localhost:8329/kangfang"; // 加上context-path
            String apiUrl = baseUrl + "/archive/voucher/uploadAndBack";

            // 创建测试PDF文件内容（简单的PDF头部）
            String pdfContent = "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF";
            byte[] pdfBytes = pdfContent.getBytes();

            // 设置请求头参数
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/octet-stream");
            headers.add("unique", "PAY0000000156");
            headers.add("file_name", "105020255300000044.pdf");
            headers.add("gl_status", "generated");
            headers.add("journal_num", "105020255300000044");
            headers.add("gl_message", "测试凭证处理成功");
            headers.add("gl_date", String.valueOf(System.currentTimeMillis()));
            headers.add("ledger_name", "测试总账");
            headers.add("poster_user_code", "B00490");
            headers.add("poster_user_name", "陈定乾");

            // 创建请求实体
            HttpEntity<byte[]> requestEntity = new HttpEntity<>(pdfBytes, headers);

            try {
                log.info("发送请求到: {}", apiUrl);
                log.info("请求头: {}", headers);
                log.info("文件大小: {} bytes", pdfBytes.length);
                RestTemplate restTemplate = new RestTemplate();
//                 发送POST请求（注意：这里需要根据实际情况配置RestTemplate）
                 ResponseEntity<String> responseEntity = restTemplate.exchange(
                     URI.create(apiUrl),
                     HttpMethod.POST,
                     requestEntity,
                     String.class
                 );

//                // 模拟响应处理
//                log.info("请求构建完成，实际调用需要配置RestTemplate");
//                log.info("测试参数验证通过:");
//                log.info("- unique: {}", headers.getFirst("unique"));
//                log.info("- file_name: {}", headers.getFirst("file_name"));
//                log.info("- gl_status: {}", headers.getFirst("gl_status"));
//                log.info("- journal_num: {}", headers.getFirst("journal_num"));
//                log.info("- 文件内容长度: {} bytes", pdfBytes.length);
                log.info(responseEntity.getBody());

                System.out.println("电子档案凭证文件上传与回传备份接口测试完成");

            } catch (Exception e) {
                log.error("测试异常: {}", e.getMessage(), e);
                throw e;
            }




    }

    @org.junit.Test
    public void test12345() throws Exception {
        ClaimUniqueRes claimUniqueRes = yjClaimService.findDocumentByExternalId("FSBB000015242");
//        ClaimUniqueRes claimUniqueRes = yjClaimService.findDocumentByExternalId("PAY0000000234");
        log.info("查询结果：{}",JSONObject.toJSONString(claimUniqueRes));
        System.out.println(System.currentTimeMillis());
    }


    @org.junit.Test
    public void test1234() throws Exception {
        ExpClaimHeader preUpdate = new ExpClaimHeader();
        preUpdate.setHeaderId(29900);
//        preUpdate.setId(claim.getId());
        preUpdate.setColumn39("103020255300001825;;;");
        ServiceRes updateResult = yjClaimService.updateClaimHeader(preUpdate);
      }


    @org.junit.Test
    public void test123() throws Exception {
//        claim = JSONUtil.toBean(jsonObject.getString("claimVo"), ExpClaimHeaderCommonVo.class);
        // 获取claimVo对象
//        JSONObject claimVo = jsonObject.getJSONObject("claimVo");

        Long currentTime = System.currentTimeMillis();
        // 获取当月第一天
        Long monthStart = CalendarAdjust.getMonthStartTime(currentTime, "GMT+8:00");
        // 获取当月最后一天
        Long monthEnd = CalendarAdjust.getMonthEndTime(currentTime, "GMT+8:00");


        // 获取submitUserVo对象
//        JSONObject submitUserVo = claimVo.getJSONObject("submitUserVo");
        // 获取提交人工号
        String jobNumber = "A04363";


//            ExpHeaderTypeVo vo = claim.getHeaderTypeVo();


        List<String> list1 = new ArrayList<>();
        //费用报销  YXFY02
        list1.add("YXFY02");

        List<String> list2 = new ArrayList<>();
        //已提交
        list2.add("submitted");
        //审批中
        list2.add("approving");
        //审批通过
        list2.add("approved");
        //已完成
        list2.add("closed");
        //业务审批通过
        list2.add("preapproved");
        //财务审批通过
        list2.add("checked");
        //打回修改
        list2.add("modifying");

        List<String> list3 = new ArrayList<>();
        list3.add(jobNumber);

        Map map = new HashMap();
        map.put("page_size", "100");
        map.put("page_num", "1");
        map.put("start_datetime", monthStart);
        map.put("end_datetime", monthEnd);
        //submit_date  提交时间
        map.put("date_field_name", "submit_date");
        map.put("type_codes", list1);
        map.put("statuses", list2);
        map.put("charge_user_codes", list3);

        ServiceRes api = yjApiService.api(batchQueryUrl, HttpMethod.POST, JSONObject.toJSONString(map), null);
        String code = api.getCode();
        if ("500000".equals(code) || "000000".equals(code)) {
            throw new RuntimeException(api.getMsg());
        }
        // 将data字段反序列化为JSONObject
        cn.hutool.json.JSONObject dataObject = (cn.hutool.json.JSONObject) api.getData();
        double totalAmount = 0;
        if (dataObject.size() > 0) {
            Integer totalPage = dataObject.getInt("total_page");
            if (totalPage > 0) {
                for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
                    Map mapNew = new HashMap();
                    mapNew.put("page_size", "100");
                    mapNew.put("page_num", pageNum);
                    mapNew.put("start_datetime", monthStart);
                    mapNew.put("end_datetime", monthEnd);
                    //submit_date  提交时间
                    mapNew.put("date_field_name", "submit_date");
                    mapNew.put("type_codes", list1);
                    mapNew.put("statuses", list2);
                    mapNew.put("charge_user_codes", list3);

                    ServiceRes apiNew = yjApiService.api(batchQueryUrl, HttpMethod.POST, JSONObject.toJSONString(mapNew), null);
                    String codeNew = api.getCode();
                    if ("500000".equals(codeNew) || "000000".equals(codeNew)) {
                        throw new RuntimeException(apiNew.getMsg());
                    }
                    // 将data字段反序列化为JSONObject
                    cn.hutool.json.JSONObject info = (cn.hutool.json.JSONObject) api.getData();
                    cn.hutool.json.JSONArray pageInfoArray = info.getJSONArray("page_info");

                    // 判断pageInfoArray的条数是否大于0
                    if (pageInfoArray != null && pageInfoArray.size() > 0) {
                        for (int i = 0; i < pageInfoArray.size(); i++) {
                            cn.hutool.json.JSONObject pageInfoObject = pageInfoArray.getJSONObject(i);
                            cn.hutool.json.JSONObject object = pageInfoObject.getJSONObject("column17_obj");
                            //获取费用类型
                            String feeType = object.getStr("value_meaning");
                            //当单据状态为 已完成
                            String statusNew = pageInfoObject.getStr("status");
                            if (feeType.equals("外部交际")) {
                                if (statusNew.equals("closed")) {
                                    cn.hutool.json.JSONArray headerBackLink = pageInfoObject.getJSONArray("header_back_links");
                                    if (headerBackLink != null) {
                                        boolean isTrue = false;
                                        for (int j = 0; j < headerBackLink.size(); j++) {
                                            //关联后序单据状态  查询有效单据状态
                                            String status = headerBackLink.getJSONObject(j).getStr("status");
                                            if (status.equals("submitted") || status.equals("approving") || status.equals("approved") || status.equals("preapproved") || status.equals("checked") || status.equals("modifying")) {
                                                isTrue = true;
                                            }
                                        }
                                        if (isTrue) {
                                            // 获取total_amount字段的值
                                            Double total_amount = pageInfoObject.getDouble("total_amount"); // 使用getDoubleValue获取double类型
                                            if (total_amount != null) {
                                                totalAmount += total_amount; // 累加到总和
                                            }
                                        }
                                    }
                                } else {
                                    // 获取total_amount字段的值
                                    Double total_amount = pageInfoObject.getDouble("total_amount"); // 使用getDoubleValue获取double类型
                                    if (total_amount != null) {
                                        totalAmount += total_amount; // 累加到总和
                                    }
                                }
                            }
                        }
                    }
                    System.out.println("总和：" + totalAmount);
                }
//                cn.hutool.json.JSONObject jsonNew = new cn.hutool.json.JSONObject();


//                if (claim.getColumnJson() != null)
//                    jsonNew = JSONUtil.parseObj(claim.getColumnJson());
//
//
//                jsonNew.set("column200", totalAmount);
//
//                //将totalAmount返参到单据column200字段里面
//                JSONObject json = new JSONObject();
//                json.put("id", claim.getId());
//                json.put("header_id", claim.getId());
//                json.put("column_json", JSONUtil.toJsonStr(jsonNew));
//                //String jsonString = JSONObject.toJSONString(json);
//                //ServiceRes serviceRes = yjClaimService.updateClaimHeader(claim);
//                yjApiService.api(updateClaimUrl, HttpMethod.POST, JSONObject.toJSONString(json), null);
            }
        }
    }


    //手动更改表单
    @org.junit.Test
    public void updateForm() throws Exception {

//        {"id":15614,"note":"","header_id":15614,"column_json":"{\"column140\":\"手工创建\",\"column102\":\"1050SAK10400\"}
        ExpClaimHeader expClaimHeader = new ExpClaimHeader();
        expClaimHeader.setHeaderId(15614);
        expClaimHeader.setId(15614);
        JSONObject columnJson = JSONObject.parseObject("{\"column140\":\"手工创建\",\"column102\":\"1050SAK10400\"}");
        columnJson.put("column200", "2500.00");
//        if(claim.getColumnJson() != null)
//            columnJson = JSONObject.parseObject(claim.getColumnJson());
//        else throw new Exception("columnJson is null");
//        columnJson.put("column102", internalOrder);
        expClaimHeader.setColumnJson(columnJson.toJSONString());
        yjClaimService.updateClaimHeader(expClaimHeader);
    }










    public static List<Map<String, Object>> readExcelToMap(String fileName, String sheetName) {
            List<Map<String, Object>> dataList = new ArrayList<>();
            EasyExcel.read(fileName, new AnalysisEventListener<Map<String, Object>>() {
                private Map<Integer, String> headMap;

                @Override
                public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                    this.headMap = headMap;
                    System.out.println("表头信息：" + headMap);
                }

                @Override
                public void invoke(Map<String, Object> valueData, AnalysisContext context) {
                    HashMap<String, Object> paramsMap = new HashMap<>();
                    for (int i = 0; i < valueData.size(); i++) {
                        String key = headMap.get(i);
                        Object value = valueData.get(i);
                        paramsMap.put(key, value);
                    }
                    dataList.add(paramsMap);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    System.out.println("Excel读取完成,文件名:" + fileName + ",sheet:" + sheetName + ",行数：" + dataList.size());
                }
            }).sheet(sheetName).doRead();
            return dataList;
        }


//   public static void main(String[] args) {
//        String fileName = "C:\\Users\\<USER>\\Desktop\\新建 XLSX 工作表.xlsx";
//        List<Map<String, Object>> listRead = readExcelToMap(fileName, "Sheet1");
//       System.out.println(listRead);
//
//       for(Map<String, Object> map : listRead){
//           String projectCode = (String) map.get("项目号");
//
//       }
////        for (Map<String, Object> map : listRead) {
////            System.out.println(map);
////        }
////        System.out.println(listRead);
//    }


    @org.junit.Test
    public void todo(){
        OaTodoPushReq todo = new OaTodoPushReq();
        todo.setAppName("云简业财");
        todo.setModelName("云简业财");
//        String modelId = claimVo.getCode();//单据-消息ID
//        if (mqQueue.getPathId() != null) {
          String  modelId = "modelId" + "-" +" mqQueue.getPathId()";
//        }
        todo.setModelId(modelId);
        String subject = "23434343466";//代办信息
        todo.setSubject(subject);

        String linkUrl = "https://oa.akesobio.com/km/review/km_review_main/kmReviewMain.do?method=view&fdId=********************************&s_css=default";//跳转链接

        todo.setLink(linkUrl);
        todo.setMobileLink(linkUrl);
        todo.setPadLink(linkUrl);
//        if (OaTodoServiceImpl.PushType.TODO.equals(user.getPushType())) {//审批类
            todo.setType("2");//审批
//        } else {//抄送或通知
//            todo.setType("2");//抄送或通知
//        }
        cn.hutool.json.JSONObject target = new cn.hutool.json.JSONObject();
        target.put("PersonNo","B00490");
        todo.setTargets(JSONUtil.toJsonStr(target));//当前代办审批人
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        todo.setCreateTime(sdf.format(new Date()));//当前代办触发时间
        todo.setLevel("3");//优先级：默认传3

        oaTodoService.todoPush(todo);
//        res = todoPush(todeReq);//代办推送

    }
//    public ServiceRes todoPush(OaTodoPushReq req) {
//        ServiceRes serviceRes = ServiceRes.success(null);
//        try {
//            String url = todoUrl + todo;//代办推送url
//            HttpHeaders headers = new HttpHeaders();
//            headers.add("Content-Type", ContentType.APPLICATION_JSON.toString());
//            headers.add("Connection", "keep-alive");
//            HttpEntity entity = new HttpEntity<>(req, headers);
//            log.info("调用OA代办推送请求：url={}，req={}，entity={}", url, req,JSONUtil.toJsonStr(entity));
//            ResponseEntity<String> responseEntity = restTemplate.exchange(URI.create(url), HttpMethod.POST, entity, String.class);
//            String response = responseEntity.getBody();
//            log.info("{}调用oa待办推送接口响应：response={}", response);
//            OaTodoPushRes res = JSONUtil.toBean(response, OaTodoPushRes.class);
//            if(!OaTodoPushRes.isSuccess(res)) {
//                serviceRes.setCode(ServiceRes.CodeEnum.F.getCode());
//                serviceRes.setMsg("调用oa待办推送接口返回失败：" + OaTodoPushRes.getMessage(res));
//            }
//        } catch (Exception e) {
//            log.error("{}调用OA待办推送接口异常：{}", req.getModelId(), e.getMessage(), e);
//            String errorMsg = req.getModelId() + "调用OA待办推送接口异常:" + e.getMessage();
//            serviceRes.setCode(ServiceRes.CodeEnum.F.getCode());
//            serviceRes.setMsg(errorMsg);
//        }
//        return serviceRes;
//    }



    //补偿推送测试方法 - 用于管理员手动触发项目补偿推送
    @org.junit.Test
    public void compensationPushTest() throws Exception {
        // 测试项目编号，管理员可以修改此参数
        String projectCode = "AK112XL2025008";

        // 1. 根据项目编号获取项目数据
        LambdaQueryWrapper<IitProject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IitProject::getProjectCode, projectCode);
        IitProject iitProject = iitProjectMapper.selectOne(queryWrapper);

        if (iitProject == null) {
            log.error("项目编号 {} 不存在", projectCode);
            throw new Exception("项目编号不存在: " + projectCode);
        }

        log.info("找到项目: {}", iitProject.getProjectName());

        // 2. 构建推送参数，模拟IitProjectServiceImpl.java#L261-269的逻辑
        com.alibaba.fastjson.JSONArray jsonArray = new com.alibaba.fastjson.JSONArray();
        com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();

        // 设置项目名称
        jsonObject.put("name", iitProject.getProjectName());
        // 设置项目编号
        jsonObject.put("code", iitProject.getProjectCode());
        // 设置项目预算（如果存在）
        if (iitProject.getTotalAmount() != null) {
            jsonObject.put("projectBudget", iitProject.getTotalAmount());
        }

        jsonArray.add(jsonObject);

        // 3. 调用会议系统更新接口
        log.info("开始推送项目到会议系统: {}", jsonArray.toString());
        String result = meetingApiService.updateSeriesProjectNumber(jsonArray);
        log.info("推送结果: {}", result);

        System.out.println("补偿推送完成，项目编号: " + projectCode);
        System.out.println("推送结果: " + result);
    }

    //初始化iit项目号

    @org.junit.Test
    public void initIitProjectCode() throws Exception {
        String fileName = "C:\\Users\\<USER>\\Desktop\\新建 XLSX 工作表.xlsx";
        List<Map<String, Object>> listRead = readExcelToMap(fileName, "Sheet1");

        for(Map<String, Object> map : listRead){
            String projectCode = (String) map.get("项目号");
            LambdaQueryWrapper<IitProject> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(IitProject::getProjectCode,projectCode);
            IitProject iitProject = iitProjectMapper.selectOne(queryWrapper);
            if(iitProject == null){
                iitProject = new IitProject();
                iitProject.setProjectCode(projectCode);
                iitProject.setProjectName(projectCode);
                iitProject.setType("01");
                //公司代码
                String companyCode = (String) map.get("公司代码");
                String internalOrder = (String) map.get("内部订单号");
                ReflectionUtil.setFieldValue(iitProject, "internalOrder"+companyCode, internalOrder);
                if(!StringUtils.hasText(iitProject.getProjectCode()))
                    System.out.println("新增项目号：" + projectCode);
                iitProjectMapper.insert(iitProject);
            }

        }
    }



    @org.junit.Test
    public void sapApiTest() throws Exception {
        //* 3）同步会议系统：
        com.alibaba.fastjson.JSONArray jsonArray = new com.alibaba.fastjson.JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name","AK104XL2024028test1111");
        jsonObject.put("code","AK104XL2024028test1111");
        jsonObject.put("projectBudget","12345.00");
        jsonObject.put("stateCode",0);

        jsonArray.add(jsonObject);
        meetingApiService.updateSeriesProjectNumber(jsonArray);
        com.alibaba.fastjson.JSONArray jsonArray1 = new com.alibaba.fastjson.JSONArray();
        JSONObject jsonObject1 = new JSONObject();
        jsonObject.put("name","AK104XL2024028test1111");
        jsonObject.put("code","AK104XL2024028test1111");
        jsonObject.put("projectBudget","12345.00");
        jsonObject.put("stateCode",1);

        jsonArray1.add(jsonObject1);
        meetingApiService.updateSeriesProjectNumber(jsonArray1);
//        List<IitProject> iitProjects = iitProjectService.list().stream().filter(iitProject -> StringUtils.hasText(iitProject.getArea())).collect(Collectors.toList());
//
//        for(IitProject iitProject : iitProjects){
//            sapApiService.ZFK_FI_001(iitProject,"B00490");
//        }

    }



    @org.junit.Test
    public void linkageLovsTest() throws Exception {

       List<LinkageLovs> linkageLovs = iitProjectService.list().stream().filter(iitProject -> "01".equals(iitProject.getType()))
               .map((iitProject -> {
                   LinkageLovs linkageLovs1 = new LinkageLovs();
                   String[] split = iitProject.getProjectName().split("-");
                   if(split.length > 1){
                       linkageLovs1.setParentValue(split[0]);
                       linkageLovs1.setChildValue(iitProject.getProjectCode());
                       linkageLovs1.setEnabledFlag("N");
                   }
                   return linkageLovs1;
//
               }))
               .collect(Collectors.toList());
       cloudpenseApiService.linkageLovs(linkageLovs,"BT003");


    }


    @org.junit.Test
    @Transactional
    public void rowlockTest(){
        iitProjectMapper.getIitProjectByProjectNameAB("AK112-IIT-");
        System.out.println("test");
    }

    @org.junit.Test
    public void yjLovServiceTest() throws Exception {

//        DepartmentFindByCodesReq departmentFindByCodesReq = new DepartmentFindByCodesReq();
//        DepartmentFindByCodesReq departmentFindByCodesReq = new DepartmentFindByCodesReq();

//        DepartmentFindByPageReq departmentFindByPageReq =  DepartmentFindByPageReq
//                .builder().page_num(1).page_size(100).type("B").bizId(UUID.randomUUID().toString()).timestamp(System.currentTimeMillis()).build();
//

        System.out.println(yjDepartmentService.findDeptsById(99075));

    }

    @org.junit.Test
    public void test8(){
        ProjectDTO projectDTO = new ProjectDTO();
        List<Object> projectIds = new ArrayList<>();
        projectIds.add("7");

//        List<String> columns = new ArrayList<>();
//        columns.add("branch_id");
//        columns.add("project_name");



//        projectDTO.setProjectCodes(projectIds);
          projectDTO.setProjectIds(projectIds);
//        projectDTO.setColumns(columns);

        List<Object> iitProjects = yjProjectService.findProjectByPage(projectDTO);
//        cn.hutool.json.JSONObject iitProjectJson = (cn.hutool.json.JSONObject) iitProjects.get(0);
        System.out.println(iitProjects);

    }


    @org.junit.Test
    public void test7() throws Exception {
        List<IitProject> list =  iitProjectService.list();

        Map<String, List<IitProject>> listMap = list.stream().collect(Collectors.groupingBy(IitProject::getProjectName));
        for(String key : listMap.keySet()){
            List<IitProject> value = listMap.get(key);
            if(value.size() > 1){
                iitProjectService.removeById(value.get(0).getId());
            }
            for(IitProject iitProject : value){
                if(!iitProject.getProjectCode().equals(iitProject.getProjectName())){
                    iitProject.setProjectCode(iitProject.getProjectName());
                    iitProjectService.updateById(iitProject);
                }
            }
        }
       List<IitProject> iitProjects = list.stream().filter(item->item.getType() != null ).collect(Collectors.toList());

        List<LovsDto> lovsDtos = new ArrayList<>();
        for(IitProject iitProject : iitProjects){
            LovsDto lovsDto = new LovsDto();

            if(iitProject.getType().equals("01")){
                lovsDto.setCode("XMHZSJ");
            }else if(iitProject.getType().equals("02")){
                lovsDto.setCode("LXZSJ");
            }else if(iitProject.getType().equals("03")){
                lovsDto.setCode("LXZSJ");
            }

            lovsDto.setValue(iitProject.getProjectName());
            lovsDto.setType(iitProject.getProjectCode());
            lovsDto.setEnabledFlag("N");
            lovsDto.setColumn3("03");
//            if(claim.getTotalAmount()!=null)
//                lovsDto.setColumn4(claim.getTotalAmount().toString());
            lovsDto.setColumn5(iitProject.getProjectManager());
            lovsDto.setColumn6(iitProject.getDepartmentCode());
            lovsDtos.add(lovsDto);
        }

        cloudpenseApiService.syncLovs(lovsDtos);

    }

    @org.junit.Test
    public void syncSeries() throws Exception {

//        List<LovsDto> lovsDtos = new ArrayList<>();
//        LovsDto lovsDto = new LovsDto();
//        lovsDto.setCode();
//        cloudpenseApiService.syncSeries();
    }

    @org.junit.Test
    public void kangfangGetClaim() throws JsonProcessingException {
        KangfangGetClaimReq kangfangGetClaimReq = new KangfangGetClaimReq();
        Calendar calendar = Calendar.getInstance();
        // 获取每月的第15天
        calendar.set(Calendar.DAY_OF_MONTH, 15);
        Date fifteenthDayOfMonth = calendar.getTime();

        kangfangGetClaimReq.setPageNum(1);
        kangfangGetClaimReq.setPageSize(100);
        kangfangGetClaimReq.setStatuses(Arrays.asList("approved"));
        kangfangGetClaimReq.setDateFieldName("approved_date");
        kangfangGetClaimReq.setTypeCodes(Arrays.asList("YXPT02"));
        kangfangGetClaimReq.setStartDatetime(fifteenthDayOfMonth.getTime());
        kangfangGetClaimReq.setEndDatetime(System.currentTimeMillis());
        String res = yjClaimService.kangfangGetClaim(kangfangGetClaimReq);
        cn.hutool.json.JSONObject resJson = JSONUtil.parseObj(res);
        resJson.getJSONObject("data").getJSONArray("page_info");

    }
    @org.junit.Test
    public void summaryOfPlatformPlans() throws JsonProcessingException {
        KangfangGetClaimReq kangfangGetClaimReq = new KangfangGetClaimReq();
        Calendar calendar = Calendar.getInstance();
        // 获取每月的第15天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date fifteenthDayOfMonth = calendar.getTime();

        kangfangGetClaimReq.setPageNum(1);
        kangfangGetClaimReq.setPageSize(100);
        kangfangGetClaimReq.setStatuses(Arrays.asList("approved"));
        kangfangGetClaimReq.setDateFieldName("approved_date");
        kangfangGetClaimReq.setTypeCodes(Arrays.asList("YXPT02"));
        kangfangGetClaimReq.setStartDatetime(fifteenthDayOfMonth.getTime());
        kangfangGetClaimReq.setEndDatetime(System.currentTimeMillis());
        String res = yjClaimService.kangfangGetClaim(kangfangGetClaimReq);
        cn.hutool.json.JSONObject resJson = JSONUtil.parseObj(res);
        List<cn.hutool.json.JSONObject> list =   resJson.getJSONObject("data").getJSONArray("page_info").toList(cn.hutool.json.JSONObject.class);

        List<String> documentNums =  list.stream().map(item -> item.getStr("document_num")).collect(Collectors.toList());

        list = apiMethod.getAClaimsByTypeId( "155918",documentNums);
        log.info("list:{}", list);
        list = list.stream().filter(item-> StringUtils.hasText(item.getStr("column45"))).collect(Collectors.toList());

        Map<String, List<cn.hutool.json.JSONObject>> map = list.stream().map(item -> {
            cn.hutool.json.JSONObject jsonObject = (cn.hutool.json.JSONObject) item.getJSONArray("header_links").get(0);
            item.putOnce("pre_code", jsonObject.getStr("code"));
            return item;
        }).collect(Collectors.groupingBy(item -> item.getStr("column45")));
        log.info("map:{}", map);
        for(String key : map.keySet()) {
            List<cn.hutool.json.JSONObject> value = map.get(key);
            if(value.size() > 0){
                //表头
                ExpClaimHeaderVo expClaimHeaderVo = new ExpClaimHeaderVo();
                cn.hutool.json.JSONObject first = value.get(0);
                // 设置外部系统单据号
                expClaimHeaderVo.setExternalId(IdUtil.simpleUUID());
                // 设置单据类型编码
                expClaimHeaderVo.setHeaderTypeCode("YXPT03");
                // 设置单据状态
                expClaimHeaderVo.setStatus("incomplete");
                // 平台项目立项的申请人
                expClaimHeaderVo.setCreatedByCode(first.getJSONObject("charge_user").getStr("code"));
                // 平台项目立项的申请人
                expClaimHeaderVo.setCreatedByUserName(first.getJSONObject("charge_user").getStr("full_name"));
                // 平台项目立项的申请人
                expClaimHeaderVo.setSubmitUserCode(first.getJSONObject("charge_user").getStr("code"));
                // 平台项目执行计划所属公司
                expClaimHeaderVo.setSubmitUserName(first.getJSONObject("charge_user").getStr("full_name"));
                // 平台项目执行计划所属公司
                //            expClaimHeaderVo.setBranchId(first.getInt("branch_id"));
                expClaimHeaderVo.setBranchCode(first.getJSONObject("branch").getStr("code"));
                // 平台项目立项的申请人
                expClaimHeaderVo.setChargeDepartmentCode(first.getJSONObject("submit_department").getStr("code"));
                // 平台项目立项的申请人
                expClaimHeaderVo.setSubmitDepartmentCode(first.getJSONObject("submit_department").getStr("code"));
                //预期期间
                expClaimHeaderVo.setPeriodName(first.getJSONObject("gl_period_vo").getStr("code"));
                expClaimHeaderVo.setColumn38(first.getStr("column38"));
                expClaimHeaderVo.setColumn41(first.getStr("column41"));
                //平台系列编号
                expClaimHeaderVo.setColumn45(key);
                List<LinkHeaderVo> linkHeaderVos = new ArrayList<>();
                //claim_lines
                List<ExpClaimLineVo> lineVos = new ArrayList<>();

                BigDecimal total_amount = new BigDecimal(0);

                for (cn.hutool.json.JSONObject item : value) {
                    total_amount = total_amount.add(item.getBigDecimal("total_amount"));
                    //构建前序单据关联行
                    LinkHeaderVo linkHeaderVo = new LinkHeaderVo();
                    linkHeaderVo.setDocumentNum(item.getStr("code"));
                    linkHeaderVo.setHeaderTypeCode(item.getJSONObject("header_type").getStr("code"));
                    linkHeaderVos.add(linkHeaderVo);
                    //明细行参数构建
                    JSONArray jsonArray = item.getJSONArray("claim_lines");
                    for (Object obj : jsonArray) {
                        cn.hutool.json.JSONObject jsonObject = (cn.hutool.json.JSONObject)obj;
                        ExpClaimLineVo expClaimLineVo = new ExpClaimLineVo();
                        expClaimLineVo.setLineTypeCode(jsonObject.getJSONObject("exp_type").getStr("code"));
                        expClaimLineVo.setCostCenterCode(jsonObject.getJSONObject("cost_center").getStr("code"));
                        expClaimLineVo.setColumn16(jsonObject.getStr("column16"));
                        expClaimLineVo.setColumn42(jsonObject.getStr("column42"));
                        expClaimLineVo.setReceiptAmount(jsonObject.getBigDecimal("receipt_amount"));
                        lineVos.add(expClaimLineVo);
                    }
                }
                expClaimHeaderVo.setExpClaimLineVoList(lineVos);
                expClaimHeaderVo.setLinkHeaderVos(linkHeaderVos);
                expClaimHeaderVo.setTotalAmount(total_amount);
                yjClaimService.createClaim(expClaimHeaderVo);
            }
        }
    }

    @org.junit.Test
    public void test6() throws Exception {

//        String fileName = "C:\\Users\\<USER>\\Downloads\\营销项目编号搜索结果导出 (3).xls";
//        List<ProjectInfo> projectInfoList = EasyExcel.read(fileName, ProjectInfo.class, new PageReadListener<ProjectInfo>(dataList -> {
//            // 将读取的数据存储到List中
//
//        })).sheet().doReadSync();
//
//        projectInfoList = projectInfoList.stream().filter(item -> "启用".equals(item.getStatus())).collect(Collectors.toList());
//        Map<String, List<ProjectInfo>> map = projectInfoList.stream().collect(Collectors.groupingBy(ProjectInfo::getProjectNumber));
//
//
//
        List<LovsDto> lovsDtos = new ArrayList<>();
        List<IitProject> iitProjects = iitProjectService.list();

//        for(IitProject iitProject : iitProjects){
//            iitProject.setProjectCode(iitProject.getProjectName());
//            iitProjectService.updateById(iitProject);
//        }


        for(IitProject iitProject : iitProjects){
//            if(map.containsKey(iitProject.getProjectName())){
//                if("AK104-IIT-054".equals(iitProject.getProjectName())) {
//                    System.out.println("");
//                }
//                List<ProjectInfo> projectInfo = map.get(iitProject.getProjectName());
//                for(ProjectInfo project : projectInfo){
//                   ReflectionUtil.setFieldValue(iitProject, "internalOrder"+project.getCompanyCode(),project.getInternalOrderNumber());
//                   iitProjectService.updateById(iitProject);
                    LovsDto lovsDto = new LovsDto();
                    lovsDto.setCode("Project_number");
                    lovsDto.setValue(iitProject.getProjectName());
                    lovsDto.setType(iitProject.getProjectCode());
                    lovsDto.setEnabledFlag("Y");
                    lovsDto.setColumn3("01");
                    lovsDto.setColumn4(iitProject.getTotalAmount());
                    lovsDto.setColumn5(iitProject.getProjectManager());
                    lovsDto.setColumn6(iitProject.getDepartmentCode());

                    lovsDto.setColumn1(iitProject.getInternalOrder1000());
                    lovsDto.setColumn2(iitProject.getInternalOrder1050());
                    lovsDto.setColumn7(iitProject.getInternalOrder1030());
                    lovsDto.setColumn8(iitProject.getInternalOrder1020());
                    lovsDto.setColumn9(iitProject.getInternalOrder1070());
                    lovsDto.setColumn10(iitProject.getInternalOrder1060());
                    lovsDto.setColumn11(iitProject.getInternalOrder1080());
                    lovsDto.setColumn12(iitProject.getInternalOrder1230());
                    lovsDto.setColumn13(iitProject.getInternalOrder1250());
                    lovsDto.setColumn14(iitProject.getInternalOrder1200());
                    lovsDto.setColumn15(iitProject.getInternalOrder1100());
                    lovsDto.setColumn16(iitProject.getInternalOrder2100());
                    lovsDtos.add(lovsDto);
//                }
//            }

        }
        cloudpenseApiService.syncLovs(lovsDtos);
        // 此时projectInfoList包含了Excel文件中的数据
//        System.out.println(projectInfoList);

    }


    @org.junit.Test
    public void test5() throws Exception {

        ExpClaimHeaderCommonVo claim = null;
        claim = new ExpClaimHeaderCommonVo();
        List<ExpClaimLineCommonVo> claimList = claim.getExpClaimLineVoList();

        JSONArray claimLines = new JSONArray();
        for(ExpClaimLineCommonVo claimLine : claimList){
            log.info("claimLine:{}", claimLine);
            JSONObject jsonObject = new JSONObject();



        }



        ExpClaimHeader expClaimHeader = new ExpClaimHeader();
//        expClaimHeader.setHeaderId(claim.getHeaderId());
//        expClaimHeader.setId(claim.getId());
//        expClaimHeader.getExpClaimLineVoList()

        yjClaimService.updateClaimHeader(expClaimHeader);
    }
    @org.junit.Test
    public void test4() throws Exception {


    }


    @org.junit.Test
    public void test3() throws Exception {

        BudgetParamDto paramDto = new BudgetParamDto();

        paramDto.setPeriodName("2024年04月");

//        ProjectDTO projectDTO = new ProjectDTO();
//        List<Object> projectCodes = new ArrayList<>();
//        projectCodes.add("AK104-IIT-C-0142");
//        projectDTO.setProjectCodes(projectCodes);
//
//        List<Object> iitProjects = yjProjectService.findProjectByPage(projectDTO);
//        cn.hutool.json.JSONObject iitProjectJson = (cn.hutool.json.JSONObject) iitProjects.get(0);

        paramDto.setProjectCode("AK104-IIT-C-0142");

        BudgetResDto budgetDto = budgetService.getBudgetDto(paramDto);
        log.info("budgetDto:{}", budgetDto);

    }

    @org.junit.Test
    public void test() throws Exception {


    }

    @org.junit.Test
    public void test2() throws Exception {
        JSONObject requestJson = JSONObject.parseObject(request);
        ExpClaimHeaderCommonVo claim = null;
        if (requestJson.get("claimVo") != null) {
            claim = JSONUtil.toBean(requestJson.getString("claimVo"), ExpClaimHeaderCommonVo.class);
        }

        //更新云简表单
        ProjectDTO projectDTO = new ProjectDTO();
        List<Object> projectCodes = new ArrayList<>();
        projectCodes.add("AK100494");
        projectDTO.setProjectCodes(projectCodes);

        List<Object> iitProjects = yjProjectService.findProjectByPage(projectDTO);
        cn.hutool.json.JSONObject iitProjectJson = (cn.hutool.json.JSONObject) iitProjects.get(0);
        ExpClaimHeader expClaimHeader = new ExpClaimHeader();
        expClaimHeader.setColumn24(iitProjectJson.getStr("id"));
        expClaimHeader.setProjectName(iitProjectJson.getStr("id"));
        expClaimHeader.setHeaderId(claim.getHeaderId());
        expClaimHeader.setId(claim.getId());
//        String jsonText = objectMapper.writeValueAsString(expClaimHeader);
        ServiceRes serviceRes = yjClaimService.updateClaimHeader(expClaimHeader);
        log.info("YJ表单更新 serviceRes:{}", serviceRes);
    }


    public static String getToken() {

        JSONObject map = new JSONObject();
        map.put("grant_type", "password");
//        map.put("username", "OATB");
//        map.put("password", "Jcyv2ori#");
        map.put("username", "demo2");
        map.put("password", "Jcyv2ori#");
        String post;
        try {
            log.info("获取token中...");
            post = HttpUtil.sendPostJson("https://akesobiouat.recloud.com.cn/t/akesobiouat/token" + "", map.toString());
            JSONObject jsonObject = JSONObject.parseObject(post);
            log.info("获取token成功：{}", jsonObject.get("access_token"));
            return jsonObject.get("access_token").toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String pushIITProject(JSONArray array, String token) throws Exception {
        //Authorization: Bearer {{AUTH_TOKEN}}
        //Content-Type: application/json
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization",  token);
        headers.put("Content-Type", "application/json");
        String post = HttpUtil.sendPostJson("https://akesobiouat.recloud.com.cn/t/akesobiouat" + "/api/akesobiocsd/seriesproject/number/save", array.toString(), headers);
        return post;
    }
    private static String request = "{\n" +
            "    \"claimVo\": {\n" +
            "        \"expClaimLineBudgetAdjustmentDtos\": [],\n" +
            "        \"note\": \"\",\n" +
            "        \"code\": \"REQ0000000276\",\n" +
            "        \"expClaimLineCustomVoList\": [],\n" +
            "        \"glPeriod\": 11,\n" +
            "        \"column41\": \"GX01\",\n" +
            "        \"headerTypeVo\": {\n" +
            "            \"code\": \"YXIIT01\",\n" +
            "            \"groupNum\": 60,\n" +
            "            \"internalType\": \"request\",\n" +
            "            \"type\": \"YXIIT01-IIT立项申请\",\n" +
            "            \"priority\": 41,\n" +
            "            \"enabled\": \"Y\",\n" +
            "            \"requestRequiredFlag\": \"N\",\n" +
            "            \"accountMergeSplitType\": \"corporate_private_split\",\n" +
            "            \"groupName\": \"IIT业务\",\n" +
            "            \"id\": 154312,\n" +
            "            \"configTypeCode\": \"gl_none\",\n" +
            "            \"autoClose\": \"Y\"\n" +
            "        },\n" +
            "        \"branchVo\": {\n" +
            "            \"departmentName\": \"康方赛诺医药有限公司\",\n" +
            "            \"code\": \"1030\",\n" +
            "            \"departmentCode\": \"1030\",\n" +
            "            \"departmentId\": 99075,\n" +
            "            \"companyName\": \"康方赛诺医药有限公司\",\n" +
            "            \"id\": 99075\n" +
            "        },\n" +
            "        \"expClaimLineBudgetVoList\": [],\n" +
            "        \"column38Obj\": {\n" +
            "            \"company_id\": 23049,\n" +
            "            \"creation_date\": *************,\n" +
            "            \"enabled_flag\": \"Y\",\n" +
            "            \"value_meaning\": \"医学研究\\n（IIT）\",\n" +
            "            \"value_code\": \"yw06\",\n" +
            "            \"id\": 157463,\n" +
            "            \"value_id\": 953456,\n" +
            "            \"last_update_date\": *************\n" +
            "        },\n" +
            "        \"submitUserVo\": {\n" +
            "            \"code\": \"\",\n" +
            "            \"mobile\": \"13560928140\",\n" +
            "            \"fullName\": \"陈定乾\",\n" +
            "            \"userName\": \"<EMAIL>\",\n" +
            "            \"employeeNumber\": \"\",\n" +
            "            \"emailAddress\": \"<EMAIL>\",\n" +
            "            \"id\": 249483\n" +
            "        },\n" +
            "        \"headerId\": 792,\n" +
            "        \"submitDepartment\": 99108,\n" +
            "        \"documentNum\": \"REQ0000000276\",\n" +
            "        \"chargeUser\": 249483,\n" +
            "        \"submitDeptVo\": {\n" +
            "            \"departmentName\": \"IT部\",\n" +
            "            \"code\": \"1794b17cf8bd856fcd80f074009aaf95\",\n" +
            "            \"departmentCode\": \"1794b17cf8bd856fcd80f074009aaf95\",\n" +
            "            \"departmentId\": 99108,\n" +
            "            \"id\": 99108\n" +
            "        },\n" +
            "        \"id\": 792,\n" +
            "        \"expClaimLineVoList\": [],\n" +
            "        \"chargeUserVo\": {\n" +
            "            \"code\": \"\",\n" +
            "            \"mobile\": \"13560928140\",\n" +
            "            \"fullName\": \"陈定乾\",\n" +
            "            \"userName\": \"<EMAIL>\",\n" +
            "            \"employeeNumber\": \"\",\n" +
            "            \"emailAddress\": \"<EMAIL>\",\n" +
            "            \"id\": 249483\n" +
            "        },\n" +
            "        \"branchId\": 99075,\n" +
            "        \"columnJson\": \"{\\\"column158\\\": \\\"test\\\", \\\"column186\\\": \\\"3\\\"}\",\n" +
            "        \"headerTypeId\": 154312,\n" +
            "        \"column41Obj\": {\n" +
            "            \"column1\": \"99075\",\n" +
            "            \"company_id\": 23049,\n" +
            "            \"creation_date\": 1725007157000,\n" +
            "            \"enabled_flag\": \"Y\",\n" +
            "            \"value_meaning\": \"AK112\",\n" +
            "            \"value_code\": \"GX01\",\n" +
            "            \"id\": 157471,\n" +
            "            \"value_id\": 953596,\n" +
            "            \"last_update_date\": 1728530986000\n" +
            "        },\n" +
            "        \"expClaimTimeLineVoList\": [],\n" +
            "        \"column38\": \"yw06\",\n" +
            "        \"deliveries\": [],\n" +
            "        \"submitUser\": 249483,\n" +
            "        \"expClaimLineBudgetTransferDtos\": [],\n" +
            "        \"glPeriodVo\": {\n" +
            "            \"periodId\": \"11\",\n" +
            "            \"code\": \"2024年10月\",\n" +
            "            \"levelName\": \"1\",\n" +
            "            \"periodStatus\": \"O\",\n" +
            "            \"parentId\": 1,\n" +
            "            \"companyId\": 23049,\n" +
            "            \"id\": 11\n" +
            "        }\n" +
            "    },\n" +
            "    \"mqQueue\": {\n" +
            "        \"headerTypeId\": 154312,\n" +
            "        \"lastUpdateDate\": 1729146844000,\n" +
            "        \"creationDate\": 1729146844000,\n" +
            "        \"platform\": \"cloudpense\",\n" +
            "        \"headerId\": 792,\n" +
            "        \"content\": \"{\\\"userid\\\": null, \\\"content\\\": null, \\\"header_id\\\": 792}\",\n" +
            "        \"eventCode\": \"expapproved\",\n" +
            "        \"companyId\": 23049,\n" +
            "        \"exchange\": \"wf_claim\",\n" +
            "        \"id\": 1539,\n" +
            "        \"interfaceName\": \"IITProjectNumberCreate\",\n" +
            "        \"rootingKey\": \"mq-aliqa-customize-claim-push\",\n" +
            "        \"status\": 0\n" +
            "    }\n" +
            "}";

    //电子档案凭证文件上传与回传备份接口测试
    @org.junit.Test
    public void testArchiveVoucherUploadAndBack() throws Exception {
        log.info("开始测试电子档案凭证文件上传与回传备份接口");
        
        // 准备测试数据
        String baseUrl = "http://localhost:8329/kangfang"; // 加上context-path
        String apiUrl = baseUrl + "/archive/voucher/uploadAndBack";
        
        // 创建测试PDF文件内容（简单的PDF头部）
        String pdfContent = "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF";
        byte[] pdfBytes = pdfContent.getBytes();
        
        // 设置请求头参数
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/octet-stream");
        headers.add("unique", "test_voucher_" + System.currentTimeMillis());
        headers.add("file_name", "test_voucher.pdf");
        headers.add("gl_status", "SUCCESS");
        headers.add("journal_num", "JN" + System.currentTimeMillis());
        headers.add("gl_message", "测试凭证处理成功");
        headers.add("gl_date", String.valueOf(System.currentTimeMillis()));
        headers.add("ledger_name", "测试总账");
        headers.add("poster_user_code", "TEST001");
        headers.add("poster_user_name", "测试用户");
        
        // 创建请求实体
        HttpEntity<byte[]> requestEntity = new HttpEntity<>(pdfBytes, headers);
        
        try {
            log.info("发送请求到: {}", apiUrl);
            log.info("请求头: {}", headers);
            log.info("文件大小: {} bytes", pdfBytes.length);
            
            // 发送POST请求（注意：这里需要根据实际情况配置RestTemplate）
            // ResponseEntity<String> responseEntity = restTemplate.exchange(
            //     URI.create(apiUrl), 
            //     HttpMethod.POST, 
            //     requestEntity, 
            //     String.class
            // );
            
            // 模拟响应处理
            log.info("请求构建完成，实际调用需要配置RestTemplate");
            log.info("测试参数验证通过:");
            log.info("- unique: {}", headers.getFirst("unique"));
            log.info("- file_name: {}", headers.getFirst("file_name"));
            log.info("- gl_status: {}", headers.getFirst("gl_status"));
            log.info("- journal_num: {}", headers.getFirst("journal_num"));
            log.info("- 文件内容长度: {} bytes", pdfBytes.length);
            
            System.out.println("电子档案凭证文件上传与回传备份接口测试完成");
            
        } catch (Exception e) {
            log.error("测试异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 电子档案凭证接口调用示例方法
     * 展示如何使用HttpClient调用修改后的接口
     */
    public void archiveVoucherApiExample() throws Exception {
        // 这是一个示例方法，展示如何调用修改后的接口
        
        String apiUrl = "http://your-server:port/kangfang/archive/voucher/uploadAndBack";
        
        // 1. 准备文件内容（字节数组）
        byte[] pdfBytes = readFileToBytes("path/to/your/voucher.pdf");
        
        // 2. 设置请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/octet-stream");
        headers.put("unique", "voucher_" + System.currentTimeMillis());
        headers.put("file_name", "voucher.pdf");
        headers.put("gl_status", "SUCCESS");
        headers.put("journal_num", "JN202501230001");
        headers.put("gl_message", "凭证处理成功");
        headers.put("gl_date", String.valueOf(System.currentTimeMillis()));
        headers.put("ledger_name", "总账");
        headers.put("poster_user_code", "USER001");
        headers.put("poster_user_name", "张三");
        
        // 3. 发送请求（使用Apache HttpClient示例）
        /*
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(apiUrl);
            
            // 设置请求头
            for (Map.Entry<String, String> header : headers.entrySet()) {
                httpPost.setHeader(header.getKey(), header.getValue());
            }
            
            // 设置请求体
            httpPost.setEntity(new ByteArrayEntity(pdfBytes));
            
            // 执行请求
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                System.out.println("响应: " + responseBody);
            }
        }
        */
        
        log.info("接口调用示例方法执行完成");
    }
    
    /**
     * 辅助方法：读取文件为字节数组
     */
    private byte[] readFileToBytes(String filePath) throws Exception {
        // 实际实现中需要读取文件
        // return Files.readAllBytes(Paths.get(filePath));
        
        // 这里返回测试用的PDF内容
        String testPdf = "%PDF-1.4\nTest PDF Content\n%%EOF";
        return testPdf.getBytes();
    }

}



