package com.cloudpense.akesobio.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-12-06 18:25
 **/
@NoArgsConstructor
@Data
public class DetailOapiDto {
    /**
     * 飞机
     */
    @JsonProperty("flight")
    private List<FlightOapiDto> flight;
    /**
     * 火车
     */
    @JsonProperty("train")
    private List<TrainOapiDto> train;

    /**
     * 酒店
     */
    @JsonProperty("hotel")
    private List<HotelOapiDto> hotel;

    /**
     * 用餐
     */
    private List<MealOapiDto> meal;

    /**
     * 乘机人
     */
    @JsonProperty("passengers")
    private List<PassengersOapiDto> passengers;
    @JsonProperty("detail")
    private List<FlightOapiDto> detail;
    @JsonProperty("source_type")
    private String sourceType;
    @JsonProperty("source")
    private String source;
    @JsonProperty("remarks_list")
    private List<String> remarksList;
    @JsonProperty("order_info")
    private OrderInfoOapiDto orderInfo;

}
