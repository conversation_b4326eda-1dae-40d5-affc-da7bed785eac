package com.cloudpense.akesobio.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023-12-06 18:25
 **/
@NoArgsConstructor
@Data
public class OrderInfoOapiDto {
    @JsonProperty("amount")
    private BigDecimal amount;
    @JsonProperty("service_price")
    private BigDecimal servicePrice;
    @JsonProperty("price")
    private BigDecimal price;
    @JsonProperty("oil_fee")
    private Integer oilFee;
    @JsonProperty("document_num")
    private String documentNum;
    @JsonProperty("order_id")
    private String orderId;
    @JsonProperty("taxation")
    private Integer taxation;
    @JsonProperty("airport_fee")
    private Integer airportFee;
    @JsonProperty("end_date")
    private String endDate;
    @JsonProperty("rooms")
    private Integer rooms;
    @JsonProperty("amount")
    private Integer amountX;
    @JsonProperty("star")
    private String star;
    @JsonProperty("person_pay")
    private Integer personPay;
    @JsonProperty("hotel_name")
    private String hotelName;
    @JsonProperty("company_pay")
    private BigDecimal companyPay;
    @JsonProperty("city_name")
    private String cityName;
    @JsonProperty("price")
    private Integer priceX;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("meal_description")
    private String mealDescription;
    @JsonProperty("city_id")
    private Integer cityId;
    @JsonProperty("room_type")
    private String roomType;
    @JsonProperty("start_date")
    private String startDate;
    @JsonProperty("scene_name")
    private String sceneName;
}
