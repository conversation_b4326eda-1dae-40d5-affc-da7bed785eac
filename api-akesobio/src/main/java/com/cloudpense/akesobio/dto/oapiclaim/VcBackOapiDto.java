package com.cloudpense.akesobio.dto.oapiclaim;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023-11-15 16:22
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VcBackOapiDto {

    @JSONField(name = "unique")
    private String unique;
    @JSONField(name = "journal_num")
    private String journalNum;
    @JSONField(name = "gl_status")
    private String glStatus;
    @J<PERSON>NField(name = "gl_message")
    private String glMessage;
    @JSONField(name = "gl_date")
    private Long glDate;
    @JSONField(name = "ledger_name")
    private String ledgerName;
    @J<PERSON><PERSON>ield(name = "file_name")
    private String fileName;
    @JSONField(name = "attachment_url")
    private String attachmentUrl;
    @JSONField(name = "poster_user_code")
    private String posterUserCode;
    @JSONField(name = "column39")
    private String column39;
}
