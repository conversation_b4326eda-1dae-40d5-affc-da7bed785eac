package com.cloudpense.akesobio.controller;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.akesobio.dto.WorkFlowStatusDto;
import com.cloudpense.akesobio.service.ReimbursementService;
import com.cloudpense.akesobio.util.http.HttpUtil;
import com.cloudpense.common.bean.MqQueue;
import com.cloudpense.common.vo.ServiceRes;
import com.cloudpense.standard.enumeration.claim.WorkflowStatusEnum;
import com.cloudpense.standard.vo.claim.ExpClaimHeaderCommonVo;
import com.cloudpense.standard.vo.masterdata.CityVo;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/HrController")
public class HrController {

    @Resource
    private ReimbursementService reimbursementService;


    @Value("${hr.addBusinessTripUrl}")
    private String addBusinessTripUrl;

    @Value("${dormitorySystem.handleDormitoryCheckInUrl}")
    private String handleDormitoryCheckInUrl;

    @Value("${dormitorySystem.dormitoryAuthKey}")
    private String dormitoryAuthKey;



    //todo 增加宿舍申请入住信息对接
    @PostMapping("addBusinessTrip")
    @ApiOperation("云简业财系统的差旅申请单需对接考勤系统，出勤、餐费数据需对应处理")
    public ServiceRes addBusinessTrip(@RequestBody String request) {
        log.info("HrController/addBusinessTrip: 云简业财系统的差旅申请单需对接考勤系统，出勤、餐费数据需对应处理 request: " + request);
        WorkFlowStatusDto statusDto = new WorkFlowStatusDto();
        try {
            ExpClaimHeaderCommonVo claim = parseRequest(request,statusDto);
            String jobNum = claim.getChargeUserVo().getCode();
            String employeeName = claim.getChargeUserVo().getFullName();

            // 处理出差考勤信息
             handleBusinessTrip(claim, jobNum);

            // 处理宿舍入住信息
            handleDormitoryCheckIn(claim, jobNum, employeeName);

            statusDto.setStatus(WorkflowStatusEnum.APPROVED.getCode());
            statusDto.setNote("推送考勤系统和宿舍系统成功");
        } catch (Exception e) {
            log.error("HrController/addBusinessTrip: 云简业财系统的差旅申请单需对接考勤系统，出勤、餐费数据需对应处理失败,{}", e.getMessage(), e);
            statusDto.setNote("推送考勤系统或宿舍系统失败");
            statusDto.setStatus(WorkflowStatusEnum.APPROVING.getCode());
            return reimbursementService.updateWorkFlowStatus(statusDto);
        }
        return reimbursementService.updateWorkFlowStatus(statusDto);
    }



    private ExpClaimHeaderCommonVo parseRequest(String request,WorkFlowStatusDto statusDto) throws Exception {
        JSONObject requestJson = JSONObject.parseObject(request);
        MqQueue mqQueue = JSONUtil.toBean(requestJson.getJSONObject("mqQueue").toString(), MqQueue.class);

        statusDto.setPathId(mqQueue.getPathId());
        statusDto.setEmployeeNumber("KF001");

        if (requestJson.get("claimVo") == null) {
            throw new Exception("claimVo is null");
        }
        return JSONUtil.toBean(requestJson.getString("claimVo"), ExpClaimHeaderCommonVo.class);
    }

    private String handleBusinessTrip(ExpClaimHeaderCommonVo claim, String jobNum) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        ObjectMapper objectMapper = new ObjectMapper();

        Map<String, Object> businessTrip = new HashMap<>();
        businessTrip.put("nid", claim.getCode());
        businessTrip.put("job_num", jobNum);
        businessTrip.put("start_businesstrip_date", sdf.format(claim.getStartDatetime()));
        businessTrip.put("end_businesstrip_date", sdf.format(claim.getEndDatetime()));
        businessTrip.put("peers", jobNum);

        String achieveFactory = getAchieveFactory(claim.getColumn28());
        businessTrip.put("kf_zone", achieveFactory);
        businessTrip.put("reason", claim.getDescription());
        businessTrip.put("destination", getDestination(claim.getDestinationCities()));

        List<Map<String, Object>> dataList = new ArrayList<>();
        dataList.add(businessTrip);
        Map<String, List<Map<String, Object>>> rootMap = new HashMap<>();
        rootMap.put("data", dataList);

        String json = objectMapper.writeValueAsString(rootMap);
        return HttpUtil.sendPostJsonWithRetry(addBusinessTripUrl, json, null,3);
    }

    private void handleDormitoryCheckIn(ExpClaimHeaderCommonVo claim, String jobNum, String employeeName) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        ObjectMapper objectMapper = new ObjectMapper();

        String achieveFactory = getAchieveFactory(claim.getColumn28());
        String accommodation = "1"; // 默认需要住宿

        if (claim.getColumn28().equals("settle_factory_01")) {
            accommodation = "0"; // 不需要住宿
        }

        Map<String, String> scheduleInfo = new HashMap<>();
        scheduleInfo.put("accommodation", accommodation);
        scheduleInfo.put("achieveFactory", achieveFactory);
        scheduleInfo.put("checkInStartTimeStr", sdf.format(claim.getStartDatetime()));
        scheduleInfo.put("checkInEndTimeStr", sdf.format(claim.getEndDatetime()));

        List<Map<String, String>> scheduleInfoList = new ArrayList<>();
        scheduleInfoList.add(scheduleInfo);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("employeeName", employeeName);
        requestBody.put("employeeCode", jobNum);
        requestBody.put("employeePeersId", jobNum);
        requestBody.put("scheduleInfo", scheduleInfoList);

        String json = objectMapper.writeValueAsString(requestBody);

        Map<String, String> headers = new HashMap<>();
        headers.put("kangfangDormitoryKey", dormitoryAuthKey);

        String result = HttpUtil.sendPostJsonWithRetry(handleDormitoryCheckInUrl, json, headers, 3);
        log.info("HrController/addDormitoryCheckIn: 云简业财系统的差旅申请单需对接宿舍系统，推送员工出差入住申请 result: " + result);

        JSONObject resultJson = JSONObject.parseObject(result);
        if (resultJson.getInteger("code") != 200) {
            throw new Exception("推送宿舍系统失败: " + resultJson.getString("msg"));
        }
    }

    private String getAchieveFactory(String column28) throws Exception {
        switch (column28) {
            case "settle_factory_01":
                return "1";
            case "settle_factory_02":
                return "2";
            case "settle_factory_03":
                return "3";
            default:
                throw new Exception("settle_factory_01,settle_factory_02,settle_factory_03 is null");
        }
    }
    private String getDestination(List<CityVo> destinationCities) {
        StringBuilder destination = new StringBuilder();
        for (CityVo cityVo : destinationCities) {
            destination.append(cityVo.getCityName()).append(",");
        }
        return destination.toString();
    }
}
