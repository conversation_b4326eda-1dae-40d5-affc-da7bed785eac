package com.cloudpense.akesobio.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.akesobio.dto.WorkFlowStatusDto;
import com.cloudpense.akesobio.entity.DeliverieMqueue;
import com.cloudpense.akesobio.service.ReimbursementService;
import com.cloudpense.akesobio.util.FkUtil;
import com.cloudpense.akesobio.vo.ValResultCommonVo;
import com.cloudpense.common.bean.MqQueue;
import com.cloudpense.common.bean.Response;
import com.cloudpense.common.util.YjUtil;
import com.cloudpense.common.vo.ServiceRes;
import com.cloudpense.standard.enumeration.claim.WorkflowStatusEnum;
import com.cloudpense.standard.vo.claim.ExpClaimHeaderCommonVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;


@Slf4j
@RestController
@RequestMapping("/reimbursement")
public class ReimbursementController {

    @Autowired
    ReimbursementService reimbursementService;

    @PostMapping("/travelReimbursement")
    @ApiOperation("校验个人报销-报销总金额不能超过申请单总金额的20%")
    ValResultCommonVo travelReimbursement(@RequestBody String param) throws Exception {
        log.info("校验个人报销-报销总金额不能超过申请单总金额的20%,入参：{}", param);
        ValResultCommonVo<String> valResultCommonVo = new ValResultCommonVo<>();
        ExpClaimHeaderCommonVo vo = FkUtil.parse(param);
        valResultCommonVo = reimbursementService.getPersonalReimbursement(vo);
        log.info("校验个人报销-报销总金额不能超过申请单总金额的20%,返参：{}", valResultCommonVo);
        return valResultCommonVo;
    }

    @PostMapping("/contractApplication")
    @ApiOperation("校验采购合同关联申请单,申请单总金额要大于等于合同总金额")
    ValResultCommonVo contractApplication(@RequestBody String param) throws Exception {
        log.info("校验采购合同关联申请单,申请单总金额要大于等于合同总金额,入参：{}", param);
        ValResultCommonVo<String> valResultCommonVo = new ValResultCommonVo<>();
        ExpClaimHeaderCommonVo vo = FkUtil.parse(param);
        valResultCommonVo = reimbursementService.getContractApplication(vo);
        log.info("校验采购合同关联申请单,申请单总金额要大于等于合同总金额,返参：{}", valResultCommonVo);
        return valResultCommonVo;
    }

    @PostMapping("/corporatePayment")
    @ApiOperation("校验对公支付关联合同审批,对公支付总金额需要在合同总金额的90%-110%之间")
    ValResultCommonVo corporatePayment(@RequestBody String param) throws Exception {
        log.info("对公合同-支付总金额校验,入参：{}", param);
        ValResultCommonVo<String> valResultCommonVo = new ValResultCommonVo<>();
        ExpClaimHeaderCommonVo vo = FkUtil.parse(param);
        valResultCommonVo = reimbursementService.getCorporatePayment(vo);
        log.info("对公合同-支付总金额校验,返参：{}", valResultCommonVo);
        return valResultCommonVo;
    }

    //收单和不收单 都会触发
    @PostMapping("/receivedInterface")
    @ApiOperation("虚拟岗：先触发虚拟岗后收单 ")
    ServiceRes receivedInterface(@RequestBody JSONObject param) throws Exception {
        log.info("虚拟岗：先触发虚拟岗后收单,入参: {}", JSONObject.toJSONString(param));
        ExpClaimHeaderCommonVo claim = JSONUtil.toBean(param.getJSONObject("claimVo").toString(), ExpClaimHeaderCommonVo.class);
        MqQueue mqQueue = JSONUtil.toBean(param.getJSONObject("mqQueue").toString(), MqQueue.class);
        ServiceRes serviceRes = null;
        try {
            serviceRes = reimbursementService.receivedInterface(claim, mqQueue);
        } catch (Exception e) {
            String msg = e.getMessage();
            log.error("虚拟岗：先触发虚拟岗后收单,异常:{}", msg, e);
        }
        return serviceRes;
    }


    @PostMapping("/deliveryReceived")
    @ApiOperation("虚拟岗：收单触发消息 ")
    Response deliveryReceived(@RequestBody JSONObject param) throws Exception {
        log.info("虚拟岗：收单触发消息,入参: {}", JSONObject.toJSONString(param));
        Response response = new Response();
        List<ExpClaimHeaderCommonVo> lists = JSONArray.parseArray(JSONObject.toJSONString(param.getJSONArray("claimVos")), ExpClaimHeaderCommonVo.class);
        DeliverieMqueue mqQueue = JSONUtil.toBean(param.getJSONObject("mqQueue").toString(), DeliverieMqueue.class);
        ServiceRes serviceRes = null;
        Integer resCode = 200000;
        String resMsg = "收单成功";
        if (CollectionUtils.isEmpty(lists)) {
            return response;
        }
        for (ExpClaimHeaderCommonVo claim : lists) {
            try {
                serviceRes = reimbursementService.deliveryReceived(claim, mqQueue);
            } catch (Exception e) {
                resCode = 500000;
                resMsg = e.getMessage();
                log.error("虚拟岗：收单触发消息,异常:{},claimCode:{}", resMsg, claim.getCode(), e);
            }
        }
        if (!ServiceRes.isSuccess(serviceRes)) {
            resCode = 500000;
            resMsg = serviceRes.getMsg();
        }
        response.setResCode(resCode);
        response.setResMsg(resMsg);
        response.setBizId(UUID.randomUUID().toString());
        return response;
    }

    @PostMapping("/personalTotalAmount")
    @ApiOperation("虚拟岗：费用报销总金额统计")
    ServiceRes personalTotalAmount(@RequestBody String param) throws Exception {
        /*log.info("personalTotalAmount,入参: {}", param.toString());
        return reimbursementService.personalTotalAmount(param);*/
        log.info("费用报销总金额统计,入参: {}", param.toString());
        JSONObject requestJson = JSONObject.parseObject(param);
        MqQueue mqQueue = JSONUtil.toBean(requestJson.getJSONObject("mqQueue").toString(), MqQueue.class);
        WorkFlowStatusDto statusDto = new WorkFlowStatusDto();
        statusDto.setStatus(com.cloudpense.standard.enumeration.claim.WorkflowStatusEnum.APPROVED.getCode());
        statusDto.setNote("费用报销总金额统计汇总成功");
        try{
            reimbursementService.personalTotalAmount(param);
        }catch (Exception e){
            log.error("虚拟岗：费用报销总金额统计,异常:{},入参:{}", e.getMessage(), param, e);
            statusDto.setStatus(WorkflowStatusEnum.REJECTED.getCode());
            statusDto.setNote("编号生成失败");
        }
        statusDto.setPathId(mqQueue.getPathId());
        statusDto.setEmployeeNumber("KF001");
        ServiceRes workFlowRes = reimbursementService.updateWorkFlowStatus(statusDto);
        return workFlowRes;
    }

    @PostMapping("/sharedDocument")
    @ApiOperation("共享费控单据给协办人")
    Response sharedDocument(@RequestBody String param) throws Exception {
        log.info("共享费控单据给协办人,入参: {}", param.toString());
        return reimbursementService.sharedDocument(param);
    }

    @PostMapping("/projectTotalAmount")
    @ApiOperation("校验IIT和平台项目总金额")
    ValResultCommonVo projectTotalAmount(@RequestBody String param) throws Exception {
        log.info("校验IIT和平台项目总金额,入参：{}", param);
        ValResultCommonVo<String> valResultCommonVo = new ValResultCommonVo<>();
        ExpClaimHeaderCommonVo vo = FkUtil.parse(param);
        valResultCommonVo = reimbursementService.projectTotalAmount(vo);
        log.info("校验IIT和平台项目总金额,返参：{}", valResultCommonVo);
        return valResultCommonVo;
    }

    @PostMapping("/JdPurchaseRequisition")
    @ApiOperation("校验京东采购总金额不超前序申请费用行总额汇总金额（K003）")
    ValResultCommonVo jdPurchaseRequisition(@RequestBody String param) throws Exception {
        log.info("校验京东采购总金额不超前序申请费用行总额汇总金额（K003）,入参：{}", param);
        ValResultCommonVo<String> valResultCommonVo = new ValResultCommonVo<>();
        ExpClaimHeaderCommonVo vo = FkUtil.parse(param);
        valResultCommonVo = reimbursementService.jdPurchaseRequisition(vo);
        log.info("校验京东采购总金额不超前序申请费用行总额汇总金额（K003）,返参：{}", valResultCommonVo);
        return valResultCommonVo;
    }

    @PostMapping("/medicationQuantity")
    @ApiOperation("校验实际领药数量")
    ValResultCommonVo medicationQuantity(@RequestBody String param) throws Exception {
        log.info("校验实际领药数量,入参：{}", param);
        ValResultCommonVo<String> valResultCommonVo = new ValResultCommonVo<>();
        ExpClaimHeaderCommonVo vo = FkUtil.parse(param);
        valResultCommonVo = reimbursementService.medicationQuantity(vo);
        log.info("校验实际领药数量,返参：{}", valResultCommonVo);
        return valResultCommonVo;
    }

    @PostMapping("/platformExecutionPlanTotalAmount")
    @ApiOperation("校验平台执行计划总金额")
    ValResultCommonVo platformExecutionPlanTotalAmount(@RequestBody String param) throws Exception {
        log.info("校验平台执行计划总金额,入参：{}", param);
        ValResultCommonVo<String> valResultCommonVo = new ValResultCommonVo<>();
        ExpClaimHeaderCommonVo vo = FkUtil.parse(param);
        valResultCommonVo = reimbursementService.platformExecutionPlanTotalAmount(vo);
        log.info("校验平台执行计划总金额,返参：{}", valResultCommonVo);
        return valResultCommonVo;
    }

    @PostMapping("/sharedDocumentToProjectApplicant")
    @ApiOperation("共享平台执行计划单据给立项申请人")
    Response sharedDocumentToProjectApplicant(@RequestBody String param) throws Exception {
        log.info("共享平台执行计划单据给立项申请人,入参: {}", param.toString());
        return reimbursementService.sharedDocumentToProjectApplicant(param);
    }

    @PostMapping("/communicationFeeTotalAmount")
    @ApiOperation("虚拟岗： 汇总个人月度交际费总金额")
    ServiceRes communicationFeeTotalAmount(@RequestBody String param) throws Exception {
        log.info("汇总个人月度交际费总金额,入参: {}", param.toString());

        JSONObject requestJson = JSONObject.parseObject(param);
        MqQueue mqQueue = JSONUtil.toBean(requestJson.getJSONObject("mqQueue").toString(), MqQueue.class);
        WorkFlowStatusDto statusDto = new WorkFlowStatusDto();

        statusDto.setStatus(com.cloudpense.standard.enumeration.claim.WorkflowStatusEnum.APPROVED.getCode());
        statusDto.setNote("个人月度交际费总金额汇总成功");
        try{
            reimbursementService.communicationFeeTotalAmount(param);
        }catch (Exception e){
            log.error("虚拟岗：汇总个人月度交际费总金额,异常:{},入参:{}", e.getMessage(), param, e);
            statusDto.setStatus(WorkflowStatusEnum.REJECTED.getCode());
            statusDto.setNote("编号生成失败");
        }
        statusDto.setPathId(mqQueue.getPathId());
        statusDto.setEmployeeNumber("KF001");
        ServiceRes workFlowRes = reimbursementService.updateWorkFlowStatus(statusDto);
        return workFlowRes;
    }



    @PostMapping("/verifyCompanyName")
    @ApiOperation("verifyCompanyName")
    ValResultCommonVo verifyCompanyName(@RequestBody String param) throws Exception {
        log.info("校验公司名称,入参：{}", param);
        ValResultCommonVo<String> valResultCommonVo = new ValResultCommonVo<>();
        ExpClaimHeaderCommonVo vo = FkUtil.parse(param);
        valResultCommonVo = reimbursementService.verifyCompanyName(vo);
        log.info("校验公司名称,返参：{}", valResultCommonVo);
        return valResultCommonVo;
    }

    @PostMapping("/loanVerification")
    @ApiOperation("借款校验")
    ValResultCommonVo loanVerification(@RequestBody String param) throws Exception {
        log.info("借款校验,入参：{}", param);
        ValResultCommonVo<String> valResultCommonVo = new ValResultCommonVo<>();
        ExpClaimHeaderCommonVo vo = FkUtil.parse(param);
        valResultCommonVo = reimbursementService.loanVerification(vo);
        log.info("借款校验,返参：{}", valResultCommonVo);
        return valResultCommonVo;
    }

    @PostMapping("/supplierVerification")
    @ApiOperation("校验供应商")
    ValResultCommonVo supplierVerification(@RequestBody String param) throws Exception {
        log.info("校验供应商,入参：{}", param);
        ValResultCommonVo<String> valResultCommonVo = new ValResultCommonVo<>();
        ExpClaimHeaderCommonVo vo = FkUtil.parse(param);
        valResultCommonVo = reimbursementService.supplierVerification(vo);
        log.info("校验供应商,返参：{}", valResultCommonVo);
        return valResultCommonVo;
    }


    @PostMapping("/checkPreDocumentRelations")
    @ApiOperation("校验前序申请单关联次数")
    ValResultCommonVo checkPreDocumentRelations(@RequestBody String param) throws Exception {
        log.info("校验前序申请单关联次数,入参：{}", param);
        ValResultCommonVo<String> valResultCommonVo = new ValResultCommonVo<>();
        ExpClaimHeaderCommonVo vo = FkUtil.parse(param);
        valResultCommonVo = reimbursementService.checkPreDocumentRelations(vo);
        log.info("校验前序申请单关联次数,返参：{}", valResultCommonVo);
        return valResultCommonVo;
    }


}
