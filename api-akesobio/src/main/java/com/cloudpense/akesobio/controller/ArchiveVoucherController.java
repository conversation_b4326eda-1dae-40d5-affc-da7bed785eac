package com.cloudpense.akesobio.controller;

import com.cloudpense.akesobio.dto.VoucherFileUploadResponseDto;
import com.cloudpense.common.service.archive.ArchiveFileUploadService;
import com.cloudpense.common.service.voucher.YjVoucherService;
import com.cloudpense.common.vo.ServiceRes;
import com.cloudpense.common.vo.yj.voucher.VoucherBackReq;
import com.cloudpense.common.vo.yj.voucher.VoucherUpdateReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 电子档案凭证控制器
 * <AUTHOR>
 * @date 2025-01-23
 * @description 处理电子档案凭证文件上传与回传备份业务
 */
@Api(tags = "电子档案凭证管理", description = "电子档案凭证文件上传与回传备份相关接口")
@Slf4j
@RestController
@RequestMapping("/archive/voucher")
public class ArchiveVoucherController {

    /**
     * 云简API成功响应码
     */
    private static final String SUCCESS_CODE = "200000";

    /**
     * 凭证状态：已生成
     */
    private static final String GL_STATUS_GENERATED = "generated";

    /**
     * 凭证状态：错误
     */
    private static final String GL_STATUS_ERROR = "error";

    @Autowired
    private ArchiveFileUploadService archiveFileUploadService;

    @Resource
    private YjVoucherService yjVoucherService;

    /**
     * 电子档案凭证文件上传与回传备份
     *
     * 接口说明：
     * - 单据唯一值unique可自定义配置字段（联系开发），默认配置字段为document_num
     * - 凭证状态gl_status必填，可选值为generated(已生成)、error(错误)
     * - 凭证号journal_num当且仅当凭证状态为generated时必填，其余情况非必填
     * - 总账消息gl_message当且仅当凭证状态为error时可用，其余情况不可用
     * - 记账日期gl_date非必填，为空时默认取当前时间
     * - 帐套名称ledger_name非必填，为空时默认取单据上的主帐套
     * - 如需回传凭证文件时，需要提前上传文件获取文件地址
     * - 如需回传制单人时，poster_user_code和poster_user_name任选其一
     *
     * @param pdfBytes 文件字节数组
     * @param unique 单据唯一值，必填，最大长度64
     * @param fileName 文件名，非必填，最大长度255
     * @param glStatus 凭证状态，必填，可选值：generated(已生成)、error(错误)，最大长度16
     * @param journalNum 凭证号，当凭证状态为generated时必填，最大长度255
     * @param glMessage 总账消息，当凭证状态为error时可用，最大长度255
     * @param glDate 记账日期，非必填，为空时默认取当前时间，时间戳格式13位
     * @param ledgerName 帐套名称，非必填，为空时默认取单据上的主帐套，最大长度255
     * @param posterUserCode 制单人员工号，与poster_user_name任选其一，最大长度64
     * @param posterUserName 制单人用户名，与poster_user_code任选其一，最大长度64
     * @return 上传与回传结果
     */
    @ApiOperation(value = "电子档案凭证文件上传与回传备份",
                  notes = "上传电子档案凭证文件并执行回传备份操作。文件内容通过请求体传递，其他参数通过请求头传递。\n" +
                         "注意事项：\n" +
                         "1. 凭证状态gl_status必填，可选值：generated(已生成)、error(错误)\n" +
                         "2. 当凭证状态为generated时，凭证号journal_num必填\n" +
                         "3. 当凭证状态为error时，总账消息gl_message可用\n" +
                         "4. 制单人信息poster_user_code和poster_user_name任选其一")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "unique", value = "单据唯一值，必填，最大长度64", required = true, dataType = "string", paramType = "header", example = "FD24-20250721001"),
        @ApiImplicitParam(name = "file_name", value = "文件名，非必填，最大长度255", required = false, dataType = "string", paramType = "header", example = "105010502000000005.PDF"),
        @ApiImplicitParam(name = "gl_status", value = "凭证状态，必填，可选值：generated(已生成)、error(错误)", required = true, dataType = "string", paramType = "header", example = "generated"),
        @ApiImplicitParam(name = "journal_num", value = "凭证号，当凭证状态为generated时必填，最大长度255", required = false, dataType = "string", paramType = "header", example = "JV202507300001"),
        @ApiImplicitParam(name = "gl_message", value = "总账消息，当凭证状态为error时可用，最大长度255", required = false, dataType = "string", paramType = "header", example = "凭证处理错误"),
        @ApiImplicitParam(name = "gl_date", value = "记账日期，非必填，为空时默认取当前时间，时间戳格式13位", required = false, dataType = "long", paramType = "header", example = "1737676800000"),
        @ApiImplicitParam(name = "ledger_name", value = "帐套名称，非必填，为空时默认取单据上的主帐套，最大长度255", required = false, dataType = "string", paramType = "header", example = "总账"),
        @ApiImplicitParam(name = "poster_user_code", value = "制单人员工号，与poster_user_name任选其一，最大长度64", required = false, dataType = "string", paramType = "header", example = "EMP001"),
        @ApiImplicitParam(name = "poster_user_name", value = "制单人用户名，与poster_user_code任选其一，最大长度64", required = false, dataType = "string", paramType = "header", example = "张三")
    })
    @PostMapping("/uploadAndBack")
    public VoucherFileUploadResponseDto uploadVoucherFileAndBack(
            @RequestBody byte[] pdfBytes,
            @RequestHeader("unique") String unique,
            @RequestHeader(value = "file_name", required = false) String fileName,
            @RequestHeader("gl_status") String glStatus,
            @RequestHeader(value = "journal_num", required = false) String journalNum,
            @RequestHeader(value = "gl_message", required = false) String glMessage,
            @RequestHeader(value = "gl_date", required = false) Long glDate,
            @RequestHeader(value = "ledger_name", required = false) String ledgerName,
            @RequestHeader(value = "poster_user_code", required = false) String posterUserCode,
            @RequestHeader(value = "poster_user_name", required = false) String posterUserName) {

        VoucherFileUploadResponseDto response = new VoucherFileUploadResponseDto();
        
        try {
            log.info("开始处理电子档案凭证文件上传与回传备份，unique: {}, 文件名: {}", unique, fileName);
            
            // 参数验证
            if (pdfBytes == null || pdfBytes.length == 0) {
                response.setSuccess(false);
                response.setMessage("上传文件内容不能为空");
                return response;
            }
            
            if (!StringUtils.hasText(unique)) {
                response.setSuccess(false);
                response.setMessage("unique参数不能为空");
                return response;
            }
            
            if (unique.length() > 64) {
                response.setSuccess(false);
                response.setMessage("unique参数长度不能超过64个字符");
                return response;
            }
            
            if (!StringUtils.hasText(glStatus)) {
                response.setSuccess(false);
                response.setMessage("gl_status参数不能为空");
                return response;
            }

            // 验证凭证状态的有效性
            if (!GL_STATUS_GENERATED.equals(glStatus) && !GL_STATUS_ERROR.equals(glStatus)) {
                response.setSuccess(false);
                response.setMessage("gl_status参数值无效，可选值为：generated(已生成)或error(错误)");
                return response;
            }

            // 业务规则验证：当凭证状态为generated时，凭证号journal_num必填
            if (GL_STATUS_GENERATED.equals(glStatus) && !StringUtils.hasText(journalNum)) {
                response.setSuccess(false);
                response.setMessage("当凭证状态为generated时，凭证号journal_num必填");
                return response;
            }

            // 业务规则验证：当凭证状态为error时，总账消息gl_message可用，其他情况不建议使用
            if (!GL_STATUS_ERROR.equals(glStatus) && StringUtils.hasText(glMessage)) {
                log.warn("当凭证状态不为error时，不建议使用gl_message参数，unique: {}", unique);
            }
            
            // 长度验证
            if (StringUtils.hasText(fileName) && fileName.length() > 255) {
                response.setSuccess(false);
                response.setMessage("文件名长度不能超过255个字符");
                return response;
            }
            
            if (StringUtils.hasText(journalNum) && journalNum.length() > 255) {
                response.setSuccess(false);
                response.setMessage("凭证号长度不能超过255个字符");
                return response;
            }

            if (StringUtils.hasText(glMessage) && glMessage.length() > 255) {
                response.setSuccess(false);
                response.setMessage("总账消息长度不能超过255个字符");
                return response;
            }

            if (StringUtils.hasText(ledgerName) && ledgerName.length() > 255) {
                response.setSuccess(false);
                response.setMessage("帐套名称长度不能超过255个字符");
                return response;
            }

            if (StringUtils.hasText(posterUserCode) && posterUserCode.length() > 64) {
                response.setSuccess(false);
                response.setMessage("制单人员工号长度不能超过64个字符");
                return response;
            }

            if (StringUtils.hasText(posterUserName) && posterUserName.length() > 64) {
                response.setSuccess(false);
                response.setMessage("制单人用户名长度不能超过64个字符");
                return response;
            }
            
            // 如果文件名为空，生成默认文件名
            if (!StringUtils.hasText(fileName)) {
                fileName = "voucher_" + unique + ".pdf";
                log.info("文件名为空，生成默认文件名: {}", fileName);
            }

            // 设置默认值
            // 记账日期gl_date非必填，为空时默认取当前时间
            if (glDate == null) {
                glDate = System.currentTimeMillis();
                log.info("记账日期为空，设置为当前时间: {}", glDate);
            }

            // 步骤1：上传文件到电子档案系统
            log.info("开始上传电子档案文件: {}", fileName);

            String attachmentUrl;
            try {
                attachmentUrl = archiveFileUploadService.uploadFile(pdfBytes, fileName);
                response.setFileName(fileName);
                response.setAttachmentUrl(attachmentUrl);
                log.info("电子档案文件上传成功，attachment_url: {}", attachmentUrl);
            } catch (Exception e) {
                log.error("电子档案文件上传失败: {}", e.getMessage(), e);
                response.setSuccess(false);
                response.setMessage("文件上传失败: " + e.getMessage());
                return response;
            }
            
            // 步骤2：优先尝试凭证查询和更新流程
            Integer batchId = null;
            boolean useUpdateFlow = false;
            
            // 当凭证状态为generated且有凭证号时，尝试查询已存在的凭证
            if (GL_STATUS_GENERATED.equals(glStatus) && StringUtils.hasText(journalNum)) {
                log.info("开始查询已存在的凭证，凭证号: {}", journalNum);
                try {
                    // 调用凭证查询服务
                    ServiceRes queryResult = yjVoucherService.voucherQuery(journalNum);
                    log.info("凭证查询响应: code={}, msg={}", queryResult.getCode(), queryResult.getMsg());
                    
                    // 检查响应状态码 - 修复：使用正确的成功状态码 200000
                    if (SUCCESS_CODE.equals(queryResult.getCode()) && queryResult.getData() != null) {
                        // 从响应数据中提取batch_id
                        Object data = queryResult.getData();
                        if (data instanceof java.util.Map) {
                            java.util.Map<String, Object> dataMap = (java.util.Map<String, Object>) data;
                            Object batchIdObj = dataMap.get("batch_id");
                            if (batchIdObj != null) {
                                try {
                                    batchId = Integer.valueOf(batchIdObj.toString());
                                    log.info("成功获取到batch_id: {}", batchId);
                                    useUpdateFlow = true;
                                } catch (NumberFormatException e) {
                                    log.warn("batch_id格式转换失败: {}, 将使用新增流程", batchIdObj);
                                }
                            } else {
                                log.warn("响应数据中未找到batch_id字段，将使用新增流程");
                            }
                        } else {
                            log.warn("响应数据格式不正确，期望Map类型，实际类型: {}, 将使用新增流程",
                                data != null ? data.getClass().getSimpleName() : "null");
                        }
                    } else {
                        log.info("凭证查询未找到记录或返回错误，将使用新增流程，code: {}, msg: {}",
                            queryResult.getCode(), queryResult.getMsg());
                    }
                } catch (Exception e) {
                    log.warn("凭证查询调用失败，将使用新增流程，错误: {}", e.getMessage());
                }
            }
            
            // 步骤3：根据查询结果选择处理流程
            VoucherFileUploadResponseDto.VoucherBackResult backResult = new VoucherFileUploadResponseDto.VoucherBackResult();
            
            if (useUpdateFlow && batchId != null) {
                // 使用凭证更新流程
                log.info("使用凭证更新流程，batch_id: {}", batchId);
                try {
                    // 构建凭证更新请求
                    VoucherUpdateReq updateRequest = VoucherUpdateReq.builder()
                        .batch_id(batchId)
                        .attachment_url(attachmentUrl)
                        .build();
                    
                    // 调用凭证更新服务
                    ServiceRes updateResult = yjVoucherService.voucherUpdate(updateRequest);
                    log.info("凭证更新响应: code={}, msg={}", updateResult.getCode(), updateResult.getMsg());

                    // 修复：使用正确的成功状态码 200000
                    if (SUCCESS_CODE.equals(updateResult.getCode())) {
                        backResult.setSuccess(true);
                        backResult.setMessage("凭证更新成功: " + updateResult.getMsg());
                        backResult.setData(updateResult.getData());
                        log.info("凭证更新成功，unique: {}, batch_id: {}", unique, batchId);
                    } else {
                        backResult.setSuccess(false);
                        backResult.setMessage("凭证更新失败: " + updateResult.getMsg());
                        log.error("凭证更新失败，unique: {}, batch_id: {}, 错误: {}", unique, batchId, updateResult.getMsg());
                    }
                } catch (Exception e) {
                    backResult.setSuccess(false);
                    backResult.setMessage("凭证更新调用异常: " + e.getMessage());
                    log.error("凭证更新调用异常，unique: {}, batch_id: {}, 错误: {}", unique, batchId, e.getMessage(), e);
                }
            } else {
                // 使用原有的凭证回传新增流程
                log.info("使用凭证回传新增流程，unique: {}", unique);
                
                // 构建凭证回传请求
                VoucherBackReq voucherBackReq = VoucherBackReq.builder()
                        .unique(unique)
                        .gl_status(glStatus)
                        .journal_num(journalNum)
                        .gl_message(glMessage)
                        .gl_date(glDate)
                        .ledger_name(ledgerName)
                        .file_name(fileName)
                        .attachment_url(attachmentUrl)
                        .poster_user_code(posterUserCode)
                        .poster_user_name(posterUserName)
                        .build();
                
                // 执行凭证回传备份
                log.info("开始执行电子档案凭证回传备份，unique: {}", unique);
                ServiceRes voucherBackResult = yjVoucherService.voucherBack(voucherBackReq);
                
                // 构建回传结果 - 修复：使用正确的成功状态码 200000
                if(SUCCESS_CODE.equals(voucherBackResult.getCode())) {
                    backResult.setSuccess(true);
                } else {
                    backResult.setSuccess(false);
                }
                backResult.setMessage(voucherBackResult.getMsg());
                backResult.setData(voucherBackResult.getData());
                
                log.info("电子档案凭证回传备份处理完成，unique: {}, 回传结果: {}", unique, voucherBackResult.getCode());
            }
            
            response.setVoucherBackResult(backResult);
            response.setSuccess(true);
            response.setMessage("电子档案凭证文件上传与处理完成");
            
            log.info("电子档案凭证文件上传与处理完成，unique: {}, 使用更新流程: {}", unique, useUpdateFlow);
            
        } catch (Exception e) {
            log.error("电子档案凭证文件上传与回传备份处理异常，unique: {}, 错误: {}", unique, e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("处理异常: " + e.getMessage());
        }
        
        return response;
    }

}