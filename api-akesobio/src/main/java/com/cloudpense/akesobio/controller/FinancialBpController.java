package com.cloudpense.akesobio.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.akesobio.dto.WorkFlowStatusDto;
import com.cloudpense.akesobio.service.ReimbursementService;
import com.cloudpense.akesobio.util.FkUtil;
import com.cloudpense.common.enums.WorkflowStatusEnum;
import com.cloudpense.common.service.claim.YjClaimService;
import com.cloudpense.common.vo.ServiceRes;
import com.cloudpense.standard.dao.entity.ExpClaimHeader;
import com.cloudpense.standard.vo.claim.ExpClaimHeaderCommonVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import com.cloudpense.common.bean.MqQueue;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Slf4j
@RequestMapping("/FinancialBpController")
public class FinancialBpController {

    @Resource
    private YjClaimService yjClaimService;

    @Resource
    private ReimbursementService reimbursementService;


    @PostMapping("/getFinancialBpByIitProjectCategory")
    @ApiOperation("根据IIT项目号分类获取财务BP")
    public ServiceRes getFinancialBpByIitProjectCategory(@RequestBody String request) throws Exception {

        log.info("根据IIT项目号分类获取财务BP,入参: {}", request);
        JSONObject requestJson = JSONObject.parseObject(request);

        // 实现根据IIT项目号分类获取财务BP的逻辑
        ExpClaimHeaderCommonVo claim = JSONUtil.toBean(requestJson.getString("claimVo"), ExpClaimHeaderCommonVo.class);
        MqQueue mqQueue = JSONUtil.toBean(requestJson.getJSONObject("mqQueue").toString(), MqQueue.class);
        WorkFlowStatusDto statusDto = new WorkFlowStatusDto();
        statusDto.setStatus(WorkflowStatusEnum.APPROVED.getCode());
        statusDto.setNote("获取财务bp成功");
        //获取云间项目号
        String projectNumber = claim.getColumn46();
        String[] parts = projectNumber.split("-");
        String category = null;
        if (parts.length >= 4) {
            category = parts[3];
        }
        //更新云间业务单据字段
        ExpClaimHeader expClaimHeader = new ExpClaimHeader();
        expClaimHeader.setHeaderId(claim.getHeaderId());
        expClaimHeader.setId(claim.getId());
        JSONObject columnJson = new JSONObject();
        if(claim.getColumnJson() != null)
            columnJson = JSONObject.parseObject(claim.getColumnJson());
        else throw new Exception("columnJson is null");
        columnJson.put("column182", category);
        expClaimHeader.setColumnJson(columnJson.toJSONString());
        yjClaimService.updateClaimHeader(expClaimHeader);

        //
        statusDto.setPathId(mqQueue.getPathId());

        //更新审批流程状态
        statusDto.setEmployeeNumber("KF001");
        ServiceRes workFlowRes = reimbursementService.updateWorkFlowStatus(statusDto);

        return workFlowRes;
    }

}
