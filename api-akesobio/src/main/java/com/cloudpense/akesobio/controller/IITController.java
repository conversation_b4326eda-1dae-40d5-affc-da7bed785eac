package com.cloudpense.akesobio.controller;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudpense.akesobio.domain.IitProject;
import com.cloudpense.akesobio.dto.WorkFlowStatusDto;
import com.cloudpense.akesobio.service.IitProjectService;
import com.cloudpense.akesobio.service.ReimbursementService;
import com.cloudpense.akesobio.service.ReportingSystemApi;
import com.cloudpense.akesobio.util.FkUtil;
import com.cloudpense.akesobio.util.ReflectionUtil;
import com.cloudpense.akesobio.util.http.HttpUtil;
import com.cloudpense.akesobio.vo.ValResultCommonVo;
import com.cloudpense.common.bean.MqQueue;
import com.cloudpense.common.service.UpdateClaimService;
import com.cloudpense.common.service.YjLovService;
import com.cloudpense.common.service.claim.YjClaimService;
import com.cloudpense.common.service.department.YjDepartmentService;
import com.cloudpense.common.vo.ServiceRes;
import com.cloudpense.standard.dao.entity.ExpClaimHeader;
import com.cloudpense.standard.enumeration.claim.WorkflowStatusEnum;
import com.cloudpense.standard.vo.Response;
import com.cloudpense.standard.vo.claim.ExpClaimHeaderCommonVo;
import com.cloudpense.standard.vo.claim.ExpClaimLineCommonVo;
import com.cloudpense.standard.vo.masterdata.DepartmentVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.lang.Thread.sleep;

@Slf4j
@RestController
@RequestMapping("/IITController")
public class IITController {
//
    @Resource
    private IitProjectService iitProjectService;
    @Resource
    private YjClaimService yjClaimService;
    @Resource
    private UpdateClaimService updateClaimService;
    @Resource
    private YjDepartmentService yjDepartmentService;

    @Autowired
    private ReimbursementService reimbursementService;

    @Resource
    private ReportingSystemApi reportingSystemApi;

    @RequestMapping("/KF-SAP-01/IITProjectNumberCreate")
    @ApiOperation("IIT项目号创建")
    ServiceRes IITProjectNumberCreate(@RequestBody String request) throws Exception {
        log.info("KF-SAP-01/IITProjectNumberCreate: IIT项目号创建 request: " + request);
        JSONObject requestJson = JSONObject.parseObject(request);
        MqQueue mqQueue = JSONUtil.toBean(requestJson.getJSONObject("mqQueue").toString(), MqQueue.class);
        WorkFlowStatusDto statusDto = new WorkFlowStatusDto();
        statusDto.setPathId(mqQueue.getPathId());
        statusDto.setEmployeeNumber("KF001");
        try{
            iitProjectService.pushIITProjectNumber(request);
            statusDto.setStatus(WorkflowStatusEnum.APPROVED.getCode());
        }catch(Exception e){
            log.error("KF-SAP-01/IITProjectNumberCreate: IIT项目号创建失败,{}", e.getMessage(), e);
            statusDto.setNote("创建项目号失败");
            statusDto.setStatus(WorkflowStatusEnum.APPROVING.getCode());
            return  reimbursementService.updateWorkFlowStatus(statusDto);
        }
        return reimbursementService.updateWorkFlowStatus(statusDto);
    }

    @PostMapping("/KF-SAP-01/checkDeptArea")
    @ApiOperation("部门片区是否存在校验")
    ValResultCommonVo checkDeptArea(@RequestBody String request) throws Exception {
        log.info("KF-SAP-01/checkDeptArea: 部门片区是否存在校验 request: " + request);
//        JSONObject requestJson = JSONObject.parseObject(request);
        ValResultCommonVo<Integer> result = new ValResultCommonVo<>();
        Map<Integer, String> message = new HashMap<>();
        result.setValue(1);
        result.setResult(1);
        result.setMessage(message);
        ExpClaimHeaderCommonVo claim = FkUtil.parse(request);
        if(claim != null) {
//            claim = JSONUtil.toBean(requestJson.getString("claimVo"), ExpClaimHeaderCommonVo.class);
            String area = claim.getColumn51();
            //不为空 且 正常
            if(StringUtils.hasText(area)) {
                result.setValue(0);
                result.setResult(0);
                return result;
            }
        }
        //为空 或者异常
        return result;
    }

    /**
     * 部门片区是否存在校验
     * @param request
     * @return
     */
    @PostMapping("/isTerminalNode")
    @ApiOperation("校验是否为终端节点")
    ValResultCommonVo isTerminalNode(@RequestBody String request) throws Exception {
        log.info("isTerminalNode: 校验是否为终端节点 request: " + request);
        ValResultCommonVo<Integer> result = new ValResultCommonVo<>();
        Map<Integer, String> message = new HashMap<>();
        result.setValue(1);
        result.setResult(1);
        result.setMessage(message);
        List<String> terminalNode = new ArrayList<>();
        ExpClaimHeaderCommonVo claim = FkUtil.parse(request);
        List<ExpClaimLineCommonVo> claimLineVoList =  claim.getExpClaimLineVoList();
        if(claimLineVoList != null && claimLineVoList.size() > 0) {
            terminalNode = claimLineVoList.stream().map(ExpClaimLineCommonVo::getCostCenterVo).map(DepartmentVo::getCode).collect(Collectors.toList());
        }
        JSONArray terminalNodeJson = terminalNode.stream().collect(Collectors.toCollection(JSONArray::new));
//        String response = HttpUtil.sendPostJson("http://localhost:9966/dev-api/costControl/YXHTUploadController/isTerminalNode", terminalNodeJson.toJSONString());
        String response = reportingSystemApi.isTerminalNode(terminalNodeJson.toJSONString());

        JSONObject responseJson = JSONObject.parseObject(response);
        if(responseJson.getIntValue("code") == 200) {
            result.setValue(0);
            result.setResult(0);
        }
        log.info("isTerminalNode: 校验是否为终端节点 response: " + response);
        log.info("isTerminalNode: 校验是否为终端节点 result: " + result);
        return result;
    }

    @PostMapping("/isTerminalNodeSign")
    @ApiOperation("签收部门为末级部门校验")
    ValResultCommonVo isTerminalNodeSign(@RequestBody String request) throws Exception {
        log.info("verifyEndLevelDepartment: 签收部门为末级部门校验 request: " + request);
        ValResultCommonVo<Integer> result = new ValResultCommonVo<>();
        Map<Integer, String> message = new HashMap<>();
        result.setValue(1);
        result.setResult(1);
        result.setMessage(message);
        ExpClaimHeaderCommonVo claim = FkUtil.parse(request);
        List<String> terminalNode = new ArrayList<>();
        List<ExpClaimLineCommonVo> claimLineVoList =  claim.getExpClaimLineVoList();
        if(claimLineVoList != null && claimLineVoList.size() > 0) {
            terminalNode = claimLineVoList.stream().map(ExpClaimLineCommonVo::getColumn34Obj).map(obj -> {
                JSONObject jsonObject = JSONObject.parseObject(obj.toString());
                return jsonObject.getString("code");
            }).collect(Collectors.toList());
        }
        JSONArray terminalNodeJson = terminalNode.stream().collect(Collectors.toCollection(JSONArray::new));
        String response = reportingSystemApi.isTerminalNode(terminalNodeJson.toJSONString());
//        String response = HttpUtil.sendPostJson("http://localhost:9966/dev-api/costControl/YXHTUploadController/isTerminalNode", terminalNodeJson.toJSONString());
        JSONObject responseJson = JSONObject.parseObject(response);
        if(responseJson.getIntValue("code") == 200) {
            result.setValue(0);
            result.setResult(0);
        }
//        log.info("isTerminalNode: 校验是否为终端节点 response: " + response);
        log.info("isTerminalNode: 校验是否为终端节点 result: " + result);
        return result;
    }



    @PostMapping("/KFQT12")
    @ApiOperation("平台项目编号主数据生成")
    ServiceRes kfqt12(@RequestBody String request) throws Exception {
        log.info("KFQT12: 平台项目编号主数据生成 request: " + request);
        JSONObject requestJson = JSONObject.parseObject(request);
        MqQueue mqQueue = JSONUtil.toBean(requestJson.getJSONObject("mqQueue").toString(), MqQueue.class);
        WorkFlowStatusDto statusDto = new WorkFlowStatusDto();
        statusDto.setPathId(mqQueue.getPathId());
        statusDto.setEmployeeNumber("KF001");
        try {
            iitProjectService.generatePlatformProjectNumber(request);
            statusDto.setStatus(WorkflowStatusEnum.APPROVED.getCode());
        }catch(Exception e){
            statusDto.setNote(e.getMessage());
            statusDto.setStatus(WorkflowStatusEnum.COMMENTING.getCode());
            return  reimbursementService.updateWorkFlowStatus(statusDto);
        }
        return reimbursementService.updateWorkFlowStatus(statusDto);
    }

    /**
     * 费控形成的系列会项目主数据同步至会议系统
     * 【涉及单据类型】：系列会立项申请-营销/系列会立项申请-准入
     * 【触发节点】：审批通过
     * 【逻辑处理】：系列会编号的拼接规则待提供，
     * 1）拼接生成系列会项目号并更新表单
     * 2）更新项目主数据（增加一个字段项目类别：IIT、系列会项目、平台项目）
     * 3）同步会议系统：待提供接口文档
     */
    @PostMapping("/KF-CRM-07")
    @ApiOperation("费控形成的系列会项目主数据同步至会议系统 更新项目预算池（项目号+金额）")
    ServiceRes kfCrm07(@RequestBody String request) throws Exception {
        log.info("KF-CRM-07: 费控形成的系列会项目主数据同步至会议系统 更新项目预算池（项目号+金额）request: " + request);
        JSONObject requestJson = JSONObject.parseObject(request);
        MqQueue mqQueue = JSONUtil.toBean(requestJson.getJSONObject("mqQueue").toString(), MqQueue.class);
        WorkFlowStatusDto statusDto = new WorkFlowStatusDto();
        statusDto.setPathId(mqQueue.getPathId());
        statusDto.setEmployeeNumber("KF001");
        try {
            iitProjectService.generateSeriesMeetingProjectNumber(request);
            statusDto.setStatus(WorkflowStatusEnum.APPROVED.getCode());
        }catch (Exception e){
            log.info("KF-CRM-07: 费控形成的系列会项目主数据同步至会议系统 更新项目预算池（项目号+金额）失败,{}", e.getMessage(), e);
            statusDto.setNote(e.getMessage());
            statusDto.setStatus(WorkflowStatusEnum.APPROVING.getCode());
            return  reimbursementService.updateWorkFlowStatus(statusDto);
        }
        log.info("KF-CRM-07: 费控形成的系列会项目主数据同步至会议系统 更新项目预算池（项目号+金额）成功");
        return  reimbursementService.updateWorkFlowStatus(statusDto);
    }
    /**
     *    628344  K070  携程统一对公结算  Y
     *    628345  K071  梓如统一对公结算  Y
     *    628346  K072  美团统一对公结算  Y
     *    628347  K073  滴滴统一对公结算  Y
     *    628348  K074  京东统一对公结算  Y
     */
    @RequestMapping("/KF-SAP-01/IITProjectNumberFind")
    @ApiOperation("IIT项目号、SAP订单号及其映射关系查询")
    ServiceRes IITProjectNumberFind(@RequestBody String request) throws Exception {
        log.info("KF-SAP-01/IITProjectNumberFind: IIT项目号查询 request: " + request);
        sleep(2000);
        JSONObject requestJson = JSONObject.parseObject(request);
        ExpClaimHeaderCommonVo claim = null;
        if(requestJson.get("claimVo") != null) {
            claim = JSONUtil.toBean(requestJson.getString("claimVo"), ExpClaimHeaderCommonVo.class);
            String companyCode = claim.getBranchVo().getCode();
            String projectNumber = claim.getColumn46();
            LambdaQueryWrapper<IitProject> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(IitProject::getProjectName, projectNumber);
            IitProject iitProject = iitProjectService.getOne(queryWrapper);
            if(iitProject != null) {
                String internalOrder = (String) ReflectionUtil.getFieldValue(iitProject, "internalOrder"+companyCode);
                ExpClaimHeader expClaimHeader = new ExpClaimHeader();
                expClaimHeader.setHeaderId(claim.getHeaderId());
                expClaimHeader.setId(claim.getId());
                JSONObject columnJson = new JSONObject();
                if(claim.getColumnJson() != null)
                    columnJson = JSONObject.parseObject(claim.getColumnJson());
                else throw new Exception("columnJson is null");
                columnJson.put("column102", internalOrder);
                expClaimHeader.setColumnJson(columnJson.toJSONString());
                return yjClaimService.updateClaimHeader(expClaimHeader);
            }
        }
        return ServiceRes.failure("平台项目编号主数据生成失败");
    }


    @RequestMapping("/KF-SAP-01/InvoiceTypeProcessing")
    @ApiOperation("发票类型判断和COLUMN175字段自动填充")
    ServiceRes invoiceTypeProcessing(@RequestBody String request) throws Exception {
        log.info("KF-SAP-01/InvoiceTypeProcessing: 发票类型判断和COLUMN175字段自动填充 request: " + request);
        JSONObject requestJson = JSONObject.parseObject(request);
        MqQueue mqQueue = JSONUtil.toBean(requestJson.getJSONObject("mqQueue").toString(), MqQueue.class);
        WorkFlowStatusDto statusDto = new WorkFlowStatusDto();
        statusDto.setPathId(mqQueue.getPathId());
        statusDto.setEmployeeNumber("KF001");
        try{
            iitProjectService.processInvoiceTypeAndColumn175(request);
            statusDto.setStatus(WorkflowStatusEnum.APPROVED.getCode());
        }catch(Exception e){
            log.error("KF-SAP-01/InvoiceTypeProcessing: 发票类型判断和COLUMN175字段自动填充失败,{}", e.getMessage(), e);
            statusDto.setNote("发票类型处理失败: " + e.getMessage());
            statusDto.setStatus(WorkflowStatusEnum.APPROVING.getCode());
            return reimbursementService.updateWorkFlowStatus(statusDto);
        }
        return reimbursementService.updateWorkFlowStatus(statusDto);
    }

    @RequestMapping("/KF-SAP-01/IITProjectNumberFind/YXDG02")
    @ApiOperation("IIT项目号、SAP订单号及其映射关系查询")
    Response IITProjectNumberFindYXDG02(@RequestBody String request) throws Exception {
        log.info("KF-SAP-01/IITProjectNumberFind/YXDG02: IIT项目号查询 request: " + request );
        log.info("request: " + request);
        JSONObject requestJson = JSONObject.parseObject(request);
        Set<Integer> typeIdSet = new HashSet<>();
        // 将提取的数字添加到Set中
        typeIdSet.add(628344);
        typeIdSet.add(628345);
        typeIdSet.add(628346);
        typeIdSet.add(628347);
        typeIdSet.add(628348);
        JSONArray claim_lines = requestJson.getJSONArray("claim_line");
        //99075 1030  99076 1050
        // 初始化Map
//        Map<String, String> companyMap = new HashMap<>();
//        // 假设第一个数字对是键，第二个数字是值
//        companyMap.put("99075", "1030");
//        companyMap.put("99076", "1050");
        int flag = 0;
        for(Object claim_line : claim_lines){
            JSONObject claim_line_json = (JSONObject) claim_line;
            if(claim_line_json.get("type_id") != null && typeIdSet.contains(claim_line_json.getInteger("type_id")))
                flag++;
        }
        if(flag == 0){ //写头上
            Integer company_id = requestJson.getInteger("branch_id");
            List<DepartmentVo> departmentVos = yjDepartmentService.findDeptsById(company_id);
            String column46 = requestJson.getString("column46");
            String internalOrder = iitProjectService.getInternalOrderNumber(column46,departmentVos.get(0).getDepartmentCode());
            requestJson.put("column102", internalOrder);
        }else {//写行上
            for(Object claim_line : claim_lines){
                JSONObject claim_line_json = (JSONObject) claim_line;
                String projectCode = claim_line_json.getString("column103");
                if(claim_line_json.get("type_id") != null && typeIdSet.contains(claim_line_json.getInteger("type_id"))&& StringUtils.hasText(projectCode)){
                    String companyCode = claim_line_json.getString("column102");
//                    if(StringUtils.hasText(companyCode)) companyCode = companyCode.substring(0, 4);
                    claim_line_json.put("column135",iitProjectService.getInternalOrderNumber(projectCode,companyCode));
                }
            }
            requestJson.put("claim_line",claim_lines);
        }
        Response response = updateClaimService.updateClaimLines(JSONObject.parseObject(requestJson.toJSONString()));
        log.info("response: " + response.toString());
        return response;
    }
    /**
     * 【涉及单据类型】：平台项目立项申请
     * 【触发节点】：审批通过
     * 【逻辑处理】：平台项目编号的拼接规则待提供
     * 1）拼接生成平台项目号并更新表单
     * 2）更新项目主数据（项目类别：平台项目）
     */
    @PostMapping("/resultVerification")
    @ApiOperation("虚拟岗：编号生成结果验证")
    ServiceRes resultVerification(@RequestBody String request) throws Exception {
        log.info("resultVerification: 编号生成结果验证 request: " + request);
        sleep(5000);
        ExpClaimHeaderCommonVo claim = null;
        JSONObject requestJson = JSONObject.parseObject(request);
        MqQueue mqQueue = JSONUtil.toBean(requestJson.getJSONObject("mqQueue").toString(), MqQueue.class);
        WorkFlowStatusDto statusDto = new WorkFlowStatusDto();
        statusDto.setStatus(WorkflowStatusEnum.REJECTED.getCode());
        statusDto.setNote("编号生成失败");
        if(requestJson.get("claimVo") != null) {
            claim = JSONUtil.toBean(requestJson.getString("claimVo"), ExpClaimHeaderCommonVo.class);
            String companyCode = claim.getBranchVo().getCode();
            String projectNumber = claim.getColumn46();

            if(!StringUtils.hasText(projectNumber))
                projectNumber = claim.getColumn41();

            LambdaQueryWrapper<IitProject> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(IitProject::getProjectName, projectNumber);
            IitProject iitProject = iitProjectService.getOne(queryWrapper);
            if(iitProject != null) {
                String internalOrder = (String) ReflectionUtil.getFieldValue(iitProject, "internalOrder"+companyCode);
                ExpClaimHeader expClaimHeader = new ExpClaimHeader();
                expClaimHeader.setHeaderId(claim.getHeaderId());
                expClaimHeader.setId(claim.getId());
                JSONObject columnJson = new JSONObject();
                if(claim.getColumnJson() != null)
                    columnJson = JSONObject.parseObject(claim.getColumnJson());
                else throw new Exception("columnJson is null");
                columnJson.put("column102", internalOrder);
                expClaimHeader.setColumnJson(columnJson.toJSONString());
                yjClaimService.updateClaimHeader(expClaimHeader);
                if(StringUtils.hasText(internalOrder)){
                    statusDto.setStatus(WorkflowStatusEnum.APPROVED.getCode());
                    statusDto.setNote("编号生成成功");
                }
            }
        }
//        MqQueue mqQueue = JSONUtil.toBean(requestJson.getJSONObject("mqQueue").toString(), MqQueue.class);
//        WorkFlowStatusDto statusDto = new WorkFlowStatusDto();
//        if(StringUtils.hasText(claim.getColumnJson())){
//            JSONObject columnJson = JSONObject.parseObject(claim.getColumnJson());
//            if(StringUtils.hasText(columnJson.getString("column102"))){
//                statusDto.setStatus(WorkflowStatusEnum.APPROVED.getCode());
//                statusDto.setNote("编号生成成功");
//            }else {
//                statusDto.setStatus(WorkflowStatusEnum.REJECTED.getCode());
//                statusDto.setNote("编号生成失败");
//            }
//        }else {
//            statusDto.setStatus(WorkflowStatusEnum.REJECTED.getCode());
//        }
        statusDto.setPathId(mqQueue.getPathId());
        statusDto.setEmployeeNumber("KF001");
        ServiceRes workFlowRes = reimbursementService.updateWorkFlowStatus(statusDto);
        return workFlowRes;
    }

}
