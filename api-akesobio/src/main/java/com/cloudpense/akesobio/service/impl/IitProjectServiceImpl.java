package com.cloudpense.akesobio.service.impl;

import java.text.SimpleDateFormat;
import java.util.*;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudpense.akesobio.domain.IitProject;
import com.cloudpense.akesobio.dto.LinkageLovs;
import com.cloudpense.akesobio.dto.LovsDto;
import com.cloudpense.akesobio.dto.ReceiptQueryRequest;
import com.cloudpense.akesobio.dto.ReceiptQueryResponse;
import com.cloudpense.akesobio.mapper.IitProjectMapper;

import com.cloudpense.akesobio.service.*;


import com.cloudpense.akesobio.util.ReflectionUtil;
import com.cloudpense.akesobio.util.SAPApiUtils;

import com.cloudpense.common.service.claim.YjClaimService;
import com.cloudpense.common.service.delivery.YjDeliveryService;

import com.cloudpense.common.vo.ServiceRes;
import com.cloudpense.standard.dao.entity.ExpClaimHeader;
import com.cloudpense.standard.vo.claim.ExpClaimHeaderCommonVo;
import com.cloudpense.standard.vo.claim.ExpClaimLineCommonVo;
import com.cloudpense.standard.vo.claim.ExpReceiptVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;


/**
 * 费控系统项目主数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-15
 */
@Service
@Slf4j
public class IitProjectServiceImpl extends ServiceImpl<IitProjectMapper, IitProject> implements IitProjectService
{
    @Resource
    private IitProjectMapper iitProjectMapper;

    @Resource
    private YjClaimService yjClaimService;

    @Resource
    private YjDeliveryService yjDeliveryService;

    @Resource
    private MeetingApiService meetingApiService;

    @Resource
    private CloudpenseApiService cloudpenseApiService;

    @Resource
    private SapApiService sapApiService;

    @Resource
    private ReceiptService receiptService;


    public  String generateSerialNumber(String prefix,int length, int number) {
        return prefix + String.format("%0" + length + "d", number);
    }


    @Override
    public String getInternalOrderNumber(String projectNumber, String companyCode) {
        if(!StringUtils.hasText(projectNumber) || !StringUtils.hasText(companyCode)){
            return "项目号、公司代码不能为空";
        }
        LambdaQueryWrapper<IitProject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IitProject::getProjectCode, projectNumber);
        IitProject iitProject = getOne(queryWrapper);
        String internalOrder = null;
        if(iitProject != null) {
             internalOrder = (String) ReflectionUtil.getFieldValue(iitProject, "internalOrder" + companyCode);
        }
        if(StringUtils.hasText(internalOrder)) return internalOrder;
        else return "内部订单号未同步或未生成";
    }

    @Override
    public IitProject generatePlatformProjectNumber(String param) throws Exception {
        JSONObject requestJson = JSONObject.parseObject(param);
        ExpClaimHeaderCommonVo claim = null;
        if(requestJson.get("claimVo") != null) {
            claim = JSONUtil.toBean(requestJson.getString("claimVo"), ExpClaimHeaderCommonVo.class);
        }else throw new Exception("claimVo is null");
        String generationPipelines = claim.getColumn41Obj().getString("value_meaning");
        String jobNum = claim.getChargeUserVo().getCode();
        String project_department = claim.getSubmitDeptVo().getDepartmentCode();
        IitProject oldIitProject = isGeneratedProjectNumber(claim.getCode());
        IitProject resultIitProject = null;
        if(oldIitProject!=null){
            //已经生成过项目号，不再生成
            resultIitProject = oldIitProject;
        }
        else {
            // * 1）拼接生成系列会项目号
            //系列会项目号规则：AK104/AK112+PT+年份+001
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
            String prefix = generationPipelines+"PT"+sdf.format(date);
            //上一个项目号
            IitProject previousIitProject = iitProjectMapper.getPlatformProjectNumber(prefix);
            int number = 1;
            if(previousIitProject!=null){
                //获取后三位数字
                String lastNumber = previousIitProject.getProjectName().substring(previousIitProject.getProjectName().length()-3,previousIitProject.getProjectName().length());
                number = Integer.parseInt(lastNumber)+1;
            }
            //根据项目号前缀补充流水号。
            prefix = generateSerialNumber(prefix,3,number);
            resultIitProject = new IitProject();
            resultIitProject.setCode(claim.getCode());
            resultIitProject.setDepartmentCode(claim.getSubmitDeptVo().getDepartmentCode());
            resultIitProject.setDepartmentDesc(claim.getSubmitDeptVo().getDepartmentName());
            resultIitProject.setProjectCode(prefix);
            resultIitProject.setProjectName(prefix);
            resultIitProject.setProjectManager(claim.getChargeUserVo().getCode());
            resultIitProject.setType("03");
            if(claim.getTotalAmount()!=null)
                resultIitProject.setTotalAmount(claim.getTotalAmount().toString());
            //入库
            iitProjectMapper.insert(resultIitProject);
        }

        // * 2）更新项目主数据
//        CloudpenseApiUtils.syncPlatformProjectNumber(resultIitProject.getProjectCode(), resultIitProject.getProjectName(), jobNum,
//                project_department,claim.getTotalAmount().toString());
        //更新云简表单
//        ProjectDTO projectDTO = new ProjectDTO();
//        List<Object> projectCodes = new ArrayList<>();
//        projectCodes.add(resultIitProject.getProjectCode());
//        projectDTO.setProjectCodes(projectCodes);
//        List<Object> iitProjects = yjProjectService.findProjectByPage(projectDTO);
//        cn.hutool.json.JSONObject iitProjectJson = (cn.hutool.json.JSONObject) iitProjects.get(0);
         //更新基础主数据
        List<LovsDto> lovsDtos = new ArrayList<>();
        LovsDto lovsDto = new LovsDto();
        lovsDto.setCode("XMHZSJ");
        lovsDto.setValue(resultIitProject.getProjectName());
        lovsDto.setType(resultIitProject.getProjectCode());
        lovsDto.setEnabledFlag("Y");
        lovsDto.setColumn3("03");
        if(claim.getTotalAmount()!=null)
            lovsDto.setColumn4(claim.getTotalAmount().toString());
        lovsDto.setColumn5(jobNum);
        lovsDto.setColumn6(project_department);
        lovsDtos.add(lovsDto);
        cloudpenseApiService.syncLovs(lovsDtos);

        //值列表联动
//        LinkageLovs linkageLovs1 = new LinkageLovs();
//        linkageLovs1.setParentValue(generationPipelines);
//        linkageLovs1.setChildValue(resultIitProject.getProjectCode());
//        linkageLovs1.setEnabledFlag("N");
//        String linkageLovsResponse = cloudpenseApiService.linkageLov(linkageLovs1,"");
//        log.info("云简值列表联动 linkageLovsResponse:{}",linkageLovsResponse);
        //更新表单
        ExpClaimHeader expClaimHeader = new ExpClaimHeader();
        expClaimHeader.setColumn45(resultIitProject.getProjectName());
        expClaimHeader.setHeaderId(claim.getHeaderId());
        expClaimHeader.setId(claim.getId());
        ServiceRes serviceRes =  yjClaimService.updateClaimHeader(expClaimHeader);
        log.info("YJ表单更新 serviceRes:{}",serviceRes);
        return resultIitProject;
    }

    @Override
    public IitProject generateSeriesMeetingProjectNumber(String param) throws Exception {

        JSONObject requestJson = JSONObject.parseObject(param);
        ExpClaimHeaderCommonVo claim = null;
        if(requestJson.get("claimVo") != null) {
            claim = JSONUtil.toBean(requestJson.getString("claimVo"), ExpClaimHeaderCommonVo.class);
        }
        String generationPipelines = claim.getColumn41Obj().getString("value_meaning");
        String jobNum = claim.getChargeUserVo().getCode();
        String project_department = claim.getSubmitDeptVo().getDepartmentCode();

        IitProject oldIitProject = isGeneratedProjectNumber(claim.getCode());
        IitProject resltIitProject = new IitProject();

        if(oldIitProject!=null){
            //已经生成过项目号，不再生成
             resltIitProject = oldIitProject;
        }else {
            //生成项目号
            // * 1）拼接生成系列会项目号并更新表单
            //系列会项目号规则：AK104/AK112+XL+年份+001
            //前缀
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
            String prefix = generationPipelines+"XL"+sdf.format(date);
            //上一个项目号
            IitProject  previousIitProject = iitProjectMapper.getSeriesMeetingProjectNumber(prefix);
            int number = 1;
            if(previousIitProject!=null){
                //获取后三位数字
                String lastNumber = previousIitProject.getProjectName().substring(previousIitProject.getProjectName().length()-3,previousIitProject.getProjectName().length());
                number = Integer.parseInt(lastNumber)+1;
            }
            //根据项目号前缀补充流水号。
            prefix = generateSerialNumber(prefix,3,number);

            resltIitProject = new IitProject();
            resltIitProject.setCode(claim.getCode());
            resltIitProject.setDepartmentCode(claim.getSubmitDeptVo().getDepartmentCode());
            resltIitProject.setDepartmentDesc(claim.getSubmitDeptVo().getDepartmentName());
            resltIitProject.setProjectCode(prefix);
            resltIitProject.setProjectName(prefix);
            resltIitProject.setType("02");
            if(claim.getTotalAmount()!=null)
                resltIitProject.setTotalAmount(claim.getTotalAmount().toString());
            //入库
            iitProjectMapper.insert(resltIitProject);
        }
//      * 2）更新基础主数据
        List<LovsDto> lovsDtos = new ArrayList<>();
        LovsDto lovsDto = new LovsDto();
        lovsDto.setCode("XMHZSJ");
        lovsDto.setValue(resltIitProject.getProjectName());
        lovsDto.setType(resltIitProject.getProjectCode());
        lovsDto.setEnabledFlag("Y");
        lovsDto.setColumn3("02");
        if(claim.getTotalAmount()!=null)
            lovsDto.setColumn4(claim.getTotalAmount().toString());
        lovsDto.setColumn5(jobNum);
        lovsDto.setColumn6(project_department);
        lovsDtos.add(lovsDto);

        cloudpenseApiService.syncLovs(lovsDtos);
//        LinkageLovs linkageLovs1 = new LinkageLovs();
//        linkageLovs1.setParentValue(generationPipelines);
//        linkageLovs1.setChildValue(resltIitProject.getProjectCode());
//        linkageLovs1.setEnabledFlag("N");
//        cloudpenseApiService.linkageLov(linkageLovs1,"");


        //更新云简表单
        ExpClaimHeader expClaimHeader = new ExpClaimHeader();
        expClaimHeader.setColumn45(resltIitProject.getProjectName());
        expClaimHeader.setHeaderId(claim.getHeaderId());
        expClaimHeader.setId(claim.getId());
        ServiceRes serviceRes =  yjClaimService.updateClaimHeader(expClaimHeader);
        log.info("YJ表单更新 serviceRes:{}",serviceRes);

        //推送预算池
//        BudgetSyncQueryParams params = new BudgetSyncQueryParams();
//        params.setBudgetCode("系列会项目预算");
//        if(claim.getTotalAmount()!=null) params.setBudgetAmount(claim.getTotalAmount().toString());
//        else params.setBudgetAmount("0");
//        params.setProjectCode(resltIitProject.getProjectCode());
//        CloudpenseApiUtils.syncBudget(params);

        //* 3）同步会议系统：
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name",resltIitProject.getProjectName());
        jsonObject.put("code",resltIitProject.getProjectCode());
        if(claim.getTotalAmount()!= null)
            jsonObject.put("projectBudget",claim.getTotalAmount().toString());

        jsonArray.add(jsonObject);
        meetingApiService.updateSeriesProjectNumber(jsonArray);

        return resltIitProject;
    }

    //判断当前单据是否已经生成过项目号
    public IitProject isGeneratedProjectNumber(String claimCode) {
        LambdaQueryWrapper<IitProject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IitProject::getCode,claimCode);
        IitProject iitProject = iitProjectMapper.selectOne(queryWrapper);
        return iitProject;
    }

    @Override
    public IitProject pushIITProjectNumber(String request) throws Exception {
        JSONObject requestJson = JSONObject.parseObject(request);
        ExpClaimHeaderCommonVo claim = null;
        if(requestJson.get("claimVo") != null) {
            claim = JSONUtil.toBean(requestJson.getString("claimVo"), ExpClaimHeaderCommonVo.class);
        }
        String generationPipelines = claim.getColumn41Obj().getString("value_meaning");
        String jobNum = claim.getChargeUserVo().getCode();
        String project_department = claim.getSubmitDeptVo().getDepartmentCode();
        String iitProjectType = claim.getColumn15Obj().getString("value_meaning");
        if(shouldSkipForManualAB(claim)){
               return null;//AB类项目编号需业务指定：因AB类项目的立项流程是先由业务
            // 编制项目号后再跟高管报备，所以项目编号要早于系统立项流程，因此在系统发起立项流程时需指定为之前跟高管报备的项目号，该项目号需要在立项提交时由申请人填写在表单的项目号中。
        }
        //判断是否已经生成过项目号
        IitProject oldIitProject =  isGeneratedProjectNumber(claim.getCode());
        IitProject resultIitProject = null;
        if(oldIitProject!=null){
            resultIitProject = oldIitProject;
        }else {
            String area = claim.getColumn51();
            if(StringUtils.hasText(area)){
                //生成项目号
                resultIitProject = generateIITProjectNumber(generationPipelines, iitProjectType, jobNum,area);
                resultIitProject.setDepartmentCode(project_department);
                resultIitProject.setCode(claim.getCode());
                resultIitProject.setType("01");
                resultIitProject.setArea(claim.getColumn51());
                resultIitProject.setProjectManager(claim.getChargeUserVo().getCode());
                if(claim.getTotalAmount()!=null)
                    resultIitProject.setTotalAmount(claim.getTotalAmount().toString());
                if(!resultIitProject.getProjectCode().contains("error"))
                    iitProjectMapper.insert(resultIitProject);
            }else {
              throw new Exception("区域不能为空");
            }
        }
        /**
         * 同步到云简费控系统
         * 1、更新云简单基础主数据
         * 2、更新云简表单
          */
//        String syncResponse = CloudpenseApiUtils.syncProjects(resultIitProject.getProjectCode(), resultIitProject.getProjectName(),
//                jobNum, project_department,"01",claim.getTotalAmount(),resultIitProject);
//        log.info("云简项目同步 syncResponse:{}",syncResponse);
        //更新云简单基础主数据
//        ProjectDTO projectDTO = new ProjectDTO();
//        List<Object> projectCodes = new ArrayList<>();
//        projectCodes.add(resultIitProject.getProjectCode());
//        projectDTO.setProjectCodes(projectCodes);
//
//        List<Object> iitProjects = yjProjectService.findProjectByPage(projectDTO);
//        cn.hutool.json.JSONObject iitProjectJson = (cn.hutool.json.JSONObject) iitProjects.get(0);

        //更新基础主数据
        List<LovsDto> lovsDtos = new ArrayList<>();
        LovsDto lovsDto = new LovsDto();
        lovsDto.setCode("Project_number");
        lovsDto.setValue(resultIitProject.getProjectName());
        lovsDto.setType(resultIitProject.getProjectCode());
        lovsDto.setEnabledFlag("Y");
        lovsDto.setColumn3("01");
        if(claim.getTotalAmount()!=null)
            lovsDto.setColumn4(claim.getTotalAmount().toString());
        lovsDto.setColumn5(jobNum);
        lovsDto.setColumn6(project_department);

        lovsDto.setColumn1(resultIitProject.getInternalOrder1000());
        lovsDto.setColumn2(resultIitProject.getInternalOrder1050());
        lovsDto.setColumn7(resultIitProject.getInternalOrder1030());
        lovsDto.setColumn8(resultIitProject.getInternalOrder1020());
        lovsDto.setColumn9(resultIitProject.getInternalOrder1070());
        lovsDto.setColumn10(resultIitProject.getInternalOrder1060());
        lovsDto.setColumn11(resultIitProject.getInternalOrder1080());
        lovsDto.setColumn12(resultIitProject.getInternalOrder1230());
        lovsDto.setColumn13(resultIitProject.getInternalOrder1250());
        lovsDto.setColumn14(resultIitProject.getInternalOrder1200());
        lovsDto.setColumn15(resultIitProject.getInternalOrder1100());
        lovsDto.setColumn16(resultIitProject.getInternalOrder2100());
        lovsDtos.add(lovsDto);
        cloudpenseApiService.syncLovs(lovsDtos);

        //更新云简表单
        ExpClaimHeader expClaimHeader = new ExpClaimHeader();
        expClaimHeader.setColumn46(resultIitProject.getProjectCode());
//        expClaimHeader.setColumn24(iitProjectJson.getStr("id"));
//        expClaimHeader.setProjectName(iitProjectJson.getStr("id"));
        expClaimHeader.setHeaderId(claim.getHeaderId());
        expClaimHeader.setId(claim.getId());
        ServiceRes serviceRes =  yjClaimService.updateClaimHeader(expClaimHeader);
        log.info("YJ表单更新 serviceRes:{}",serviceRes);
        //值列表联动
//        List<LinkageLovs> linkageLovsList = new ArrayList<>();
        LinkageLovs linkageLovs1 = new LinkageLovs();
//        String[] split = resultIitProject.getProjectName().split("-");
//        if(split.length > 1){
            linkageLovs1.setParentValue(generationPipelines);
            linkageLovs1.setChildValue(resultIitProject.getProjectCode());
            linkageLovs1.setEnabledFlag("N");
            String linkageLovsResponse = cloudpenseApiService.linkageLov(linkageLovs1,"BT003");
            log.info("云简值列表联动 linkageLovsResponse:{}",linkageLovsResponse);
//        }

        // 推送sap
        if(StringUtils.hasText(resultIitProject.getArea())){
            String sapResponse = sapApiService.ZFK_FI_001(resultIitProject,jobNum);
            log.info("SAP项目号推送 sapResponse:{}",sapResponse);
            // 推送会议系统
            String meetingResponse = meetingApiService.updateProjectNumber(resultIitProject,claim.getBranchVo().getCode());
            log.info("会议系统项目号推送 meetingResponse:{}",meetingResponse);
        }
        return resultIitProject;
    }

    @Override
    public void processInvoiceTypeAndColumn175(String request) throws Exception {
        log.info("开始处理发票类型判断和COLUMN175字段自动填充");

        JSONObject requestJson = JSONObject.parseObject(request);
        ExpClaimHeaderCommonVo claim = null;
        if(requestJson.get("claimVo") != null) {
            claim = JSONUtil.toBean(requestJson.getString("claimVo"), ExpClaimHeaderCommonVo.class);
        } else {
            throw new Exception("claimVo is null");
        }

        // 需要强制收单的单据类型列表
        Set<String> forceDeliveryDocTypes = new HashSet<>(Arrays.asList(
            "YXHY02", "ZRHY02", "YXDG01", "YXDG02"
        ));

        // 检查单据类型，如果是需要强制收单的类型，直接设置为Y
        String documentCode = claim.getCode();
        log.info("单据号: {}, 检查是否为强制收单类型", documentCode);
        
        for (String docType : forceDeliveryDocTypes) {
            if (documentCode != null && documentCode.contains(docType)) {
                log.info("单据类型包含 {}, 强制设置COLUMN175为Y", docType);
                updateClaimColumn175(claim, "Y");
                log.info("发票类型判断和COLUMN175字段自动填充处理完成");
                return;
            }
        }

        // 对于非强制收单类型，按照原有发票类型判断逻辑处理
        log.info("单据类型不在强制收单列表中，按照发票类型判断逻辑处理");
        
//        // 发票类型白名单
//        Set<String> invoiceTypeWhitelist = new HashSet<>(Arrays.asList(
//            "01", "02", "03", "04", "11", "15", "18", "19", "24", "25",
//            "50", "51", "60", "59", "61", "62", "63", "64", "65", "66",
//            "70", "72", "74", "99"
//        ));
//
        // 检查费用明细行中的发票类型
        boolean hasElectronicInvoice = false;
        boolean hasNonElectronicInvoice = false;
//
//        List<ExpClaimLineCommonVo> claimLines = claim.getExpClaimLineVoList();
//        if (claimLines != null && !claimLines.isEmpty()) {
//            for (ExpClaimLineCommonVo line : claimLines) {
//                List<ExpReceiptVo> receipts = line.getReceipts();
//                if (receipts != null && !receipts.isEmpty()) {
//                    for (ExpReceiptVo receipt : receipts) {
//                        String invoiceType = receipt.getInvoiceType();
//                        String invoiceTypeMeaning = receipt.getInvoiceTypeMeaning();
//
//                        log.info("处理发票类型: {}, 发票类型含义: {}", invoiceType, invoiceTypeMeaning);
//
//                        // 判断是否为纯电子发票（不在白名单中）
//                        if (StringUtils.hasText(invoiceType) && !invoiceTypeWhitelist.contains(invoiceType)) {
//                            hasElectronicInvoice = true;
//                            log.info("发现纯电子发票，发票类型: {}", invoiceType);
//                        }
//
//                        // 根据票据类型含义判断是否包含"电子"关键字
//                        if (StringUtils.hasText(invoiceTypeMeaning) && invoiceTypeMeaning.contains("电子")) {
//                            hasElectronicInvoice = true;
//                            log.info("发现电子发票，票据类型含义: {}", invoiceTypeMeaning);
//                        } else if (StringUtils.hasText(invoiceTypeMeaning)) {
//                            hasNonElectronicInvoice = true;
//                            log.info("发现非电子发票，票据类型含义: {}", invoiceTypeMeaning);
//                        }
//                    }
//                }
//            }
//        }

        ReceiptQueryRequest receiptQueryRequest = new ReceiptQueryRequest();
        receiptQueryRequest.setBizId(UUID.randomUUID().toString());
        receiptQueryRequest.setTimestamp(System.currentTimeMillis());
        receiptQueryRequest.setDocument_num(claim.getCode());

        //查询单据发票  排除会议核报YXHY02，会议核报准入ZRHY02，对公预付YXDG01，对公应付YXDG02
        //，4个表单，其他核报单的离职岗节点加 校验发票类型是电子发票，不论在职/离职 都自动通过，如果发票类型是纸单 就保留节点卡住，需要业务提交纸质单据，财务收单后才能自动审批通过
        ReceiptQueryResponse receiptQueryResponse = receiptService.queryInvoiceCount(receiptQueryRequest);
        if (receiptQueryResponse.getInvoice_number().equals(receiptQueryResponse.getE_invoice_number())){
            hasElectronicInvoice = true;
            hasNonElectronicInvoice = false;
        }
        // 确定COLUMN175字段的值（是否投递纸单）
        String column175Value;
        if (hasElectronicInvoice && !hasNonElectronicInvoice) {
            // 全部为电子发票
            column175Value = "N";
            log.info("全部为电子发票，设置COLUMN175为: {}", column175Value);
        } else if (hasNonElectronicInvoice) {
            // 存在非电子发票
            column175Value = "Y";
            log.info("存在非电子发票，设置COLUMN175为: {}", column175Value);
        } else {
            // 没有发票信息，默认为"是"
            column175Value = "Y";
            log.info("没有发票信息，默认设置COLUMN175为: {}", column175Value);
        }

        // 更新单据头的COLUMN175字段
        updateClaimColumn175(claim, column175Value);

        log.info("发票类型判断和COLUMN175字段自动填充处理完成");
    }

    /**
     * 更新单据头的COLUMN175字段
     */
    private void updateClaimColumn175(ExpClaimHeaderCommonVo claim, String column175Value) throws Exception {
        try {
            JSONObject columnJson = new JSONObject();
            if (StringUtils.hasText(claim.getColumnJson())) {
                columnJson = JSONObject.parseObject(claim.getColumnJson());
            }

            // 设置COLUMN175字段
            columnJson.put("column175", column175Value);

            log.info("更新单据头COLUMN175字段，单据号: {}, COLUMN175值: {}", claim.getDocumentNum(), column175Value);

            // 使用ExpClaimHeader对象更新单据
            ExpClaimHeader expClaimHeader = new ExpClaimHeader();
            expClaimHeader.setHeaderId(claim.getHeaderId());
            expClaimHeader.setId(claim.getId());
            expClaimHeader.setColumnJson(columnJson.toJSONString());

            ServiceRes serviceRes = yjClaimService.updateClaimHeader(expClaimHeader);

            if (serviceRes != null && !"200000".equals(serviceRes.getCode())) {
                throw new Exception("更新单据头COLUMN175字段失败: " + serviceRes.getMsg());
            }

            log.info("成功更新单据头COLUMN175字段");

            // 如果COLUMN175设置为N，调用纸单手动创建接口
            if ("N".equals(column175Value)) {
                log.info("COLUMN175设置为Y，开始创建纸单，单据号: {}", claim.getDocumentNum());
                try {
                    if (yjDeliveryService != null) {
                        ServiceRes deliveryRes = yjDeliveryService.createManualDelivery(claim.getDocumentNum());
                        if (ServiceRes.isSuccess(deliveryRes)) {
                            log.info("纸单创建成功，单据号: {}", claim.getDocumentNum());
                        } else {
                            log.warn("纸单创建失败，但不影响主流程，单据号: {}, 错误信息: {}", 
                                    claim.getDocumentNum(), deliveryRes.getMsg());
                        }
                    } else {
                        log.warn("YjDeliveryService未注入，跳过纸单创建，单据号: {}", claim.getDocumentNum());
                    }
                } catch (Exception deliveryEx) {
                    log.warn("纸单创建异常，但不影响主流程，单据号: {}, 异常信息: {}", 
                            claim.getDocumentNum(), deliveryEx.getMessage(), deliveryEx);
                }
            }

        } catch (Exception e) {
            log.error("更新单据头COLUMN175字段时发生异常: {}", e.getMessage(), e);
            throw new Exception("更新单据头COLUMN175字段失败: " + e.getMessage());
        }
    }

    @Override
    public IitProject generateIITProjectNumber(String generationPipelines,String iitProjectType,String jobNum,String area) {
        IitProject resultIitProject = new IitProject();

        String iitProjectNumber = generationPipelines+"-IIT";

        int length = 0;
        int number = 1;
        if("A类".equals(iitProjectType)||"B类".equals(iitProjectType)){
            length = 3;
        }
        else if("C类".equals(iitProjectType)){
            iitProjectNumber += "-C";
            length = 4;

            iitProjectNumber += "-"+getAreaCode(area).replace("0","");

        }
        else if("非临床研究".equals(iitProjectType)){
            iitProjectNumber += "-PC";
            length = 4;
            iitProjectNumber += "-"+getAreaCode(area);
//            // 根据jobnum查到用户部门，判断属于东南西北哪个区。然后加上区号。东 E，南 W，西 S，北 N。
//            if( "YX-03".equals(area)){//YX-03东中国区
//                iitProjectNumber += "-E0";
//            }else if("YX-06".equals(area)){//YX-06西中国区
//                iitProjectNumber += "-W0";
//            } else if("YX-08".equals(area)){//YX-08医学中心
//                iitProjectNumber += "-M0";
//            }
//            //YX-11北中国区本部
//            else if("YX-11".equals(area)){
//                iitProjectNumber += "-N0";
//            }
//            //YX-14中中国区本部
//            else if("YX-14".equals(area)){
//                iitProjectNumber += "-C0";
//            }
//            //YX-12南中国一区本部
//            else if("YX-12".equals(area)){
//                iitProjectNumber += "-S1";
//            }
//            //YX-13南中国二区本部
//            else if("YX-13".equals(area)){
//                iitProjectNumber += "-S2";
//            }
//            else{
//                throw  new IllegalArgumentException("area is not valid");
//            }
        }
        else {
            throw  new IllegalArgumentException("area is not valid");
        }
        iitProjectNumber = iitProjectNumber +"-";

        IitProject iitProject = null;
        if("A类".equals(iitProjectType)||"B类".equals(iitProjectType)){
            iitProject = iitProjectMapper.getIitProjectByProjectNameAB(iitProjectNumber);
        }else if("C类".equals(iitProjectType)||"非临床研究".equals(iitProjectType)){
            iitProject = iitProjectMapper.getIitProjectByProjectNameC(iitProjectNumber);
        }else {
            throw  new IllegalArgumentException("iitProjectType is not valid");
        }
        if(iitProject!=null){
            String lastNumber = iitProject.getProjectName().split("-")[iitProject.getProjectName().split("-").length-1];
            number = Integer.parseInt(lastNumber)+1;
        }
        //根据项目号前缀补充流水号。
        iitProjectNumber = generateSerialNumber(iitProjectNumber,length,number);
        resultIitProject.setProjectName(iitProjectNumber);
        resultIitProject.setProjectCode(iitProjectNumber);
        //根据项目号生成sap订单号。
        resultIitProject = generateSapOrderNumber(iitProjectNumber,iitProjectType,area,resultIitProject);
        return resultIitProject;
    }


    private String getAreaCode(String area){
        String areaCode = "";
        if( "YX-03".equals(area)){//YX-03东中国区
            areaCode = "E0";
        }else if("YX-06".equals(area)){//YX-06西中国区
            areaCode = "W0";
        } else if("YX-08".equals(area)){//YX-08医学中心
            areaCode = "M0";
        }
        //YX-11北中国区本部
        else if("YX-11".equals(area)){
            areaCode = "N0";
        }
        //YX-14中中国区本部
        else if("YX-14".equals(area)){
            areaCode = "C0";
        }
        //YX-12南中国一区本部
        else if("YX-12".equals(area)){
            areaCode = "S1";
        }
        //YX-13南中国二区本部
        else if("YX-13".equals(area)){
            areaCode = "S2";
        }
        else{
            throw  new IllegalArgumentException("area is not valid");
        }
        return areaCode;
    }
    @Override
    public IitProject generateSapOrderNumber(String iitProjectNumber,String iitProjectType,String area,IitProject tempIitProject) {
        //后缀
        String suffix = "";


        if("C类".equals(iitProjectType)){

            String[] split = iitProjectNumber.split("-");
            //流水号
            String serialNumber = split[split.length-1];
            //产品管线
            String generationPipelines = split[0];
            //常量
            String constant = "C";
            //地区代码
            String areaCode = getAreaCode(area);
            suffix = generationPipelines.replace("AK","") +constant+areaCode+serialNumber;
//            String[] split = iitProjectNumber.split("-");
//            suffix =  iitProjectNumber.substring(0, iitProjectNumber.length() - 4).replace("-IIT","")
//                    .replace("-","")
//                    .replace("AK","");
//            //YX-03东中国区
//            if( area.equals("YX-03")){
//                suffix += "0";
//            }
//            //YX-06西中国区
//            else if( area.contains("YX-06")){
//                suffix += "0";
//            }
//            //YX-08医学中心
//            else if("YX-08".equals(area)){
//                suffix += "0";
//            }
//            //YX-14中中国区本部
//            else if("YX-14".equals(area)){
//                suffix += "0";
//            }
//            //YX-11北中国区本部
//            else if("YX-11".equals(area)){
//                suffix += "0";
//            }
//            //YX-12南中国一区本部
//            else if("YX-12".equals(area)){
//                suffix += "";
//            }
//            //YX-13南中国二区本部
//            else if("YX-13".equals(area)){
//                suffix += "";
//            }
//            else {
//                new IllegalArgumentException("department is not valid");
//            }
//            suffix  += split[split.length-1];
        }
        else if("非临床研究".equals(iitProjectType)){
            String[] split = iitProjectNumber.split("-");
            //流水号
            String serialNumber = split[split.length-1];
            //产品管线
            String generationPipelines = split[0];
            //常量
            String constant = "P";
            //地区代码
            String areaCode = getAreaCode(area);
            suffix = generationPipelines.replace("AK","") +constant+areaCode+serialNumber;
        }
        else if("A类".equals(iitProjectType)||"B类".equals(iitProjectType)){
            String[] split = iitProjectNumber.split("-");
            int serialNumber = Integer.parseInt(split[split.length-1]);
            if(serialNumber>100){
                throw new IllegalArgumentException("serialNumber is not valid");
            }
            suffix = iitProjectNumber.replace("-0","");
            suffix = suffix.replace("-","");
        }else {
            throw new IllegalArgumentException("iitProjectType is not valid");
        }

        //公司代码映射
//        Map<String, String> map = new TreeMap<>();
//        map.put("1000", "10");
//        map.put("1020", "20");
//        map.put("1030", "30");
//        map.put("1050", "50");
//        map.put("1070", "70");
//        map.put("1060", "60");
//        map.put("1080", "80");
//        map.put("1230", "23");
//        map.put("1250", "25");
//        map.put("1200", "12");
//        map.put("1100", "11");
//        map.put("2100", "21");

        tempIitProject.setInternalOrder1000("10"+suffix);
        tempIitProject.setInternalOrder1020("20"+suffix);
        tempIitProject.setInternalOrder1030("30"+suffix);
        tempIitProject.setInternalOrder1050("50"+suffix);
        tempIitProject.setInternalOrder1070("70"+suffix);
        tempIitProject.setInternalOrder1060("60"+suffix);
        tempIitProject.setInternalOrder1080("80"+suffix);
        tempIitProject.setInternalOrder1230("23"+suffix);
        tempIitProject.setInternalOrder1250("25"+suffix);
        tempIitProject.setInternalOrder1200("12"+suffix);
        tempIitProject.setInternalOrder1100("11"+suffix);
        tempIitProject.setInternalOrder2100("21"+suffix);

        return tempIitProject;
    }

    /**
     * 查询费控系统项目主数据
     *
     * @param id 费控系统项目主数据主键
     * @return 费控系统项目主数据
     */
    @Override
    public IitProject selectIitProjectById(Long id)
    {
        return iitProjectMapper.selectIitProjectById(id);
    }

    /**
     * 查询费控系统项目主数据列表
     *
     * @param iitProject 费控系统项目主数据
     * @return 费控系统项目主数据
     */
    @Override
    public List<IitProject> selectIitProjectList(IitProject iitProject)
    {
        return iitProjectMapper.selectIitProjectList(iitProject);
    }

    /**
     * 新增费控系统项目主数据
     *
     * @param iitProject 费控系统项目主数据
     * @return 结果
     */
    @Override
    public int insertIitProject(IitProject iitProject)
    {
        return iitProjectMapper.insertIitProject(iitProject);
    }

    /**
     * 修改费控系统项目主数据
     *
     * @param iitProject 费控系统项目主数据
     * @return 结果
     */
    @Override
    public int updateIitProject(IitProject iitProject)
    {
        return iitProjectMapper.updateIitProject(iitProject);
    }

    /**
     * 批量删除费控系统项目主数据
     *
     * @param ids 需要删除的费控系统项目主数据主键
     * @return 结果
     */
    @Override
    public int deleteIitProjectByIds(Long[] ids)
    {
        return iitProjectMapper.deleteIitProjectByIds(ids);
    }

    /**
     * 删除费控系统项目主数据信息
     *
     * @param id 费控系统项目主数据主键
     * @return 结果
     */
    @Override
    public int deleteIitProjectById(Long id)
    {
        return iitProjectMapper.deleteIitProjectById(id);
    }

    /**
     * 判断是否应跳过系统自动编号流程
     * 规则：项目类型为 A/B 且表单中已存在人工项目号
     */
    private boolean shouldSkipForManualAB(ExpClaimHeaderCommonVo claim) {
        if (claim == null) {     // 安全保护
            return false;
        }
        String type   = Optional.ofNullable(claim.getColumn15Obj())
                                .map(obj -> obj.getString("value_meaning"))
                                .orElse("");
        boolean isAB  = "A类".equals(type) || "B类".equals(type);

        // 业务约定：人工项目号填写在 Column46（项目号字段）
        boolean hasManualCode = StringUtils.hasText(claim.getColumn46());

        return isAB && hasManualCode;
    }
}
