package com.cloudpense.akesobio.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudpense.akesobio.domain.IitProject;
import com.cloudpense.standard.vo.claim.ExpClaimHeaderCommonVo;

import java.util.List;


/**
 * 费控系统项目主数据Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-15
 */
public interface IitProjectService extends IService<IitProject>
{

    //根据项目号 公司代码 获取内部订单号

    public String getInternalOrderNumber(String projectNumber, String companyCode);
    //生成平台项目编号
    public IitProject generatePlatformProjectNumber(String param) throws Exception;
    public IitProject generateSeriesMeetingProjectNumber(String param) throws Exception;
    public IitProject pushIITProjectNumber(String param) throws Exception;
    //发票类型判断和COLUMN175字段自动填充
    public void processInvoiceTypeAndColumn175(String param) throws Exception;
    //生成 IIT Project Number
    public IitProject generateIITProjectNumber(String generationPipelines,String iitProjectType,String jobNum,String area);
    //根据IIT Project Number 生成sap 内部订单号
    public IitProject generateSapOrderNumber(String iitProjectNumber,String iitProjectType,String department,IitProject tempIitProject);
    /**
     * 查询费控系统项目主数据
     * 
     * @param id 费控系统项目主数据主键
     * @return 费控系统项目主数据
     */
    public IitProject selectIitProjectById(Long id);

    /**
     * 查询费控系统项目主数据列表
     * 
     * @param iitProject 费控系统项目主数据
     * @return 费控系统项目主数据集合
     */
    public List<IitProject> selectIitProjectList(IitProject iitProject);

    /**
     * 新增费控系统项目主数据
     * 
     * @param iitProject 费控系统项目主数据
     * @return 结果
     */
    public int insertIitProject(IitProject iitProject);

    /**
     * 修改费控系统项目主数据
     * 
     * @param iitProject 费控系统项目主数据
     * @return 结果
     */
    public int updateIitProject(IitProject iitProject);

    /**
     * 批量删除费控系统项目主数据
     * 
     * @param ids 需要删除的费控系统项目主数据主键集合
     * @return 结果
     */
    public int deleteIitProjectByIds(Long[] ids);

    /**
     * 删除费控系统项目主数据信息
     * 
     * @param id 费控系统项目主数据主键
     * @return 结果
     */
    public int deleteIitProjectById(Long id);
}
