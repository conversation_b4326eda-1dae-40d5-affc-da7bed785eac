package com.cloudpense.akesobio.service.impl;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.akesobio.dto.*;
import com.cloudpense.akesobio.entity.DeliverieMqueue;
import com.cloudpense.akesobio.entity.PageInfoData;
import com.cloudpense.akesobio.entity.SharedDocumentData;
import com.cloudpense.akesobio.entity.SharedDocumentDataVo;
import com.cloudpense.akesobio.service.CloudpenseApiService;
import com.cloudpense.akesobio.service.ReceiptService;
import com.cloudpense.akesobio.service.ReimbursementService;
import com.cloudpense.akesobio.util.CalendarAdjust;
//import com.cloudpense.akesobio.util.CloudpenseApiUtils;
import com.cloudpense.akesobio.util.http.HttpUtil;
import com.cloudpense.akesobio.vo.ValResultCommonVo;
import com.cloudpense.common.bean.MqQueue;
import com.cloudpense.common.bean.Response;
import com.cloudpense.common.service.YjApiService;
import com.cloudpense.common.service.claim.YjClaimService;
import com.cloudpense.common.vo.ServiceRes;
import com.cloudpense.standard.enumeration.claim.WorkflowStatusEnum;
import com.cloudpense.standard.vo.claim.ExpClaimHeaderCommonVo;
import com.cloudpense.standard.vo.claim.ExpClaimLineCommonVo;
import com.cloudpense.standard.vo.claim.SimpleFndWorkflowPathVo;
import com.cloudpense.standard.vo.document.base.ExpHeaderTypeVo;
import com.cloudpense.standard.vo.masterdata.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReimbursementServiceImpl implements ReimbursementService {

    @Resource
    private CloudpenseApiService cloudpenseApiService;

    @Value("${fk.updateWorkFlowUrl}")
    private String updateWorkFlowUrl;

    @Value("${fk.selectValueUrl}")
    private String selectValueUrl;

    @Value("${fk.updateClaimUrl}")
    private String updateClaimUrl;

    @Value("${fk.batchQueryUrl}")
    private String batchQueryUrl;

    @Value("${fk.employeeInfoUrl}")
    private String employeeInfoUrl;

    @Value("${fk.documentSharingAddUrl}")
    private String documentSharingAddUrl;

    @Value("${fk.findListByConditionUrl}")
    private String findListByConditionUrl;

    @Autowired
    YjApiService yjApiService;

    @Resource
    private ReceiptService receiptService;
    @Autowired
    YjClaimService yjClaimService;

    @Override
    public ValResultCommonVo getPersonalReimbursement(ExpClaimHeaderCommonVo vo) {
        ValResultCommonVo<Integer> result = new ValResultCommonVo<>();
        Map<Integer, String> message = new HashMap<>();
        result.setValue(0);
        result.setResult(0);
        result.setMessage(message);

        List<ExpClaimHeaderCommonVo> headerLinks = vo.getHeaderLinks();

        List<String> listStatus = new ArrayList<>();
        //已提交
        listStatus.add("submitted");
        //审批中
        listStatus.add("approving");
        //审批通过
        listStatus.add("approved");
        //已完成
        listStatus.add("closed");
        //业务审批通过
        listStatus.add("preapproved");
        //财务审批通过
        listStatus.add("checked");
        //打回修改
        listStatus.add("modifying");
//        //未提交
//        listStatus.add("incomplete");

        List<String> listCode = new ArrayList<>();
        //差旅申请 YXCL01
        listCode.add("YXCL01");
        //费用申请  YXFY01
        listCode.add("YXFY01");
        //准入申请  YXZR01
        listCode.add("YXZR01");
        //会议申请 YXHYCRM
        //listCode.add("YXHYCRM");
        //ZRCL01-差旅申请-准入 ZRCL01
        listCode.add("ZRCL01");
        //ZRFY01-费用申请-准入 ZRFY01
        listCode.add("ZRFY01");
        //ZRHYCRM-会议申请-准入 ZRHYCRM
        //listCode.add("ZRHYCRM");
        //YXHY03-会议总结
        listCode.add("YXHY03");
        //ZRHY03-会议总结-准入
        listCode.add("ZRHY03");


        String number = vo.getCode();
        log.info("校验个人报销-报销单单号为：" + number);
        for (int i = 0; i < headerLinks.size(); i++) {
            //申请单关联单据报销总金额
            BigDecimal totalAmountNew = new BigDecimal(0);
            //关联申请单据单号
            String oddNumbers = headerLinks.get(i).getCode();
            ExpHeaderTypeVo headerTypeVo = headerLinks.get(i).getHeaderTypeVo();
            //单据代码
            String documentCode = headerTypeVo.getCode();
            log.info("校验个人报销-报销单单号为：" + number + ",关联明细第" + (i + 1) + "条,关联申请单单号为：" + oddNumbers + ",关联申请单代码为：" + documentCode);
            if (listCode.contains(documentCode)) {
               /* if (documentCode.equals("YXHY03") || documentCode.equals("ZRHY03")) {
                    List<cn.hutool.json.JSONObject> objectList = getRelatedDocumentInfo4(oddNumbers);
                    //根据申请单查询出的关联单据条数信息
                    cn.hutool.json.JSONArray jsonArray = objectList.get(0).getJSONArray("header_links");
                    if (jsonArray.size() > 0) {
                        cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(0);
                        //会议申请code
                        String meetingCode = jsonObject.getStr("code");
                        oddNumbers = meetingCode;
                    }
                }*/
                List<cn.hutool.json.JSONObject> objectList = getRelatedDocumentInfo(oddNumbers);
                //根据申请单查询出的关联单据条数信息
                cn.hutool.json.JSONArray jsonArray = objectList.get(0).getJSONArray("header_back_links");
                log.info("关联申请单单号为：" + oddNumbers + ",查询出的关联单据条数信息：" + jsonArray.size() + "条");

                /** 关联申请单总金额 */
                BigDecimal totalAmount2 = new BigDecimal(0);

                if (jsonArray.size() > 0) {
                    for (int j = 0; j < jsonArray.size(); j++) {
                        cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(j);
                        cn.hutool.json.JSONObject headerTpye = jsonObject.getJSONObject("header_type");
                        //单据编码
                        String code = headerTpye.getStr("code");
                        BigDecimal total_amount = new BigDecimal(0);
                        //单据状态
                        String status = jsonObject.getStr("status");
                        //申请单单号
                        String applicationNumber = jsonObject.getStr("code");
                        log.info("关联申请单单号为：" + applicationNumber + ",单据编码为：" + code + ",单据状态为：" + status);

                        if (listStatus.contains(status) || number.equals(jsonObject.getStr("code"))) {
                            switch (code) {
                                //差旅报销  YXCL02
                                case "YXCL02":
                                    total_amount = jsonObject.getBigDecimal("total_amount");
                                    break;
                                //费用报销  YXFY02
                                case "YXFY02":
                                    total_amount = jsonObject.getBigDecimal("total_amount");
                                    break;
                                //准入核报  YXZR02
                                case "YXZR02":
                                    total_amount = jsonObject.getBigDecimal("column101");
                                    break;
                                //会议核报  YXHY02
                                case "YXHY02":
                                    total_amount = jsonObject.getBigDecimal("column101");
                                    break;
                                //差旅报销-准入  ZRCL02
                                case "ZRCL02":
                                    total_amount = jsonObject.getBigDecimal("total_amount");
                                    break;
                                //费用报销-准入  ZRFY02
                                case "ZRFY02":
                                    total_amount = jsonObject.getBigDecimal("total_amount");
                                    break;
                                //会议核报-准入  ZRHY02
                                case "ZRHY02":
                                    total_amount = jsonObject.getBigDecimal("column101");
                                    break;
                            }
                            if (total_amount != null) {
                                totalAmountNew = totalAmountNew.add(total_amount);
                            }
                        }
                    }
                }

                List<ExpClaimHeaderCommonVo> list = vo.getHeaderLinks();

                if (list.size() > 0) {
                    //YXCL01-差旅申请    ZRCL01-差旅申请-准入
                    if (documentCode.equals("YXCL01") || documentCode.equals("ZRCL01")) {
                        totalAmount2 = list.get(i).getTotalAmount();
                    } else if (documentCode.equals("YXHY03") || documentCode.equals("ZRHY03")) {
                        List<cn.hutool.json.JSONObject> objectListNew = getRelatedDocumentInfo4(oddNumbers);
                        //根据申请单查询出的关联单据条数信息
                        cn.hutool.json.JSONArray jsonArrayNew = objectListNew.get(0).getJSONArray("header_links");
                        if (jsonArrayNew.size() > 0) {
                            cn.hutool.json.JSONObject jsonObject = jsonArrayNew.getJSONObject(0);
                            String columnJson = jsonObject.getStr("column_json");
                            cn.hutool.json.JSONObject jsonObjectJson = new cn.hutool.json.JSONObject(columnJson);
                            totalAmount2 = jsonObjectJson.getBigDecimal("column101");
                        }
                    } else {
                        String columnJson = list.get(i).getColumnJson();
                        cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject(columnJson);
                        totalAmount2 = jsonObject.getBigDecimal("column101");

                    }

                }
               /* if (list.size() > 0) {
                    totalAmount2 = list.stream().filter(a -> a.getTotalAmount() != null)
                            .map(ExpClaimHeaderCommonVo::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                } else {
                    log.info("报销单{}未关联申请单");
                    return result;
                }*/
                //关联申请单总金额 >= 申请单关联单据报销总金额
                if (totalAmount2.compareTo(totalAmountNew) >= 0) {
                    result.setValue(0);
                    result.setResult(0);
                    return result;
                }
                /** 关联申请单总金额 102.5% */
                BigDecimal totalAmount3 = totalAmount2.multiply(new BigDecimal(1.025)).setScale(2, BigDecimal.ROUND_HALF_UP);
                /** 关联申请单总金额 110% */
                BigDecimal totalAmount4 = totalAmount2.multiply(new BigDecimal(1.1)).setScale(2, BigDecimal.ROUND_HALF_UP);

                //申请单关联单据报销总金额 >= 申请单金额110%
                if (totalAmountNew.compareTo(totalAmount4) >= 0) {
                    Integer headerId = vo.getHeaderId();
                    BigDecimal amountMsg = totalAmountNew.subtract(totalAmount2);
                    String excessMsg = "报销金额已超申请单金额110%，金额为：" + amountMsg;
                    Map<Integer, JSONObject> templateParam = new HashMap<>();
                    result.setValue(1);
                    result.setResult(1);
                    JSONObject param = new JSONObject();
                    param.put("line_id", headerId);
                    param.put("ExcessMsg", excessMsg);
                    templateParam.put(headerId, param);
                    result.setTemplateParam(templateParam);
                    return result;
                }

                BigDecimal total = new BigDecimal(150000);
                //申请单关联单据报销总金额 >= 申请单金额102.5% && 申请单金额110% > 申请单关联单据报销总金额
                if (totalAmountNew.compareTo(totalAmount3) >= 0 && totalAmount4.compareTo(totalAmountNew) > 0) {
                    result.setValue(3);
                    result.setResult(3);
                    return result;
                }


                //申请单关联单据报销总金额 > 申请单金额102.5%  并且  申请单关联单据报销总金额< 150000
                if (totalAmount3.compareTo(totalAmountNew) > 0 && total.compareTo(totalAmountNew) > 0) {
                    result.setValue(2);
                    result.setResult(2);
                    return result;
                }
            }
        }
        return result;


        //关联申请单据单号
        //String oddNumbers = headerLinks.get(0).getCode();


        // ExpHeaderTypeVo headerTypeVo = headerLinks.get(0).getHeaderTypeVo();
        //申请单据代码
        // String code = headerTypeVo.getCode();

        // List<cn.hutool.json.JSONObject> objectList = getRelatedDocumentInfo(oddNumbers);

        //根据申请单查询出的关联单据条数信息
        // cn.hutool.json.JSONArray jsonArray = objectList.get(0).getJSONArray("header_back_links");


        //Integer num = jsonArray.size();

       /* List<String> stringList = new ArrayList<>();
        //差旅申请 YXCL01
        stringList.add("YXCL01");
        //费用申请  YXFY01
        stringList.add("YXFY01");
        //ZRCL01-差旅申请-准入 ZRCL01
        stringList.add("ZRCL01");
        //ZRFY01-费用申请-准入 ZRFY01
        stringList.add("ZRFY01");
        //如果是差旅申请，费用申请，并且关联2次，则不能提交
        if (stringList.contains(code) && num > 1) {
            result.setValue(4);
            result.setResult(4);
            return result;
        }*/

        //return result;
    }

    @Override
    public ValResultCommonVo getContractApplication(ExpClaimHeaderCommonVo vo) {
        ValResultCommonVo<Integer> result = new ValResultCommonVo<>();
        Map<Integer, String> message = new HashMap<>();
        result.setValue(1);
        result.setResult(1);
        result.setMessage(message);

        /** 采购合同总金额 */
        BigDecimal totalAmount1 = vo.getTotalAmount();
        /** 申请单总金额 */
        BigDecimal totalAmount2 = new BigDecimal(0);

        List<ExpClaimHeaderCommonVo> list = vo.getHeaderLinks();
        if (list.size() > 0) {
            totalAmount2 = list.stream().filter(a -> a.getTotalAmount() != null)
                    .map(ExpClaimHeaderCommonVo::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            log.info("采购合同{}未关联申请单");
            return result;
        }
        int count = 0;
        if (totalAmount1.compareTo(totalAmount2) > 0) {
            count = 1;
        }
        result.setResult(count);
        result.setValue(count);
        return result;
    }

    @Override
    public ValResultCommonVo getCorporatePayment(ExpClaimHeaderCommonVo vo) {
        ValResultCommonVo<Integer> result = new ValResultCommonVo<>();
        Map<Integer, String> message = new HashMap<>();
        result.setValue(0);
        result.setResult(0);
        result.setMessage(message);

        List<ExpClaimHeaderCommonVo> headerLinks = vo.getHeaderLinks();

        List<String> listStatus = new ArrayList<>();
        //已提交
        listStatus.add("submitted");
        //审批中
        listStatus.add("approving");
        //审批通过
        listStatus.add("approved");
        //已完成
        listStatus.add("closed");
        //业务审批通过
        listStatus.add("preapproved");
        //财务审批通过
        listStatus.add("checked");
        //打回修改
        listStatus.add("modifying");

        String number = vo.getCode();

        log.info("对公合同-支付总金额校验报销单号为：" + number + ",关联采购合同-会议总结有" + headerLinks.size() + "条");

        for (int i = 0; i < headerLinks.size(); i++) {
            //申请单关联单据报销总金额(累计支付金额)
            BigDecimal totalAmountNew = new BigDecimal(0);
            //关联申请单据单号
            String oddNumbers = headerLinks.get(i).getCode();
            ExpHeaderTypeVo headerTypeVo = headerLinks.get(i).getHeaderTypeVo();
            //单据代码
            String documentCode = headerTypeVo.getCode();
            log.info("第" + (i + 1) + "条--关联申请单据单号" + oddNumbers + ",单据代码为：" + documentCode);
            //YXHT-采购合同
            if (documentCode.equals("YXHT")) {
                List<cn.hutool.json.JSONObject> objectList = getRelatedDocumentInfo(oddNumbers);
                log.info("查询单据：" + oddNumbers + "-单据相关联信息为：" + objectList.size() + "条");
                //根据申请单查询出的关联单据条数信息
                cn.hutool.json.JSONArray jsonArray = objectList.get(0).getJSONArray("header_back_links");
                if (jsonArray.size() > 0) {
                    log.info("查询objectList的jsonArray：" + jsonArray.size() + "条");
                    for (int j = 0; j < jsonArray.size(); j++) {
                        log.info("jsonArray=" + jsonArray.getJSONObject(j));
                        cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(j);
                        cn.hutool.json.JSONObject headerTpye = jsonObject.getJSONObject("header_type");
                        log.info("headerTpye=" + headerTpye);
                        String code = headerTpye.getStr("code");
                        log.info("单据编码=" + code);
                        //BigDecimal total_amount = jsonObject.getBigDecimal("total_amount");
                        BigDecimal total_amount = new BigDecimal(0);
                        String status = jsonObject.getStr("status");
                        log.info("单据状态=" + status);
                        String aaaa = jsonObject.getStr("code");
                        log.info("单号--" + aaaa);
                        if (listStatus.contains(status) || number.equals(jsonObject.getStr("code"))) {
                            log.info("进入了switch--" + status);
                            switch (code) {
                                //对公预付   YXDG01
                                case "YXDG01":
                                    total_amount = jsonObject.getBigDecimal("advance_amount");
                                    break;
                                //对公应付   YXDG02
                                case "YXDG02":
                                    total_amount = jsonObject.getBigDecimal("total_pay_amount");
                                    break;
                                //会议核报-准入  ZRHY02
                                case "ZRHY02":
                                    total_amount = jsonObject.getBigDecimal("column21");
                                    break;
                                //准入核报 YXZR02
                                case "YXZR02":
                                    total_amount = jsonObject.getBigDecimal("column21");
                                    break;
                                //会议核报  YXHY02
                                case "YXHY02":
                                    total_amount = jsonObject.getBigDecimal("column21");
                                    break;
                            }
                            if (total_amount != null) {
                                log.info("查询jsonArray里面的金额：" + total_amount);
                                totalAmountNew = totalAmountNew.add(total_amount);
                            }
                        }
                    }
                }
                /** 关联采购合同 单据总金额 */
                BigDecimal totalAmount2 = new BigDecimal(0);
                List<ExpClaimHeaderCommonVo> list = vo.getHeaderLinks();
                if (list.size() > 0) {
                    totalAmount2 = list.get(i).getTotalAmount();
                }
                //关联采购合同单据总金额 >= 累计支付金额
                if (totalAmount2.compareTo(totalAmountNew) >= 0) {
                    log.info("//关联采购合同单据总金额 >= 累计支付金额-" + "totalAmountNew=" + totalAmountNew);
                    log.info("//关联采购合同单据总金额 >= 累计支付金额-" + "totalAmount2=" + totalAmount2);
                    result.setValue(0);
                    result.setResult(0);
                    return result;
                }

                /** 关联采购合同单据总金额 102.5% */
                BigDecimal totalAmount3 = totalAmount2.multiply(new BigDecimal(1.025)).setScale(2, BigDecimal.ROUND_HALF_UP);

                //累计支付金额 >= 关联采购合同单据总金额 102.5%
                if (totalAmountNew.compareTo(totalAmount3) >= 0) {
                    log.info("//累计支付金额 >= 关联采购合同单据总金额102.5%-" + "totalAmountNew=" + totalAmountNew);
                    log.info("//累计支付金额 >= 关联采购合同单据总金额102.5%-" + "totalAmount3=" + totalAmount3);
                    result.setValue(1);
                    result.setResult(1);
                    return result;
                }

                BigDecimal total = new BigDecimal(150000);

                //累计支付金额 > 关联采购合同单据总金额  并且   关联采购合同单据总金额 102.5% > 累计支付金额
                if (totalAmountNew.compareTo(totalAmount2) > 0 && totalAmount3.compareTo(totalAmountNew) > 0) {
                    //关联采购合同单据总金额102.5% > 累计支付金额 && 累计支付金额< 15w
                    if (totalAmount3.compareTo(totalAmountNew) > 0 && total.compareTo(totalAmountNew) > 0) {
                        result.setValue(0);
                        result.setResult(0);
                        return result;
                    }

                    // 关联采购合同单据总金额102.5% > 累计支付金额 并且  申请单关联单据报销总金额 >= 150000
                    if (totalAmount3.compareTo(totalAmountNew) > 0 && totalAmountNew.compareTo(total) >= 0) {
                        log.info("//关联采购合同单据总金额102.5% > 累计支付金额 并且  申请单关联单据报销总金额 >= 150000-" + "totalAmountNew=" + totalAmountNew);
                        log.info("//关联采购合同单据总金额102.5% > 累计支付金额 并且  申请单关联单据报销总金额 >= 150000-" + "totalAmount3=" + totalAmount3);
                        log.info("//关联采购合同单据总金额102.5% > 累计支付金额 并且  申请单关联单据报销总金额 >= 150000-" + "total=" + total);
                        result.setValue(3);
                        result.setResult(3);
                        return result;
                    }
                }
            }

        }
        // 判断 对公支付总金额 是否在 对公支付上限总金额 和 对公支付下限总金额 之间
        //boolean isWithinRange = totalAmount4.compareTo(totalAmount1) <= 0 && totalAmount1.compareTo(totalAmount3) <= 0;
        return result;
    }

    @Override
    public ServiceRes receivedInterface(ExpClaimHeaderCommonVo claim, MqQueue mqQueue) throws Exception {
        UserApiVo submitUserDto = claim.getSubmitUserVo();
        String jobNumber = submitUserDto.getEmployeeNumber();
        String bizId = UUID.randomUUID().toString();
        long timestamp = System.currentTimeMillis();
        String lov_name = "YGZZ";
        String codeNew = jobNumber;

        //获取token
        String access_token = cloudpenseApiService.getToken();
        //查询值列表 离职人员清单
        String url = selectValueUrl + "?timestamp=" + timestamp + "&bizId=" + bizId + "&lov_name=" + lov_name + "&code=" + codeNew + "&access_token=" + access_token;
        String result = HttpUtil.sendGet(url);

        // 解析JSON字符串为JSONObject
        JSONObject root = JSONObject.parseObject(result);
        // 获取data对象
        JSONObject data = root.getJSONObject("data");
        // 获取page_info数组
        JSONArray pageInfoArray = data.getJSONArray("page_info");
        // 获取page_info数组的第一个元素
        JSONObject pageInfo = pageInfoArray.getJSONObject(0);
        // 获取enabled_flag的值
        String enabledFlag = pageInfo.getString("enabled_flag");

        String msg = "";
        String status = "";
        if (enabledFlag.equals("Y")) {
            //审批通过
            status = "approved";
        } else {
            //审批中
            status = "approving";
        }

        ReceiptQueryRequest request = new ReceiptQueryRequest();
        request.setBizId(UUID.randomUUID().toString());
        request.setTimestamp(System.currentTimeMillis());
        request.setDocument_num(claim.getCode());

        //查询单据发票  排除会议核报YXHY02，会议核报准入ZRHY02，对公预付YXDG01，对公应付YXDG02
        //，4个表单，其他核报单的离职岗节点加 校验发票类型是电子发票，不论在职/离职 都自动通过，如果发票类型是纸单 就保留节点卡住，需要业务提交纸质单据，财务收单后才能自动审批通过
        ReceiptQueryResponse receiptQueryResponse = receiptService.queryInvoiceCount(request);
        if (receiptQueryResponse.getInvoice_number().equals(receiptQueryResponse.getE_invoice_number())
                && !claim.getCode().contains("YXHY02")
                && !claim.getCode().contains("ZRHY02")
                && !claim.getCode().contains("YXDG01")
                && !claim.getCode().contains("YXDG02"))
            status = "approved";


        WorkFlowStatusDto statusDto = new WorkFlowStatusDto();
        statusDto.setStatus(status);
        statusDto.setNote(msg);
        statusDto.setPathId(mqQueue.getPathId());
        statusDto.setEmployeeNumber("KF001");

        ServiceRes workFlowRes = updateWorkFlowStatus(statusDto);
        return ServiceRes.success("执行完成");
    }

    @Override
    public ServiceRes deliveryReceived(ExpClaimHeaderCommonVo claim, DeliverieMqueue mqQueue) throws Exception {
        String deliveryStatus = mqQueue.getDeliveryStatus();
        List<SimpleFndWorkflowPathVo> workflowPathVos = claim.getWorkflowPathVos();
        List<SimpleFndWorkflowPathVo> collect = workflowPathVos.stream()
                .filter(workflowPathVo -> Objects.nonNull(workflowPathVo.getPosition())
                        && "KF01".equals(workflowPathVo.getPosition().getPositionCode())
                        && "approving".equals(workflowPathVo.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect) || collect.size() == 0) {
            return ServiceRes.failure("当前节点不在虚拟岗，不处理");
        }
        Integer pathId = collect.get(0).getPathId();

        String workFlowStatus = WorkflowStatusEnum.APPROVING.getCode();
        String status = claim.getStatus();
        log.info("receivedInterface,status: {},deliveryStatus:{}", status, JSONObject.toJSONString(mqQueue));
        String msg = "";
        //退单（拒单）
        if ("rejected".equals(deliveryStatus)) {
            workFlowStatus = WorkflowStatusEnum.REJECTED.getCode();
        }
        //收单
        if ("received".equals(deliveryStatus)) {
            workFlowStatus = WorkflowStatusEnum.APPROVED.getCode();
        }

        WorkFlowStatusDto statusDto = new WorkFlowStatusDto();
        statusDto.setStatus(workFlowStatus);
        statusDto.setNote(msg);
        statusDto.setPathId(pathId);
        statusDto.setEmployeeNumber("KF001");
        ServiceRes workFlowRes = updateWorkFlowStatus(statusDto);
        return ServiceRes.success("执行完成");
    }

    @Override
    public Response personalTotalAmount(String param) throws JsonProcessingException {
        Response response = new Response();
        response.setResCode(200000);
        response.setResMsg("成功");
        JSONObject jsonObject = JSONObject.parseObject(param);
        ExpClaimHeaderCommonVo claim = null;
        if (jsonObject.get("claimVo") != null) {
            claim = JSONUtil.toBean(jsonObject.getString("claimVo"), ExpClaimHeaderCommonVo.class);
            // 获取claimVo对象
            JSONObject claimVo = jsonObject.getJSONObject("claimVo");

            String number = claimVo.getString("code");
            log.info("费用报销总金额单据单号为：" + number);

            //获取单据预算期间
            JSONObject glPeriodVo = claimVo.getJSONObject("glPeriodVo");
            String budgetPeriod = glPeriodVo.getString("code");
            log.info("费用报销总金额单据预算期间:" + budgetPeriod);

            //获取提交日期时间
            long submitDate = claimVo.getLong("submitDate");


            // 1. 将时间戳转换为Instant
            Instant instant = Instant.ofEpochMilli(submitDate);

            // 2. 转换为带时区的日期时间（使用上海时区）
            ZonedDateTime originalDateTime = instant.atZone(ZoneId.of("Asia/Shanghai"));

            // 3. 获取当月的第一天（月初）00:00:00
            ZonedDateTime firstDayOfMonth = originalDateTime
                    .with(TemporalAdjusters.firstDayOfMonth())
                    .with(LocalTime.MIN);

            // 4. 获取当月的最后一天（月末）23:59:59.999
            ZonedDateTime lastDayOfMonth = originalDateTime
                    .with(TemporalAdjusters.lastDayOfMonth())
                    .with(LocalTime.MAX);

            // 5. 基于月初计算上6个月
            ZonedDateTime sixMonthsBefore = firstDayOfMonth.minusMonths(6);

            // 6. 基于月初计算下6个月
            ZonedDateTime sixMonthsAfter = firstDayOfMonth.plusMonths(6);

            // 转换为时间戳（毫秒）
            long firstDayMillis = firstDayOfMonth.toInstant().toEpochMilli();
            long lastDayMillis = lastDayOfMonth.toInstant().toEpochMilli();

            //时间段
            long firstDayTimestamp = sixMonthsBefore.toInstant().toEpochMilli();
            long lastDayTimestamp = sixMonthsAfter.toInstant().toEpochMilli();


            // 获取submitUserVo对象
            JSONObject submitUserVo = claimVo.getJSONObject("submitUserVo");
            // 获取提交人工号
            String jobNumber = submitUserVo.getString("employeeNumber");
            // String url = "/plugin/claim/kangfangGetClaim";

            ExpHeaderTypeVo vo = claim.getHeaderTypeVo();
            String voCode = vo.getCode();

            List<String> list1 = new ArrayList<>();


            //一、营销中心涉及的单据（任何一张单的“个人月度对私报销”都需要累计计算以下4张报销对私金额，含自动核报单）
            // 1、FSAB差旅报销（此类报销单据均为对私） YXCL02
            // 2、FSBB-费用报销（此类报销单据均为对私）YXFY02
            // 3、FSCD-会议核报（按费用行详情“支付对象”-“支付给个人”带出对私金额） YXHY02
            // 4、FSFB-终端准入核报（此类报销单据均为对私）YXZR02
            if (voCode.equals("YXCL02") || voCode.equals("YXFY02") || voCode.equals("YXHY02") || voCode.equals("YXZR02")) {
                list1.add("YXCL02");
                list1.add("YXFY02");
                list1.add("YXHY02");
                list1.add("YXZR02");
            }

            //二、准入部门涉及的单据（任何一张单的“个人月度对私报销”都需要累计计算以下3张报销单金额，含自动核报单）
            // 1、FMAB-差旅报销-准入（此类报销单据均为对私）  ZRCL02
            // 2、FMBB-费用报销-准入（此类报销单据均为对私）  ZRFY02
            // 3、FMCC-会议核报-准入（按费用行详情“支付对象”-“支付给个人”带出对私金额）ZRHY02
            if (voCode.equals("ZRCL02") || voCode.equals("ZRFY02") || voCode.equals("ZRHY02")) {
                list1.add("ZRCL02");
                list1.add("ZRFY02");
                list1.add("ZRHY02");
            }

            //一、营销中心涉及的单据（任何一张单的“个人月度对私申请总额”都需要累计计算以下4张申请单金额）
            // 1、FSAA-差旅申请（此类申请单据均为对私）YXCL01
            // 2、FSBA-费用申请（按费用行详情“支付对象”-“支付给个人”带出对私金额）YXFY01
            // 3、FSCB-会议申请（按费用行详情“支付对象”-“支付给个人”带出对私金额）YXHYCRM
            // 4、FSFA-终端准入申请（按费用行详情“支付对象”-“支付给个人”带出对私金额）YXZR01
            if (voCode.equals("YXCL01") || voCode.equals("YXFY01") || voCode.equals("YXHYCRM") || voCode.equals("YXZR01")) {
                list1.add("YXCL01");
                list1.add("YXFY01");
                list1.add("YXHYCRM");
                list1.add("YXZR01");
            }

            //二、准入部门涉及的单据（任何一张单的“个人月度对私申请”都需要累计计算以下3张申请单金额）：
            // 1、FMAA-差旅申请-准入（此类申请单据全部均为对私） ZRCL01
            // 2、FMBA-费用申请-准入（按费用行详情“支付对象”-“支付给个人”带出对私金额）ZRFY01
            // 3、FMCA-会议申请-准入（按费用行详情“支付对象”-“支付给个人”带出对私金额）ZRHYCRM
            if (voCode.equals("ZRCL01") || voCode.equals("ZRFY01") || voCode.equals("ZRHYCRM")) {
                list1.add("ZRCL01");
                list1.add("ZRFY01");
                list1.add("ZRHYCRM");
            }
            log.info("费用报销总金额单据代码集合:" + list1);

            List<String> list2 = new ArrayList<>();
            //已提交
            list2.add("submitted");
            //审批中
            list2.add("approving");
            //审批通过
            list2.add("approved");
            //已完成
            list2.add("closed");
            //业务审批通过
            list2.add("preapproved");
            //财务审批通过
            list2.add("checked");
            //打回修改
            list2.add("modifying");

            List<String> list3 = new ArrayList<>();
            list3.add(jobNumber);

            Map map = new HashMap();
            map.put("page_size", "100");
            map.put("page_num", "1");
            map.put("start_datetime", firstDayTimestamp);
            map.put("end_datetime", lastDayTimestamp);
            //submit_date  提交时间
            map.put("date_field_name", "submit_date");
            map.put("type_codes", list1);
            map.put("statuses", list2);
            map.put("charge_user_codes", list3);

            ServiceRes api = yjApiService.api(batchQueryUrl, HttpMethod.POST, JSONObject.toJSONString(map), null);
            String code = api.getCode();
            if ("500000".equals(code) || "000000".equals(code)) {
                throw new RuntimeException(api.getMsg());
            }
            // 将data字段反序列化为JSONObject
            cn.hutool.json.JSONObject dataObject = (cn.hutool.json.JSONObject) api.getData();
            if (dataObject.size() > 0) {
                Integer totalPage = dataObject.getInt("total_page");
                double totalAmount = 0;
                if (totalPage > 0) {
                    for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
                        Map mapNew = new HashMap();
                        mapNew.put("page_size", "100");
                        mapNew.put("page_num", pageNum);
                        mapNew.put("start_datetime", firstDayTimestamp);
                        mapNew.put("end_datetime", lastDayTimestamp);
                        //submit_date  提交时间
                        mapNew.put("date_field_name", "submit_date");
                        mapNew.put("type_codes", list1);
                        mapNew.put("statuses", list2);
                        mapNew.put("charge_user_codes", list3);
                        ServiceRes apiNew = yjApiService.api(batchQueryUrl, HttpMethod.POST, JSONObject.toJSONString(map), null);
                        String codeNew = apiNew.getCode();
                        if ("500000".equals(codeNew) || "000000".equals(codeNew)) {
                            throw new RuntimeException(apiNew.getMsg());
                        }
                        // 将data字段反序列化为JSONObject
                        cn.hutool.json.JSONObject info = (cn.hutool.json.JSONObject) api.getData();

                        // 从 dataObject 中获取 page_info 数组
                        cn.hutool.json.JSONArray pageInfoArray = info.getJSONArray("page_info");

                        log.info("费用报销总金额单据关联条数:" + pageInfoArray.size() + "---单号：" + number);

                        // 判断pageInfoArray的条数是否大于0
                        if (pageInfoArray != null && pageInfoArray.size() > 0) {
                            for (int i = 0; i < pageInfoArray.size(); i++) {
                                cn.hutool.json.JSONObject pageInfoObject = pageInfoArray.getJSONObject(i);
                                //当单据状态为 已完成
                                String statusNew = pageInfoObject.getStr("status");
                                //表单创建来源
                                String from = pageInfoObject.getStr("column140");
                                //获取预算期间
                                cn.hutool.json.JSONObject glPeriodVoNew = pageInfoObject.getJSONObject("gl_period_vo");
                                String budgetPeriodNew = glPeriodVoNew.getStr("code");
                                cn.hutool.json.JSONObject object = pageInfoObject.getJSONObject("header_type");
                                String formCode = object.getStr("code");
                                String danhao = pageInfoObject.getStr("code");
                                log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",表单创建来源为:" + from);
                                if (budgetPeriod.equals(budgetPeriodNew)) {
                                    //一、营销中心涉及的单据（任何一张单的“个人月度对私报销”都需要累计计算以下4张报销对私金额，含自动核报单）
                                    // 1、FSAB差旅报销（此类报销单据均为对私） YXCL02
                                    if (formCode.equals("YXCL02")) {
                                        // 获取total_amount字段的值
                                        Double total_amount = pageInfoObject.getDouble("total_amount");
                                        log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",表单创建来源为:" + from + ",金额为：" + total_amount);
                                        if (total_amount != null) {
                                            totalAmount += total_amount; // 累加到总和
                                        }
                                    }
                                    // 2、FSBB-费用报销（此类报销单据均为对私）YXFY02
                                    if (formCode.equals("YXFY02")) {
                                        // 获取total_amount字段的值
                                        Double total_amount = pageInfoObject.getDouble("total_amount");
                                        log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",表单创建来源为:" + from + ",金额为：" + total_amount);
                                        if (total_amount != null) {
                                            totalAmount += total_amount; // 累加到总和
                                        }
                                    }
                                    if (formCode.equals("YXHY02")) {
                                        if (from.equals("手工创建")) {
                                            // 3、FSCD-会议核报（按费用行详情“支付对象”-“支付给个人”带出对私金额） YXHY02
                                            cn.hutool.json.JSONArray lineArray = pageInfoObject.getJSONArray("claim_lines");
                                            if (lineArray != null && lineArray.size() > 0) {
                                                for (int j = 0; j < lineArray.size(); j++) {
                                                    cn.hutool.json.JSONObject lineObject = lineArray.getJSONObject(j);
                                                    String pay_object = lineObject.getStr("pay_object");
                                                    if (pay_object.equals("U")) {
                                                        Double total_amount = lineObject.getDouble("receipt_amount");
                                                        log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",表单创建来源为:" + from + ",费用行支付给个人金额为：" + total_amount);
                                                        if (total_amount != null) {
                                                            totalAmount += total_amount; // 累加到总和
                                                        }
                                                    }
                                                }
                                            }
                                        } else {
                                            // 3、FSCD-会议核报 取支付总金额 total_pay_amount YXHY02
                                            // 获取total_amount字段的值
                                            Double total_amount = pageInfoObject.getDouble("total_pay_amount");
                                            log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",表单创建来源为:" + from + ",金额为：" + total_amount);
                                            if (total_amount != null) {
                                                totalAmount += total_amount; // 累加到总和
                                            }
                                        }
                                    }

                                    // 4、FSFB-终端准入核报（此类报销单据均为对私）YXZR02
                                    if (formCode.equals("YXZR02")) {
                                        // 获取total_amount字段的值
                                        Double total_amount = pageInfoObject.getDouble("total_amount");
                                        log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",表单创建来源为:" + from + ",金额为：" + total_amount);
                                        if (total_amount != null) {
                                            totalAmount += total_amount; // 累加到总和
                                        }
                                    }

                                    //二、准入部门涉及的单据（任何一张单的“个人月度对私报销”都需要累计计算以下3张报销单金额，含自动核报单）
                                    // 1、FMAB-差旅报销-准入（此类报销单据均为对私）  ZRCL02
                                    if (formCode.equals("ZRCL02")) {
                                        // 获取total_amount字段的值
                                        Double total_amount = pageInfoObject.getDouble("total_amount");
                                        log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",表单创建来源为:" + from + ",金额为：" + total_amount);
                                        if (total_amount != null) {
                                            totalAmount += total_amount; // 累加到总和
                                        }
                                    }
                                    // 2、FMBB-费用报销-准入（此类报销单据均为对私）  ZRFY02
                                    if (formCode.equals("ZRFY02")) {
                                        // 获取total_amount字段的值
                                        Double total_amount = pageInfoObject.getDouble("total_amount");
                                        log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",表单创建来源为:" + from + ",金额为：" + total_amount);
                                        if (total_amount != null) {
                                            totalAmount += total_amount; // 累加到总和
                                        }
                                    }
                                    // 3、FMCC-会议核报-准入（按费用行详情“支付对象”-“支付给个人”带出对私金额）ZRHY02
                                    if (formCode.equals("ZRHY02")) {
                                        if (from.equals("手工创建")) {
                                            // 3、FMCC-会议核报-准入（按费用行详情“支付对象”-“支付给个人”带出对私金额）ZRHY02
                                            cn.hutool.json.JSONArray lineArray = pageInfoObject.getJSONArray("claim_lines");
                                            if (lineArray != null && lineArray.size() > 0) {
                                                for (int j = 0; j < lineArray.size(); j++) {
                                                    cn.hutool.json.JSONObject lineObject = lineArray.getJSONObject(j);
                                                    String pay_object = lineObject.getStr("pay_object");
                                                    if (pay_object.equals("U")) {
                                                        Double total_amount = lineObject.getDouble("receipt_amount");
                                                        log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",表单创建来源为:" + from + ",费用行支付给个人金额为：" + total_amount);
                                                        if (total_amount != null) {
                                                            totalAmount += total_amount; // 累加到总和
                                                        }
                                                    }
                                                }
                                            }
                                        } else {
                                            // 3、FMCC-会议核报-准入 取支付总金额 total_pay_amount ZRHY02
                                            if (formCode.equals("ZRHY02")) {
                                                // 获取total_amount字段的值
                                                Double total_amount = pageInfoObject.getDouble("total_pay_amount");
                                                log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",金额为：" + total_amount);
                                                if (total_amount != null) {
                                                    totalAmount += total_amount; // 累加到总和
                                                }
                                            }
                                        }
                                    }


                                    //一、营销中心涉及的单据（任何一张单的“个人月度对私申请总额”都需要累计计算以下4张申请单金额）
                                    // 1、FSAA-差旅申请（此类申请单据均为对私）YXCL01
                                    if (formCode.equals("YXCL01")) {
                                        // 获取total_amount字段的值
                                        Double total_amount = pageInfoObject.getDouble("total_amount");
                                        log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",金额为：" + total_amount);
                                        if (total_amount != null) {
                                            totalAmount += total_amount; // 累加到总和
                                        }
                                    }
                                    // 2、FSBA-费用申请（按费用行详情“支付对象”-“支付给个人”带出对私金额）YXFY01
                                    if (formCode.equals("YXFY01")) {
                                        cn.hutool.json.JSONArray lineArray = pageInfoObject.getJSONArray("claim_lines");
                                        if (lineArray != null && lineArray.size() > 0) {
                                            for (int j = 0; j < lineArray.size(); j++) {
                                                cn.hutool.json.JSONObject lineObject = lineArray.getJSONObject(j);
                                                String pay_object = lineObject.getStr("pay_object");
                                                if (pay_object.equals("U")) {
                                                    Double total_amount = lineObject.getDouble("receipt_amount");
                                                    log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",费用行支付给个人金额为：" + total_amount);
                                                    if (total_amount != null) {
                                                        totalAmount += total_amount; // 累加到总和
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    // 3、FSCB-会议申请（按费用行详情“支付对象”-“支付给个人”带出对私金额）YXHYCRM
                                    if (formCode.equals("YXHYCRM")) {
                                        cn.hutool.json.JSONArray lineArray = pageInfoObject.getJSONArray("claim_lines");
                                        if (lineArray != null && lineArray.size() > 0) {
                                            for (int j = 0; j < lineArray.size(); j++) {
                                                cn.hutool.json.JSONObject lineObject = lineArray.getJSONObject(j);
                                                String pay_object = lineObject.getStr("pay_object");
                                                if (pay_object.equals("U")) {
                                                    Double total_amount = lineObject.getDouble("receipt_amount");
                                                    log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",费用行支付给个人金额为：" + total_amount);
                                                    if (total_amount != null) {
                                                        totalAmount += total_amount; // 累加到总和
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    // 4、FSFA-终端准入申请（按费用行详情“支付对象”-“支付给个人”带出对私金额）YXZR01
                                    if (formCode.equals("YXZR01")) {
                                        cn.hutool.json.JSONArray lineArray = pageInfoObject.getJSONArray("claim_lines");
                                        if (lineArray != null && lineArray.size() > 0) {
                                            for (int j = 0; j < lineArray.size(); j++) {
                                                cn.hutool.json.JSONObject lineObject = lineArray.getJSONObject(j);
                                                String pay_object = lineObject.getStr("pay_object");
                                                if (pay_object.equals("U")) {
                                                    Double total_amount = lineObject.getDouble("receipt_amount");
                                                    log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",费用行支付给个人金额为：" + total_amount);
                                                    if (total_amount != null) {
                                                        totalAmount += total_amount; // 累加到总和
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    //二、准入部门涉及的单据（任何一张单的“个人月度对私申请”都需要累计计算以下3张申请单金额）：
                                    //FMAA-差旅申请-准入（此类申请单据全部均为对私）
                                    if (formCode.equals("ZRCL01")) {
                                        // 获取total_amount字段的值
                                        Double total_amount = pageInfoObject.getDouble("total_amount");
                                        log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",金额为：" + total_amount);
                                        if (total_amount != null) {
                                            totalAmount += total_amount; // 累加到总和
                                        }
                                    }
                                    //FMBA-费用申请-准入（按费用行详情“支付对象”-“支付给个人”带出对私金额）
                                    if (formCode.equals("ZRFY01")) {
                                        cn.hutool.json.JSONArray lineArray = pageInfoObject.getJSONArray("claim_lines");
                                        if (lineArray != null && lineArray.size() > 0) {
                                            for (int j = 0; j < lineArray.size(); j++) {
                                                cn.hutool.json.JSONObject lineObject = lineArray.getJSONObject(j);
                                                String pay_object = lineObject.getStr("pay_object");
                                                if (pay_object.equals("U")) {
                                                    Double total_amount = lineObject.getDouble("receipt_amount");
                                                    log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",费用行支付给个人金额为：" + total_amount);
                                                    if (total_amount != null) {
                                                        totalAmount += total_amount; // 累加到总和
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    //FMCA-会议申请-准入（按费用行详情“支付对象”-“支付给个人”带出对私金额）
                                    if (formCode.equals("ZRHYCRM")) {
                                        cn.hutool.json.JSONArray lineArray = pageInfoObject.getJSONArray("claim_lines");
                                        if (lineArray != null && lineArray.size() > 0) {
                                            for (int j = 0; j < lineArray.size(); j++) {
                                                cn.hutool.json.JSONObject lineObject = lineArray.getJSONObject(j);
                                                String pay_object = lineObject.getStr("pay_object");
                                                if (pay_object.equals("U")) {
                                                    Double total_amount = lineObject.getDouble("receipt_amount");
                                                    log.info("费用报销总金额单据关联第:" + i + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",费用行支付给个人金额为：" + total_amount);
                                                    if (total_amount != null) {
                                                        totalAmount += total_amount; // 累加到总和
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    log.info("费用报销总金额单据单号为：" + number + ",总金额累计：" + totalAmount);
                    //将totalAmount返参到单据column29字段里面
                    JSONObject json = new JSONObject();
                    json.put("id", claim.getId());
                    json.put("header_id", claim.getId());
                    json.put("column29", totalAmount);
                    //String jsonString = JSONObject.toJSONString(json);
                    // ServiceRes serviceRes = yjClaimService.updateClaimHeader(claim);
                    log.info("费用报销总金额单据单号为：" + number + ",Json：" + json);
                    ServiceRes res = yjApiService.api(updateClaimUrl, HttpMethod.POST, JSONObject.toJSONString(json), null);
                    log.info("费用报销总金额单据单号为：" + number + ",Json：" + json + ",云简接口返回结果：" + res);
                }
            }
        }
        return response;
    }

    @Override
    public Response sharedDocument(String param) throws JsonProcessingException {
        Response response = new Response();
        response.setResCode(200000);
        response.setResMsg("成功");
        // 获取当前时间的时间戳（毫秒）
        LocalDateTime now = LocalDateTime.now();
        long currentTimestamp = now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        // 获取当前时间的昨天0时的时间戳（毫秒）
        LocalDateTime yesterdayMidnight = now.minusDays(1);
        long yesterdayMidnightTimestamp = yesterdayMidnight.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        List<String> list1 = new ArrayList<>();
        //CRM会议申请
        list1.add("YXHYCRM");
        //CRM会议申请-准入
        list1.add("ZRHYCRM");

        List<String> list2 = new ArrayList<>();
        //审批通过
        list2.add("approved");

        Map map = new HashMap();
        map.put("page_size", "100");
        map.put("page_num", "1");
        map.put("start_datetime", yesterdayMidnightTimestamp);
        map.put("end_datetime", currentTimestamp);
        //approved_date  审批通过时间
        map.put("date_field_name", "approved_date");
        map.put("type_codes", list1);
        map.put("statuses", list2);

        ServiceRes api = yjApiService.api(batchQueryUrl, HttpMethod.POST, JSONObject.toJSONString(map), null);

        String code = api.getCode();
        if ("500000".equals(code) || "000000".equals(code)) {
            throw new RuntimeException(api.getMsg());
        }

        // 将data字段反序列化为JSONObject
        cn.hutool.json.JSONObject dataObject = (cn.hutool.json.JSONObject) api.getData();
        if (dataObject.size() > 0) {
            Integer totalPage = dataObject.getInt("total_page");
            List<PageInfoData> list = new ArrayList<>();
            if (totalPage > 0) {
                for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
                    Map mapNew = new HashMap();
                    mapNew.put("page_size", "100");
                    mapNew.put("page_num", pageNum);
                    mapNew.put("start_datetime", yesterdayMidnightTimestamp);
                    mapNew.put("end_datetime", currentTimestamp);
                    //approved_date  审批通过时间
                    mapNew.put("date_field_name", "approved_date");
                    mapNew.put("type_codes", list1);
                    mapNew.put("statuses", list2);
                    //查询单据
                    ServiceRes apiNew = yjApiService.api(batchQueryUrl, HttpMethod.POST, JSONObject.toJSONString(map), null);
                    String codeNew = apiNew.getCode();
                    if ("500000".equals(codeNew) || "000000".equals(codeNew)) {
                        throw new RuntimeException(apiNew.getMsg());
                    }
                    // 将data字段反序列化为JSONObject
                    cn.hutool.json.JSONObject info = (cn.hutool.json.JSONObject) api.getData();
                    cn.hutool.json.JSONArray dataArray = info.getJSONArray("page_info");
                    // 将当前页的数据添加到列表中
                    for (int i = 0; i < dataArray.size(); i++) {
                        cn.hutool.json.JSONObject jsonObject = dataArray.getJSONObject(i);
                        PageInfoData data = new PageInfoData();
                        if (jsonObject.get("column_json").toString() != null) {
                            // 根据你的Data类字段进行赋值
                            data.setColumn_json(jsonObject.get("column_json").toString());
                            data.setOddNumber(jsonObject.get("code").toString());
                            list.add(data);
                        }
                    }
                }
            }

            //获取token
//            String access_token = cloudpenseApiService.getToken();

            for (PageInfoData p : list) {
                String oddNumber = p.getOddNumber();
                cn.hutool.json.JSONObject jsonObject = new cn.hutool.json.JSONObject(p.getColumn_json());
                String column121 = jsonObject.getStr("column121");
                if (column121 != null) {
                    String[] values = column121.split(",");
                    List<String> listData = new ArrayList<>();
                    for (String value : values) {
                        listData.add(value.trim()); // 添加到列表中，并去除可能的前后空格
                    }
                    if (listData.size() > 0) {
                        for (String jobNumber : listData) {
                           /* String bizId = UUID.randomUUID().toString();
                            long timestamp = System.currentTimeMillis();
                            //查询值列表 离职人员清单
                            String url = employeeInfoUrl + "?code=" + jobNumber + "&bizId=" + bizId + "&timestamp=" + timestamp + "&access_token=" + access_token;
                            String result = HttpUtil.sendGet(url);
                            // 解析JSON字符串为JSONObject
                            JSONObject root = JSONObject.parseObject(result);
                            Integer code1 = root.getInteger("resCode");
                            if ("500000".equals(code1) || "000000".equals(code1)) {
                                throw new RuntimeException(root.getString("resMsg"));
                            }*/
                            String jobNum = jobNumber;
                            if (jobNum != null) {
                                String url1 = documentSharingAddUrl + "?document_num=" + oddNumber;
                                SharedDocumentData sharedDocumentData = new SharedDocumentData();
                                sharedDocumentData.setCode(jobNum);
                                sharedDocumentData.setSequence_num("");
                                List<SharedDocumentData> dataList = new ArrayList<>();
                                dataList.add(sharedDocumentData);
                                SharedDocumentDataVo dataVo = new SharedDocumentDataVo();
                                dataVo.setBizId(UUID.randomUUID().toString());
                                dataVo.setTimestamp(System.currentTimeMillis());
                                dataVo.setData(dataList);
                                //共享单据给协办人
                                ServiceRes serviceRes = yjApiService.api(url1, HttpMethod.POST, JSONObject.toJSONString(dataVo), null);
                            }
                        }
                    }
                }
            }
        }
        return response;
    }

    @Override
    public ValResultCommonVo projectTotalAmount(ExpClaimHeaderCommonVo vo) {
        //获取token
//        String access_token = cloudpenseApiService.getToken();
        ValResultCommonVo<Integer> result = new ValResultCommonVo<>();
        Map<Integer, String> message = new HashMap<>();
        result.setValue(0);
        result.setResult(0);
        result.setMessage(message);

        String FKnumber = vo.getCode();

        //系列会（平台）项目号
        if (vo.getColumn45() != null && vo.getColumn45() != "") {
            String project = vo.getColumn45();
            JSONObject projectObj = vo.getColumn45Obj();
            //项目号总金额
            BigDecimal projectTotalAmount = projectObj.getBigDecimal("column4");
            if (projectTotalAmount == null) {
                projectTotalAmount = BigDecimal.ZERO;
            }
            //项目号类别
            String projectNumberCategory = projectObj.getString("column3");
            //对公预付   YXDG01  type_id:154314
            //对公应付   YXDG02  type_id:154318
            //实际领药申请 YXIIT02 type_id:155923
            //会议核报  YXHY02  type_id:155924
            //ZRHY02-会议核报-准入  ZRHY02  type_id:155934
            List<String> listTypeID = new ArrayList<>();
            switch (projectNumberCategory) {
                //IIT
                case "01":
                    listTypeID.add("154314");
                    listTypeID.add("154318");
                    listTypeID.add("155923");
                    break;
                //系列会编号
                case "02":
                    listTypeID.add("155924");
                    listTypeID.add("154314");
                    listTypeID.add("155934");
                    break;
                //平台编号
                case "03":
                    listTypeID.add("154314");
                    listTypeID.add("154318");
                    break;
            }
            List<String> listStatus = new ArrayList<>();
            //已提交
            listStatus.add("submitted");
            //审批中
            listStatus.add("approving");
            //审批通过
            listStatus.add("approved");
            //已完成
            listStatus.add("closed");
            //业务审批通过
            listStatus.add("preapproved");
            //财务审批通过
            listStatus.add("checked");
            //打回修改
            listStatus.add("modifying");
            //未提交
            listStatus.add("incomplete");

            List<cn.hutool.json.JSONObject> objectList = getProjectDocumentInfo(project, null, listTypeID, listStatus);

            BigDecimal totalAmount = new BigDecimal(0);
            log.info("系列会（平台）项目号：" + project + "--查询单据有：" + objectList.size() + "条");
            // 查询项目号历史单据总金额
            if (objectList != null && objectList.size() > 0) {
                for (int i = 0; i < objectList.size(); i++) {
                    cn.hutool.json.JSONObject jsonObject = objectList.get(i);
                    cn.hutool.json.JSONObject headerType = jsonObject.getJSONObject("header_type");
                    String code = headerType.getStr("code");
                    String number = jsonObject.getStr("code");
                    String status = jsonObject.getStr("status");
                    BigDecimal total_amount = new BigDecimal(0);
                    log.info("第" + i + "条--total_pay_amount金额为：" + jsonObject.getBigDecimal("total_pay_amount"));
                    log.info("第" + i + "条--advance_amount金额为：" + jsonObject.getBigDecimal("advance_amount"));
                    log.info("第" + i + "条--total_amount金额为：" + jsonObject.getBigDecimal("total_amount"));
                    log.info("第" + i + "条--报销单据单号为：" + number);
                    log.info("第" + i + "条--报销单据单状态为：" + status);
                    if (!status.equals("incomplete") || FKnumber.equals(number)) {
                        switch (code) {
                            //对公预付   YXDG01  type_id:154314
                            case "YXDG01":
                                total_amount = jsonObject.getBigDecimal("advance_amount");
                                break;
                            //对公应付   YXDG02  type_id:154318
                            case "YXDG02":
                                total_amount = jsonObject.getBigDecimal("total_pay_amount");
                                break;
                            //实际领药申请 YXIIT02 type_id:155923
                            case "YXIIT02":
                                total_amount = jsonObject.getBigDecimal("total_amount");
                                break;
                            //会议核报  YXHY02  type_id:155924
                            case "YXHY02":
                                total_amount = jsonObject.getBigDecimal("total_pay_amount");
                                break;
                            //ZRHY02-会议核报-准入  ZRHY02  type_id:155934
                            case "ZRHY02":
                                total_amount = jsonObject.getBigDecimal("total_pay_amount");
                                break;
                        }
                        if (total_amount != null) {
                            totalAmount = totalAmount.add(total_amount);
                        }
                    }
                }
            }
            //系列会（平台）项目号 > 项目号总金额
            if (totalAmount.compareTo(projectTotalAmount) > 0) {
                result.setResult(1);
                result.setValue(1);
                return result;
            }
        }
        //IIT项目号
        if (vo.getColumn46() != null && vo.getColumn46() != "") {
            String iitProject = vo.getColumn46();
            JSONObject projectObj = vo.getColumn46Obj();
            //项目号总金额
            BigDecimal projectTotalAmount = projectObj.getBigDecimal("column4");
            if (projectTotalAmount == null) {
                projectTotalAmount = BigDecimal.ZERO;
            }
            //项目号类别
            String projectNumberCategory = projectObj.getString("column3");
            //对公预付   YXDG01  type_id:154314
            //对公应付   YXDG02  type_id:154318
            //实际领药申请 YXIIT02 type_id:155923
            //会议核报  YXHY02  type_id:155924
            //ZRHY02-会议核报-准入  ZRHY02  type_id:155934
            List<String> listTypeID = new ArrayList<>();
            switch (projectNumberCategory) {
                case "01":
                    listTypeID.add("154314");
                    listTypeID.add("154318");
                    listTypeID.add("155923");
                    break;
                case "02":
                    listTypeID.add("155924");
                    listTypeID.add("154314");
                    listTypeID.add("155934");
                    break;
                case "03":
                    listTypeID.add("154314");
                    listTypeID.add("154318");
                    break;
            }

            List<String> listStatus = new ArrayList<>();
            //已提交
            listStatus.add("submitted");
            //审批中
            listStatus.add("approving");
            //审批通过
            listStatus.add("approved");
            //已完成
            listStatus.add("closed");
            //业务审批通过
            listStatus.add("preapproved");
            //财务审批通过
            listStatus.add("checked");
            //打回修改
            listStatus.add("modifying");
            //未提交
            listStatus.add("incomplete");

            List<cn.hutool.json.JSONObject> objectList = getProjectDocumentInfo(null, iitProject, listTypeID, listStatus);

            log.info("IIT项目号：" + iitProject + "--查询单据有：" + objectList.size() + "条");
            BigDecimal totalAmount = new BigDecimal(0);
            // 查询IIT项目号历史单据总金额
            if (objectList != null && objectList.size() > 0) {
                for (int i = 0; i < objectList.size(); i++) {
                    cn.hutool.json.JSONObject jsonObject = objectList.get(i);
                    cn.hutool.json.JSONObject headerType = jsonObject.getJSONObject("header_type");
                    String code = headerType.getStr("code");
                    String number = jsonObject.getStr("code");
                    String status = jsonObject.getStr("status");
                    BigDecimal total_amount = new BigDecimal(0);
                    log.info("第" + i + "条--total_pay_amount金额为：" + jsonObject.getBigDecimal("total_pay_amount"));
                    log.info("第" + i + "条--advance_amount金额为：" + jsonObject.getBigDecimal("advance_amount"));
                    log.info("第" + i + "条--total_amount金额为：" + jsonObject.getBigDecimal("total_amount"));
                    log.info("第" + i + "条--报销单据单号为：" + number);
                    log.info("第" + i + "条--报销单据单状态为：" + status);
                    if (!status.equals("incomplete") || FKnumber.equals(number)) {
                        switch (code) {
                            //对公预付   YXDG01  type_id:154314
                            case "YXDG01":
                                total_amount = jsonObject.getBigDecimal("advance_amount");
                                break;
                            //对公应付   YXDG02  type_id:154318
                            case "YXDG02":
                                total_amount = jsonObject.getBigDecimal("total_pay_amount");
                                break;
                            //实际领药申请 YXIIT02 type_id:155923
                            case "YXIIT02":
                                total_amount = jsonObject.getBigDecimal("total_amount");
                                break;
                            //会议核报  YXHY02  type_id:155924
                            case "YXHY02":
                                total_amount = jsonObject.getBigDecimal("total_pay_amount");
                                break;
                            //ZRHY02-会议核报-准入  ZRHY02  type_id:155934
                            case "ZRHY02":
                                total_amount = jsonObject.getBigDecimal("total_pay_amount");
                                break;
                        }
                        if (total_amount != null) {
                            totalAmount = totalAmount.add(total_amount); // 累加到总和
                        }
                    }
                }
            }
            //IIT项目号 申请单总金额 > 项目号总金额
            if (totalAmount.compareTo(projectTotalAmount) > 0) {
                result.setResult(1);
                result.setValue(1);
                return result;
            }
        }
        return result;
    }

    @Override
    public ValResultCommonVo jdPurchaseRequisition(ExpClaimHeaderCommonVo vo) {
        ValResultCommonVo<Integer> result = new ValResultCommonVo<>();
        Map<Integer, String> message = new HashMap<>();
        result.setValue(0);
        result.setResult(0);
        result.setMessage(message);

        List<ExpClaimHeaderCommonVo> headerLinks = vo.getHeaderLinks();

        List<String> listStatus = new ArrayList<>();
        //已提交
        listStatus.add("submitted");
        //审批中
        listStatus.add("approving");
        //审批通过
        listStatus.add("approved");
        //已完成
        listStatus.add("closed");
        //业务审批通过
        listStatus.add("preapproved");
        //财务审批通过
        listStatus.add("checked");
        //打回修改
        listStatus.add("modifying");


        for (int i = 0; i < headerLinks.size(); i++) {
            //关联申请单据单号
            String oddNumbers = headerLinks.get(i).getCode();
            //关联申请单据费用行
            List<ExpClaimLineCommonVo> lineVoList = headerLinks.get(i).getExpClaimLineVoList();
            //关联申请单据费用行（K003）总金额
            BigDecimal totalAmountNew = new BigDecimal(0);
            if (lineVoList.size() > 0) {
                for (int j = 0; j < lineVoList.size(); j++) {
                    SimpleExpTypeVo simpleExpTypeVo = lineVoList.get(j).getExpTypeVo();
                    if (simpleExpTypeVo.getTypeCode().equals("K003")) {
                        BigDecimal finReceiptAmount = lineVoList.get(j).getFinReceiptAmount();
                        if (finReceiptAmount != null) {
                            totalAmountNew = totalAmountNew.add(finReceiptAmount);
                        }
                    }
                }
            }
            BigDecimal totalAmount = new BigDecimal(0);
            if (oddNumbers != null) {
                List<cn.hutool.json.JSONObject> objectList = getRelatedDocumentInfo1(oddNumbers, listStatus);
                //根据申请单查询出的关联单据条数信息
                cn.hutool.json.JSONArray jsonArray = objectList.get(0).getJSONArray("header_back_links");
                if (jsonArray.size() > 0) {
                    for (int j = 0; j < jsonArray.size(); j++) {
                        cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(j);
                        BigDecimal total_amount = jsonObject.getBigDecimal("total_amount");
                        if (total_amount != null) {
                            totalAmount = totalAmount.add(total_amount);
                        }
                    }
                }
            }
            if (totalAmount.compareTo(totalAmountNew) > 0) {
                result.setValue(1);
                result.setResult(1);
                return result;
            }
        }
        return result;
    }

    @Override
    public ValResultCommonVo medicationQuantity(ExpClaimHeaderCommonVo vo) {
        //获取token
//        String access_token = cloudpenseApiService.getToken();
        ValResultCommonVo<Integer> result = new ValResultCommonVo<>();
        Map<Integer, String> message = new HashMap<>();
        result.setValue(0);
        result.setResult(0);
        result.setMessage(message);

        List<ExpClaimHeaderCommonVo> headerLinks = vo.getHeaderLinks();

        List<String> listStatus = new ArrayList<>();
        //已提交
        listStatus.add("submitted");
        //审批中
        listStatus.add("approving");
        //审批通过
        listStatus.add("approved");
        //已完成
        listStatus.add("closed");
        //业务审批通过
        listStatus.add("preapproved");
        //财务审批通过
        listStatus.add("checked");
        //打回修改
        listStatus.add("modifying");

        List<String> drugNameList = new ArrayList();
        drugNameList.add("依沃西单抗注射液");
        drugNameList.add("卡度尼利单抗注射液");
        drugNameList.add("AK117");

        Map<String, BigDecimal> map = new HashMap<>();

        String FKnumber = vo.getCode();
        for (int i = 0; i < headerLinks.size(); i++) {
            //关联申请单据单号
            String oddNumbers = headerLinks.get(i).getCode();
            //关联申请单据费用行
            List<ExpClaimLineCommonVo> lineVoList = headerLinks.get(i).getExpClaimLineVoList();
            //关联申请单据费用行 总数量
            BigDecimal totalNumberNew = new BigDecimal(0);
            //依沃西单抗注射液 申请 总数量
            BigDecimal a = new BigDecimal(0);
            //卡度尼利单抗注射液 申请 总数量
            BigDecimal b = new BigDecimal(0);
            //AK117 申请 总数量
            BigDecimal c = new BigDecimal(0);
            if (lineVoList.size() > 0) {
                for (int j = 0; j < lineVoList.size(); j++) {
                    String column100 = lineVoList.get(j).getColumn100();
                    BigDecimal number = lineVoList.get(j).getQuantity();
                    if (number != null) {
                        //依沃西单抗注射液
                        if (column100.equals("1")) {
                            a = a.add(number);
                        }
                        //卡度尼利单抗注射液
                        if (column100.equals("2")) {
                            b = b.add(number);
                        }
                        //AK117
                        if (column100.equals("3")) {
                            c = c.add(number);
                        }
                    }
                   /* if (number != null) {
                        totalNumberNew = totalNumberNew.add(number);
                    }*/
                }
            }
            //当前单据已用 总数量
            BigDecimal totalNumber = new BigDecimal(0);
            //依沃西单抗注射液 已用 总数量
            BigDecimal aNew = new BigDecimal(0);
            //卡度尼利单抗注射液 已用 总数量
            BigDecimal bNew = new BigDecimal(0);
            //AK117 已用 总数量
            BigDecimal cNew = new BigDecimal(0);

            if (oddNumbers != null) {
                List<cn.hutool.json.JSONObject> objectList = getRelatedDocumentInfo2(oddNumbers, listStatus);
                //根据申请单查询出的关联单据条数信息
                cn.hutool.json.JSONArray jsonArray = objectList.get(0).getJSONArray("header_back_links");
                if (jsonArray.size() > 0) {
                    for (int j = 0; j < jsonArray.size(); j++) {
                        cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(j);
                        String status = jsonObject.getStr("status");
                        if (listStatus.contains(status) || FKnumber.equals(jsonObject.getStr("code"))) {
                            cn.hutool.json.JSONArray array = jsonObject.getJSONArray("claim_lines");
                            if (array.size() > 0) {
                                for (int k = 0; k < array.size(); k++) {
                                    String columnJson = array.getJSONObject(k).getStr("column_json");
                                    cn.hutool.json.JSONObject object = new cn.hutool.json.JSONObject(columnJson);
                                    String column100 = object.getStr("column100");
                                    BigDecimal quantity = object.getBigDecimal("column191");
                                    if (quantity != null) {
                                        //依沃西单抗注射液
                                        if (column100.equals("1")) {
                                            aNew = aNew.add(quantity);
                                        }
                                        //卡度尼利单抗注射液
                                        if (column100.equals("2")) {
                                            bNew = bNew.add(quantity);
                                        }
                                        //AK117
                                        if (column100.equals("3")) {
                                            cNew = cNew.add(quantity);
                                        }
                                    }
                                    /*if (quantity != null) {
                                        totalNumber = totalNumber.add(quantity);
                                    }*/
                                }
                            }
                        }
                    }
                }
            }
            if (aNew.compareTo(a) > 0) {
                Integer headerId = vo.getHeaderId();
                BigDecimal amountMsg = aNew.subtract(a);
                String excessMsg = "依沃西单抗注射液申请已超标，超标数量为：" + amountMsg;
                Map<Integer, JSONObject> templateParam = new HashMap<>();
                result.setValue(1);
                result.setResult(1);
                JSONObject param = new JSONObject();
                param.put("line_id", headerId);
                param.put("ExcessMsg", excessMsg);
                templateParam.put(headerId, param);
                result.setTemplateParam(templateParam);
                return result;
            }
            if (bNew.compareTo(b) > 0) {
                Integer headerId = vo.getHeaderId();
                BigDecimal amountMsg = bNew.subtract(b);
                String excessMsg = "卡度尼利单抗注射液申请已超标，超标数量为：" + amountMsg;
                Map<Integer, JSONObject> templateParam = new HashMap<>();
                result.setValue(1);
                result.setResult(1);
                JSONObject param = new JSONObject();
                param.put("line_id", headerId);
                param.put("ExcessMsg", excessMsg);
                templateParam.put(headerId, param);
                result.setTemplateParam(templateParam);
                return result;
            }
            if (cNew.compareTo(c) > 0) {
                Integer headerId = vo.getHeaderId();
                BigDecimal amountMsg = cNew.subtract(c);
                String excessMsg = "AK117申请已超标，超标数量为：" + amountMsg;
                Map<Integer, JSONObject> templateParam = new HashMap<>();
                result.setValue(1);
                result.setResult(1);
                JSONObject param = new JSONObject();
                param.put("line_id", headerId);
                param.put("ExcessMsg", excessMsg);
                templateParam.put(headerId, param);
                result.setTemplateParam(templateParam);
                return result;
            }
        }
        return result;
    }

    @Override
    public ValResultCommonVo platformExecutionPlanTotalAmount(ExpClaimHeaderCommonVo vo) {
        ValResultCommonVo<Integer> result = new ValResultCommonVo<>();
        Map<Integer, String> message = new HashMap<>();
        result.setValue(0);
        result.setResult(0);
        result.setMessage(message);

        List<ExpClaimHeaderCommonVo> headerLinks = vo.getHeaderLinks();

        List<String> listStatus = new ArrayList<>();
        //已提交
        listStatus.add("submitted");
        //审批中
        listStatus.add("approving");
        //审批通过
        listStatus.add("approved");
        //已完成
        listStatus.add("closed");
        //业务审批通过
        listStatus.add("preapproved");
        //财务审批通过
        listStatus.add("checked");
        //打回修改
        listStatus.add("modifying");

        String FKnumber = vo.getCode();

        for (int i = 0; i < headerLinks.size(); i++) {
            //关联申请单据单号
            String oddNumbers = headerLinks.get(i).getCode();
            //关联申请单据总金额
            BigDecimal totalAmount = headerLinks.get(i).getTotalAmount();

            //历史单据已关联 总金额
            BigDecimal totalAmountNew = new BigDecimal(0);
            if (oddNumbers != null) {
                List<cn.hutool.json.JSONObject> objectList = getRelatedDocumentInfo2(oddNumbers, listStatus);
                //根据申请单查询出的关联单据条数信息
                cn.hutool.json.JSONArray jsonArray = objectList.get(0).getJSONArray("header_back_links");
                if (jsonArray.size() > 0) {
                    for (int j = 0; j < jsonArray.size(); j++) {
                        cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(j);
                        String status = jsonObject.getStr("status");
                        String code = jsonObject.getStr("code");
                        if (listStatus.contains(status) || FKnumber.equals(jsonObject.getStr("code"))) {
                            BigDecimal total_amount = jsonObject.getBigDecimal("total_amount");
                            if (total_amount != null) {
                                totalAmountNew = totalAmountNew.add(total_amount);
                            }
                        }
                    }
                }
            }
            if (totalAmountNew.compareTo(totalAmount) > 0) {
                result.setValue(1);
                result.setResult(1);
                return result;
            }
        }
        return result;
    }

    @Override
    public Response sharedDocumentToProjectApplicant(String param) throws JsonProcessingException {
        Response response = new Response();
        response.setResCode(200000);
        response.setResMsg("成功");
        JSONObject jsonObject = JSONObject.parseObject(param);
        ExpClaimHeaderCommonVo vo = null;
        if (jsonObject.get("claimVo") != null) {
            vo = JSONUtil.toBean(jsonObject.getString("claimVo"), ExpClaimHeaderCommonVo.class);
           /* UserApiVo submitUserDtoNew = vo.getSubmitUserVo();
            String jobNumberNew = submitUserDtoNew.getEmployeeNumber();*/

            List<ExpClaimHeaderCommonVo> headerLinks = vo.getHeaderLinks();
            //平台执行计划单号
            String FKnumber = vo.getCode();
            for (int i = 0; i < headerLinks.size(); i++) {
                //关联申请单据单号
                String oddNumbers = headerLinks.get(i).getCode();
                UserApiVo submitUserDto = headerLinks.get(0).getSubmitUserVo();
                String jobNumber = submitUserDto.getEmployeeNumber();
                /*  if (!jobNumberNew.equals(jobNumber)) {*/
                String url = documentSharingAddUrl + "?document_num=" + FKnumber;
                SharedDocumentData sharedDocumentData = new SharedDocumentData();
                sharedDocumentData.setCode(jobNumber);
                sharedDocumentData.setSequence_num("");
                List<SharedDocumentData> dataList = new ArrayList<>();
                dataList.add(sharedDocumentData);
                SharedDocumentDataVo dataVo = new SharedDocumentDataVo();
                dataVo.setBizId(UUID.randomUUID().toString());
                dataVo.setTimestamp(System.currentTimeMillis());
                dataVo.setData(dataList);
                //共享单据给协办人
                ServiceRes serviceRes = yjApiService.api(url, HttpMethod.POST, JSONObject.toJSONString(dataVo), null);
            }
        }
        return response;
    }

    @Override
    public Response communicationFeeTotalAmount(String param) throws JsonProcessingException {
        Response response = new Response();
        response.setResCode(200000);
        response.setResMsg("成功");
        JSONObject jsonObject = JSONObject.parseObject(param);
        ExpClaimHeaderCommonVo claim = null;
        if (jsonObject.get("claimVo") != null) {
            claim = JSONUtil.toBean(jsonObject.getString("claimVo"), ExpClaimHeaderCommonVo.class);
            // 获取claimVo对象
            JSONObject claimVo = jsonObject.getJSONObject("claimVo");

            String number = claimVo.getString("code");
            log.info("个人月度对私交际费单据单号为：" + number);

            //获取单据预算期间
            JSONObject glPeriodVo = claimVo.getJSONObject("glPeriodVo");
            String budgetPeriod = glPeriodVo.getString("code");

            //获取提交日期时间
            long submitDate = claimVo.getLong("submitDate");

            //long submitDate = 1743407668201L;

            // 1. 将时间戳转换为Instant
            Instant instant = Instant.ofEpochMilli(submitDate);

            // 2. 转换为带时区的日期时间（使用上海时区）
            ZonedDateTime originalDateTime = instant.atZone(ZoneId.of("Asia/Shanghai"));

            // 3. 获取当月的第一天（月初）00:00:00
            ZonedDateTime firstDayOfMonth = originalDateTime
                    .with(TemporalAdjusters.firstDayOfMonth())
                    .with(LocalTime.MIN);

            // 4. 获取当月的最后一天（月末）23:59:59.999
            ZonedDateTime lastDayOfMonth = originalDateTime
                    .with(TemporalAdjusters.lastDayOfMonth())
                    .with(LocalTime.MAX);

            // 5. 基于月初计算上6个月
            ZonedDateTime sixMonthsBefore = firstDayOfMonth.minusMonths(6);

            // 6. 基于月初计算下6个月
            ZonedDateTime sixMonthsAfter = firstDayOfMonth.plusMonths(6);

            // 转换为时间戳（毫秒）
            long firstDayMillis = firstDayOfMonth.toInstant().toEpochMilli();
            long lastDayMillis = lastDayOfMonth.toInstant().toEpochMilli();

            //时间段
            long beforeMillis = sixMonthsBefore.toInstant().toEpochMilli();
            long afterMillis = sixMonthsAfter.toInstant().toEpochMilli();


            // 获取submitUserVo对象
            JSONObject submitUserVo = claimVo.getJSONObject("submitUserVo");
            // 获取提交人工号
            String jobNumber = submitUserVo.getString("employeeNumber");


            ExpHeaderTypeVo vo = claim.getHeaderTypeVo();
            String voCode = vo.getCode();

            List<String> list1 = new ArrayList<>();

            //FSBA-费用申请 -YXFY01,FMBA-费用申请-准入-ZRFY01
            if (voCode.equals("YXFY01") || voCode.equals("ZRFY01")) {
                list1.add("YXFY01");
                list1.add("ZRFY01");
            }
            //FSBB-费用报销 -YXFY02
            if (voCode.equals("YXFY02")) {
                list1.add("YXFY02");
            }

            //FMBB-费用报销-准入-ZRFY02
            if (voCode.equals("ZRFY02")) {
                list1.add("ZRFY02");
            }
            log.info("个人月度对私交际费单据代码集合:" + list1);

            List<String> list2 = new ArrayList<>();
            //已提交
            list2.add("submitted");
            //审批中
            list2.add("approving");
            //审批通过
            list2.add("approved");
            //已完成
            list2.add("closed");
            //业务审批通过
            list2.add("preapproved");
            //财务审批通过
            list2.add("checked");
            //打回修改
            list2.add("modifying");

            List<String> list3 = new ArrayList<>();
            list3.add(jobNumber);

            Map map = new HashMap();
            map.put("page_size", "100");
            map.put("page_num", "1");
            map.put("start_datetime", beforeMillis);
            map.put("end_datetime", afterMillis);
            //submit_date  提交时间
            map.put("date_field_name", "submit_date");
            map.put("type_codes", list1);
            map.put("statuses", list2);
            map.put("charge_user_codes", list3);

            ServiceRes api = yjApiService.api(batchQueryUrl, HttpMethod.POST, JSONObject.toJSONString(map), null);
            String code = api.getCode();
            if ("500000".equals(code) || "000000".equals(code)) {
                throw new RuntimeException(api.getMsg());
            }
            // 将data字段反序列化为JSONObject
            cn.hutool.json.JSONObject dataObject = (cn.hutool.json.JSONObject) api.getData();
            double totalAmount = 0;
            if (dataObject.size() > 0) {
                Integer totalPage = dataObject.getInt("total_page");
                if (totalPage > 0) {
                    for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
                        Map mapNew = new HashMap();
                        mapNew.put("page_size", "100");
                        mapNew.put("page_num", pageNum);
                        mapNew.put("start_datetime", beforeMillis);
                        mapNew.put("end_datetime", afterMillis);
                        //submit_date  提交时间
                        mapNew.put("date_field_name", "submit_date");
                        mapNew.put("type_codes", list1);
                        mapNew.put("statuses", list2);
                        mapNew.put("charge_user_codes", list3);

                        ServiceRes apiNew = yjApiService.api(batchQueryUrl, HttpMethod.POST, JSONObject.toJSONString(mapNew), null);
                        String codeNew = api.getCode();
                        if ("500000".equals(codeNew) || "000000".equals(codeNew)) {
                            throw new RuntimeException(apiNew.getMsg());
                        }
                        // 将data字段反序列化为JSONObject
                        cn.hutool.json.JSONObject info = (cn.hutool.json.JSONObject) api.getData();
                        cn.hutool.json.JSONArray pageInfoArray = info.getJSONArray("page_info");
                        log.info("个人月度对私交际费关联条数:" + pageInfoArray.size() + "---单号：" + number);
                        // 判断pageInfoArray的条数是否大于0
                        if (pageInfoArray != null && pageInfoArray.size() > 0) {
                            for (int i = 0; i < pageInfoArray.size(); i++) {
                                cn.hutool.json.JSONObject pageInfoObject = pageInfoArray.getJSONObject(i);
                                cn.hutool.json.JSONObject object = pageInfoObject.getJSONObject("column17_obj");
                                //表单创建来源
                                String from = pageInfoObject.getStr("column140");
                                //获取费用类型
                                String feeType = object.getStr("value_meaning");
                                //当单据状态为 已完成
                                String statusNew = pageInfoObject.getStr("status");
                                //获取预算期间
                                cn.hutool.json.JSONObject glPeriodVoNew = pageInfoObject.getJSONObject("gl_period_vo");
                                String budgetPeriodNew = glPeriodVoNew.getStr("code");
                                //单据编码
                                cn.hutool.json.JSONObject objectCode = pageInfoObject.getJSONObject("header_type");
                                String formCode = objectCode.getStr("code");
                                //单号
                                String danhao = pageInfoObject.getStr("code");

                                log.info("个人月度对私交际费单据关联第:" + (i + 1)
                                        + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",费用类型为:" + feeType + ",状态为" + statusNew + ",表单创建来源为:" + from);

                                if (feeType.equals("外部交际") && budgetPeriod.equals(budgetPeriodNew)) {
                                    if (statusNew.equals("closed") && from.equals("手工创建")) {
                                        cn.hutool.json.JSONArray headerBackLink = pageInfoObject.getJSONArray("header_back_links");
                                        if (headerBackLink != null) {
                                            boolean isTrue = false;
                                            for (int j = 0; j < headerBackLink.size(); j++) {
                                                //关联后序单据状态  查询有效单据状态
                                                String status = headerBackLink.getJSONObject(j).getStr("status");
                                                if (status.equals("submitted") || status.equals("approving") || status.equals("approved") || status.equals("preapproved") || status.equals("checked") || status.equals("modifying")) {
                                                    isTrue = true;
                                                }
                                            }
                                            if (isTrue) {
                                                // 获取total_amount字段的值
                                                Double total_amount = pageInfoObject.getDouble("total_amount"); // 使用getDoubleValue获取double类型
                                                log.info("个人月度对私交际费单据关联第:" + (i + 1) + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",费用类型为:" + feeType + ",状态为" + statusNew + ",表单创建来源为:" + from + ",金额为" + total_amount);
                                                if (total_amount != null) {
                                                    totalAmount += total_amount; // 累加到总和
                                                }
                                            }
                                        }
                                    } else {
                                        // 获取total_amount字段的值
                                        Double total_amount = pageInfoObject.getDouble("total_amount"); // 使用getDoubleValue获取double类型
                                        log.info("个人月度对私交际费单据关联第:" + (i + 1) + "条单号为：" + danhao + ",编码为：" + formCode + ",预算期间为：" + budgetPeriodNew + ",费用类型为:" + feeType + ",状态为" + statusNew + ",表单创建来源为:" + from + ",金额为" + total_amount);
                                        if (total_amount != null) {
                                            totalAmount += total_amount; // 累加到总和
                                        }
                                    }
                                }
                            }
                        }
                    }
                    log.info("个人月度对私交际费单据单号为：" + number + ",总金额合计为：" + totalAmount);
                    BigDecimal bd = new BigDecimal(totalAmount).setScale(2, RoundingMode.HALF_UP);
                    log.info("总金额合计转换后为：" + bd.doubleValue());
                    cn.hutool.json.JSONObject jsonNew = new cn.hutool.json.JSONObject();


                    if (claim.getColumnJson() != null)
                        jsonNew = JSONUtil.parseObj(claim.getColumnJson());


                    jsonNew.set("column200", bd.toString());

                    //将totalAmount返参到单据column200字段里面
                    JSONObject json = new JSONObject();
                    json.put("id", claim.getId());
                    json.put("header_id", claim.getId());
                    json.put("column_json", JSONObject.toJSONString(jsonNew));
                    //String jsonString = JSONObject.toJSONString(json);
                    //ServiceRes serviceRes = yjClaimService.updateClaimHeader(claim);
                    log.info("个人月度对私交际费单据单号为：" + number + ",Json：" + json);
                    ServiceRes res = yjApiService.api(updateClaimUrl, HttpMethod.POST, JSONObject.toJSONString(json), null);
                    log.info("个人月度对私交际费单据单号为：" + number + ",Json：" + json + ",云简接口返回结果：" + res);
                }
            }
        }
        return response;
    }

    @Override
    public ValResultCommonVo verifyCompanyName(ExpClaimHeaderCommonVo vo) {
        ValResultCommonVo<Integer> result = new ValResultCommonVo<>();
        Map<Integer, String> message = new HashMap<>();
        result.setValue(0);
        result.setResult(0);
        result.setMessage(message);

        DepartmentVo departmentVo = vo.getBranchVo();
        String companyNameCode = departmentVo.getDepartmentCode();

        List<ExpClaimHeaderCommonVo> headerLinks = vo.getHeaderLinks();

        for (int i = 0; i < headerLinks.size(); i++) {
            //关联申请单据单号
            String oddNumbers = headerLinks.get(i).getCode();
            DepartmentVo departmentVoNew = headerLinks.get(i).getBranchVo();
            String companyNameCodeNew = departmentVoNew.getDepartmentCode();
            if (!companyNameCode.equals(companyNameCodeNew)) {
                result.setValue(1);
                result.setResult(1);
                return result;
            }
        }

        return result;
    }

    @Override
    public ValResultCommonVo loanVerification(ExpClaimHeaderCommonVo vo) {
        ValResultCommonVo<Integer> result = new ValResultCommonVo<>();
        Map<Integer, String> message = new HashMap<>();
        result.setValue(0);
        result.setResult(0);
        result.setMessage(message);

        List<ExpClaimHeaderCommonVo> headerLinks = vo.getHeaderLinks();

        List<String> codeList = new ArrayList<>();

        ExpHeaderTypeVo typeVo = vo.getHeaderTypeVo();
        String code = typeVo.getCode();

        for (int i = 0; i < headerLinks.size(); i++) {
            //关联申请单据单号
            String oddNumbers = headerLinks.get(i).getCode();
            ExpHeaderTypeVo headerTypeVo = headerLinks.get(i).getHeaderTypeVo();
            //单据代码
            String documentCode = headerTypeVo.getCode();
            codeList.add(documentCode);
        }
        //YXJK01  员工借款申请
        if (codeList.contains("YXJK01")) {
            //员工借款申请前序单据
            String priorDocument1 = "a";
            //会议总结前序单据
            String priorDocument2 = "b";
            for (int i = 0; i < headerLinks.size(); i++) {
                if (code.equals("YXFY02") || code.equals("ZRFY02")) {
                    //关联申请单据单号
                    String oddNumbers = headerLinks.get(i).getCode();
                    ExpHeaderTypeVo headerTypeVo = headerLinks.get(i).getHeaderTypeVo();
                    //单据代码
                    String documentCode = headerTypeVo.getCode();
                    //员工借款申请
                    if (documentCode.equals("YXJK01")) {
                        List<cn.hutool.json.JSONObject> objectList = getRelatedDocumentInfo4(oddNumbers);
                        //根据申请单查询出的关联单据条数信息
                        cn.hutool.json.JSONArray jsonArray = objectList.get(0).getJSONArray("header_links");
                        if (jsonArray != null && jsonArray.size() > 0) {
                            cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(0);
                            priorDocument1 = jsonObject.getStr("code");
                        }
                    }
                    //ZRFY01-费用申请-准入  费用申请  YXFY01
                    if (documentCode.equals("ZRFY01") || documentCode.equals("YXFY01")) {
                        priorDocument2 = oddNumbers;
                    }
                } else {
                    //关联申请单据单号
                    String oddNumbers = headerLinks.get(i).getCode();
                    ExpHeaderTypeVo headerTypeVo = headerLinks.get(i).getHeaderTypeVo();
                    //单据代码
                    String documentCode = headerTypeVo.getCode();
                    //员工借款申请
                    if (documentCode.equals("YXJK01")) {
                        List<cn.hutool.json.JSONObject> objectList = getRelatedDocumentInfo4(oddNumbers);
                        //根据申请单查询出的关联单据条数信息
                        cn.hutool.json.JSONArray jsonArray = objectList.get(0).getJSONArray("header_links");
                        if (jsonArray != null && jsonArray.size() > 0) {
                            cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(0);
                            priorDocument1 = jsonObject.getStr("code");
                        }
                    }
                    //YXHY03-会议总结  ZRHY03-会议总结-准入
                    if (documentCode.equals("YXHY03") || documentCode.equals("ZRHY03")) {
                        List<cn.hutool.json.JSONObject> objectList = getRelatedDocumentInfo4(oddNumbers);
                        //根据申请单查询出的关联单据条数信息
                        cn.hutool.json.JSONArray jsonArray = objectList.get(0).getJSONArray("header_links");
                        if (jsonArray != null && jsonArray.size() > 0) {
                            cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(0);
                            priorDocument2 = jsonObject.getStr("code");
                        }
                    }
                }
            }
            if (!priorDocument1.equals(priorDocument2)) {
                result.setValue(1);
                result.setResult(1);
                return result;
            }
        }

        return result;
    }

    @Override
    public ValResultCommonVo supplierVerification(ExpClaimHeaderCommonVo vo) {
        ValResultCommonVo<Integer> result = new ValResultCommonVo<>();
        Map<Integer, String> message = new HashMap<>();
        result.setValue(0);
        result.setResult(0);
        result.setMessage(message);

        List<String> codeList = new ArrayList<>();

        ExpHeaderTypeVo typeVo = vo.getHeaderTypeVo();
        String code = typeVo.getCode();
        List<ExpClaimHeaderCommonVo> headerLinks = vo.getHeaderLinks();
        for (int i = 0; i < headerLinks.size(); i++) {
            //关联申请单据单号
            String oddNumbers = headerLinks.get(i).getCode();
            ExpHeaderTypeVo headerTypeVo = headerLinks.get(i).getHeaderTypeVo();
            //单据代码
            String documentCode = headerTypeVo.getCode();
            codeList.add(documentCode);
        }
        if (codeList.contains("YXHT")) {
            //YXDG01-对公预付，YXDG02-对公应付
            if (code.equals("YXDG01") || code.equals("YXDG02")) {
                String columnJson = vo.getColumnJson();
                cn.hutool.json.JSONObject jsonObjectJson = new cn.hutool.json.JSONObject(columnJson);
                String isContract = jsonObjectJson.getStr("column106");
                if (isContract.equals("Y")) {
                    //供应商ID
                    String supplier_id = vo.getSupplierId().toString();
                    for (int i = 0; i < headerLinks.size(); i++) {
                        //关联申请单据单号
                        String oddNumbers = headerLinks.get(i).getCode();
                        ExpHeaderTypeVo headerTypeVo = headerLinks.get(i).getHeaderTypeVo();
                        //单据代码
                        String documentCode = headerTypeVo.getCode();
                        if (documentCode.equals("YXHT")) {
                            List<cn.hutool.json.JSONObject> objectList = getRelatedDocumentInfo5(oddNumbers);
                            String supplierId = objectList.get(0).getStr("supplier_id").toString();
                            if (!supplier_id.equals(supplierId)) {
                                result.setValue(1);
                                result.setResult(1);
                                return result;
                            }
                        }
                    }

                }

            }
            //YXHY02-会议核报，ZRHY02-会议核报-准入
            if (code.equals("YXHY02") || code.equals("ZRHY02")) {
                List<String> supplierIdList = new ArrayList<>();
                //关联申请单据费用行
                List<ExpClaimLineCommonVo> lineVoList = vo.getExpClaimLineVoList();
                for (int i = 0; i < lineVoList.size(); i++) {
                    if (lineVoList.get(i).getSupplierId() != null) {
                        //供应商ID
                        String supplier_id = lineVoList.get(i).getSupplierId().toString();
                        supplierIdList.add(supplier_id);
                    }
                }
                for (int i = 0; i < headerLinks.size(); i++) {
                    //关联申请单据单号
                    String oddNumbers = headerLinks.get(i).getCode();
                    ExpHeaderTypeVo headerTypeVo = headerLinks.get(i).getHeaderTypeVo();
                    //单据代码
                    String documentCode = headerTypeVo.getCode();
                    if (documentCode.equals("YXHT")) {
                        List<cn.hutool.json.JSONObject> objectList = getRelatedDocumentInfo5(oddNumbers);
                        String supplierId = objectList.get(0).getStr("supplier_id").toString();
                        if (!supplierIdList.contains(supplierId)) {
                            result.setValue(1);
                            result.setResult(1);
                            return result;
                        }
                    }
                }
            }
        }
        return result;
    }

    @Override
    public ValResultCommonVo checkPreDocumentRelations(ExpClaimHeaderCommonVo vo) {
        return null;
    }

    public ServiceRes updateWorkFlowStatus(WorkFlowStatusDto statusDto) {
        ServiceRes success = ServiceRes.success(null);
        try {
            BaseOapiDto baseOapiDto = new BaseOapiDto();
            baseOapiDto.setData(statusDto);
            log.info("updateWorkFlowStatus,入参: {}", JSONObject.toJSONString(baseOapiDto));
            ServiceRes api = yjApiService.api(updateWorkFlowUrl, HttpMethod.PUT, JSONObject.toJSONString(baseOapiDto), BaseOapiDto.class);
            String code = api.getCode();
            if ("500000".equals(code) || "000000".equals(code)) {
                throw new RuntimeException(api.getMsg());
            }
            log.info("updateWorkFlowStatus,返参: {}", JSONObject.toJSONString(api));
        } catch (Exception e) {
            String message = e.getMessage();
            log.error("updateWorkFlowStatus,异常:{}", message, e);
            success = ServiceRes.failure(message);
        }

        return success;
    }

    /**
     * 查询单据信息 通用
     *
     * @param oddNumbers 单据类型ID
     * @return List<JSONObject>
     */
    public List<cn.hutool.json.JSONObject> getRelatedDocumentInfo(String oddNumbers) {
        List<cn.hutool.json.JSONObject> claimRes = Lists.newArrayList();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ExpClaimQueryDTO claimQueryDTO = new ExpClaimQueryDTO();
        //返参当前单据信息
        claimQueryDTO.setHeaderColumns(Arrays.asList("document_num", "approved_date", "column17", "status", "submit_user", "charge_user", "branch_id",
                "description", "column2", "column22", "column25", "column44", "column65", "charge_department", "currency_code",
                "submit_department", "total_amount", "submit_date", "invoice_type", "start_datetime", "end_datetime", "gl_period", "project_name"));
        //返回back后序单据参数
        //返回head_link前序单据
        claimQueryDTO.setHeaderBackLinkColumns(Arrays.asList("document_num", "status", "header_type_id", "gl_period", "total_amount", "total_pay_amount", "advance_amount", "column21", "column101", "column_json"));
        //费用行
        //      claimQueryDTO.setLineColumns(Arrays.asList("destination_city_to"));
        //     claimQueryDTO.setCompanyId(1050);
//        claimQueryDTO.setPageNum(pageNum);
        Map<String, List<String>> paramsMap = Maps.newHashMap();
        paramsMap.put("status", Arrays.asList("approved"));
//        paramsMap.put("header_type_id", Arrays.asList(typeId));
        paramsMap.put("document_num", Arrays.asList(oddNumbers));
        claimQueryDTO.setParamsMap(paramsMap);
        String jsonString = JSONUtil.toJsonStr(claimQueryDTO);
        ServiceRes serviceRes = yjApiService.api1(findListByConditionUrl, HttpMethod.POST, jsonString, ExpClaimHeaderCommonVo.class);
        String resCode = serviceRes.getCode();
        if (!"200000".equals(resCode) || Objects.isNull(serviceRes.getData())) {
            log.info("自动核销单，未查询到单据");
            return claimRes;
        }
        claimRes = (List<cn.hutool.json.JSONObject>) serviceRes.getData();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(claimRes)) {
            return claimRes;
        }
        return claimRes;
    }

    /**
     * 查询项目号单据信息 通用
     *
     * @return List<JSONObject>
     */
    public List<cn.hutool.json.JSONObject> getProjectDocumentInfo(String project, String iitProject, List<String> listTypeID, List<String> listStatus) {
        List<cn.hutool.json.JSONObject> claimRes = Lists.newArrayList();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ExpClaimQueryDTO claimQueryDTO = new ExpClaimQueryDTO();
        //返参当前单据信息
        claimQueryDTO.setHeaderColumns(Arrays.asList("document_num", "approved_date", "column17", "status", "submit_user", "charge_user", "branch_id",
                "description", "column2", "column22", "column25", "column44", "column65", "charge_department", "currency_code",
                "submit_department", "total_amount", "submit_date", "invoice_type", "start_datetime", "end_datetime", "gl_period", "project_name"));
        //返回back后序单据参数
        //返回当前单据
        claimQueryDTO.setHeaderColumns(Arrays.asList("document_num", "status", "header_type_id", "gl_period", "total_amount", "total_pay_amount", "advance_amount"));
        //返回head_link前序单据
        claimQueryDTO.setLineBackLinkColumns(Arrays.asList("document_num", "status", "header_type_id", "gl_period", "total_amount", "total_pay_amount", "advance_amount"));
        //费用行
        //      claimQueryDTO.setLineColumns(Arrays.asList("destination_city_to"));
        //     claimQueryDTO.setCompanyId(1050);
//        claimQueryDTO.setPageNum(pageNum);
        Map<String, List<String>> paramsMap = Maps.newHashMap();
        paramsMap.put("status", listStatus);
        paramsMap.put("header_type_id", listTypeID);
        //paramsMap.put("column45", Arrays.asList(project));
        // paramsMap.put("column46", Arrays.asList(iitProject));
        Map<String, String> paramMap = new HashMap<>();
        if (project != null && project != "") {
            paramMap.put("column45", project);
        }
        if (iitProject != null && iitProject != "") {
            paramMap.put("column46", iitProject);
        }
        claimQueryDTO.setParamsMap(paramsMap);
        claimQueryDTO.setParamMap(paramMap);
        String jsonString = JSONUtil.toJsonStr(claimQueryDTO);
        ServiceRes serviceRes = yjApiService.api1(findListByConditionUrl, HttpMethod.POST, jsonString, ExpClaimHeaderCommonVo.class);
        String resCode = serviceRes.getCode();
        if (!"200000".equals(resCode) || Objects.isNull(serviceRes.getData())) {
            log.info("自动核销单，未查询到单据");
            return claimRes;
        }
        claimRes = (List<cn.hutool.json.JSONObject>) serviceRes.getData();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(claimRes)) {
            return claimRes;
        }
        return claimRes;
    }

    /**
     * 查询单据信息
     *
     * @return List<JSONObject>
     */
    public List<cn.hutool.json.JSONObject> getRelatedDocumentInfo1(String oddNumbers, List<String> listStatus) {
        List<cn.hutool.json.JSONObject> claimRes = Lists.newArrayList();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ExpClaimQueryDTO claimQueryDTO = new ExpClaimQueryDTO();
        //返参当前单据信息
        claimQueryDTO.setHeaderColumns(Arrays.asList("document_num", "approved_date", "column17", "status", "submit_user", "charge_user", "branch_id",
                "description", "column2", "column22", "column25", "column44", "column65", "charge_department", "currency_code",
                "submit_department", "total_amount", "submit_date", "invoice_type", "start_datetime", "end_datetime", "gl_period", "project_name"));
        //返回back后序单据参数
        //返回head_link前序单据
        claimQueryDTO.setHeaderBackLinkColumns(Arrays.asList("document_num", "status", "header_type_id", "gl_period", "total_amount", "total_pay_amount", "advance_amount", "column21", "column101"));
        //费用行
        //      claimQueryDTO.setLineColumns(Arrays.asList("destination_city_to"));
        //     claimQueryDTO.setCompanyId(1050);
//        claimQueryDTO.setPageNum(pageNum);
        Map<String, List<String>> paramsMap = Maps.newHashMap();
        paramsMap.put("status", listStatus);
//        paramsMap.put("header_type_id", Arrays.asList(typeId));
        paramsMap.put("document_num", Arrays.asList(oddNumbers));
        claimQueryDTO.setParamsMap(paramsMap);
        String jsonString = JSONUtil.toJsonStr(claimQueryDTO);
        ServiceRes serviceRes = yjApiService.api1(findListByConditionUrl, HttpMethod.POST, jsonString, ExpClaimHeaderCommonVo.class);
        String resCode = serviceRes.getCode();
        if (!"200000".equals(resCode) || Objects.isNull(serviceRes.getData())) {
            log.info("自动核销单，未查询到单据");
            return claimRes;
        }
        claimRes = (List<cn.hutool.json.JSONObject>) serviceRes.getData();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(claimRes)) {
            return claimRes;
        }
        return claimRes;
    }

    /**
     * 查询单据信息
     *
     * @return List<JSONObject>
     */
    public List<cn.hutool.json.JSONObject> getRelatedDocumentInfo2(String oddNumbers, List<String> listStatus) {
        List<cn.hutool.json.JSONObject> claimRes = Lists.newArrayList();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ExpClaimQueryDTO claimQueryDTO = new ExpClaimQueryDTO();
        //返参当前单据信息
        claimQueryDTO.setHeaderColumns(Arrays.asList("document_num", "approved_date", "column17", "status", "submit_user", "charge_user", "branch_id",
                "description", "column2", "column22", "column25", "column44", "column65", "charge_department", "currency_code",
                "submit_department", "total_amount", "submit_date", "invoice_type", "start_datetime", "end_datetime", "gl_period", "project_name"));
        //返回back后序单据参数
        //返回head_link前序单据
        claimQueryDTO.setHeaderBackLinkColumns(Arrays.asList("document_num", "status", "header_type_id", "gl_period", "total_amount", "total_pay_amount", "advance_amount", "column21", "column101", "column_json"));
        //费用行
        claimQueryDTO.setLineBackLinkColumns(Arrays.asList("quantity", "receipt_amount", "column_json"));
        //      claimQueryDTO.setLineColumns(Arrays.asList("destination_city_to"));
        //     claimQueryDTO.setCompanyId(1050);
//        claimQueryDTO.setPageNum(pageNum);
        Map<String, List<String>> paramsMap = Maps.newHashMap();
        paramsMap.put("status", listStatus);
//        paramsMap.put("header_type_id", Arrays.asList(typeId));
        paramsMap.put("document_num", Arrays.asList(oddNumbers));
        claimQueryDTO.setParamsMap(paramsMap);
        String jsonString = JSONUtil.toJsonStr(claimQueryDTO);
        ServiceRes serviceRes = yjApiService.api1(findListByConditionUrl, HttpMethod.POST, jsonString, ExpClaimHeaderCommonVo.class);
        String resCode = serviceRes.getCode();
        if (!"200000".equals(resCode) || Objects.isNull(serviceRes.getData())) {
            log.info("自动核销单，未查询到单据");
            return claimRes;
        }
        claimRes = (List<cn.hutool.json.JSONObject>) serviceRes.getData();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(claimRes)) {
            return claimRes;
        }
        return claimRes;
    }

    /**
     * 查询单据信息
     * 个人报销
     *
     * @return List<JSONObject>
     */
    public List<cn.hutool.json.JSONObject> getRelatedDocumentInfo3(String oddNumbers, List<String> listStatus) {
        List<cn.hutool.json.JSONObject> claimRes = Lists.newArrayList();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ExpClaimQueryDTO claimQueryDTO = new ExpClaimQueryDTO();
        //返参当前单据信息
        claimQueryDTO.setHeaderColumns(Arrays.asList("document_num", "approved_date", "column17", "status", "submit_user", "charge_user", "branch_id",
                "description", "column2", "column22", "column25", "column44", "column65", "charge_department", "currency_code",
                "submit_department", "total_amount", "submit_date", "invoice_type", "start_datetime", "end_datetime", "gl_period", "project_name"));
        //返回back后序单据参数
        //返回head_link前序单据
        claimQueryDTO.setHeaderBackLinkColumns(Arrays.asList("document_num", "status", "header_type_id", "gl_period", "total_amount", "total_pay_amount", "advance_amount", "column21", "column101", "column_json"));
        //费用行
        //      claimQueryDTO.setLineColumns(Arrays.asList("destination_city_to"));
        //     claimQueryDTO.setCompanyId(1050);
//        claimQueryDTO.setPageNum(pageNum);
        Map<String, List<String>> paramsMap = Maps.newHashMap();
        paramsMap.put("status", listStatus);
//        paramsMap.put("header_type_id", Arrays.asList(typeId));
        paramsMap.put("document_num", Arrays.asList(oddNumbers));
        claimQueryDTO.setParamsMap(paramsMap);
        String jsonString = JSONUtil.toJsonStr(claimQueryDTO);
        ServiceRes serviceRes = yjApiService.api1(findListByConditionUrl, HttpMethod.POST, jsonString, ExpClaimHeaderCommonVo.class);
        String resCode = serviceRes.getCode();
        if (!"200000".equals(resCode) || Objects.isNull(serviceRes.getData())) {
            log.info("自动核销单，未查询到单据");
            return claimRes;
        }
        claimRes = (List<cn.hutool.json.JSONObject>) serviceRes.getData();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(claimRes)) {
            return claimRes;
        }
        return claimRes;
    }

    /**
     * 查询单据信息
     * 借款
     *
     * @return List<JSONObject>
     */
    public List<cn.hutool.json.JSONObject> getRelatedDocumentInfo4(String oddNumbers) {
        List<cn.hutool.json.JSONObject> claimRes = Lists.newArrayList();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ExpClaimQueryDTO claimQueryDTO = new ExpClaimQueryDTO();
        //返参当前单据信息
        claimQueryDTO.setHeaderColumns(Arrays.asList("document_num", "approved_date", "column17", "status", "submit_user", "charge_user", "branch_id",
                "description", "column2", "column22", "column25", "column44", "column65", "charge_department", "currency_code",
                "submit_department", "total_amount", "submit_date", "invoice_type", "start_datetime", "end_datetime", "gl_period", "project_name"));
        //返回back后序单据参数
        claimQueryDTO.setHeaderLinkColumns(Arrays.asList("document_num", "status", "header_type_id", "gl_period", "total_amount", "total_pay_amount", "advance_amount", "column21", "column101", "column_json"));
        //claimQueryDTO.setHeaderLinkColumns(Arrays.asList("document_num", "status", "header_type_id", "gl_period", "total_amount", "total_pay_amount", "advance_amount", "column21", "column101", "column_json"));

        //费用行
        //      claimQueryDTO.setLineColumns(Arrays.asList("destination_city_to"));
        //     claimQueryDTO.setCompanyId(1050);
//        claimQueryDTO.setPageNum(pageNum);
        Map<String, List<String>> paramsMap = Maps.newHashMap();
        paramsMap.put("status", Arrays.asList("approved"));
//        paramsMap.put("header_type_id", Arrays.asList(typeId));
        paramsMap.put("document_num", Arrays.asList(oddNumbers));
        claimQueryDTO.setParamsMap(paramsMap);
        String jsonString = JSONUtil.toJsonStr(claimQueryDTO);
        ServiceRes serviceRes = yjApiService.api1(findListByConditionUrl, HttpMethod.POST, jsonString, ExpClaimHeaderCommonVo.class);
        String resCode = serviceRes.getCode();
        if (!"200000".equals(resCode) || Objects.isNull(serviceRes.getData())) {
            log.info("自动核销单，未查询到单据");
            return claimRes;
        }
        claimRes = (List<cn.hutool.json.JSONObject>) serviceRes.getData();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(claimRes)) {
            return claimRes;
        }
        return claimRes;
    }

    /**
     * 查询单据信息 通用
     * 供应商
     *
     * @return List<JSONObject>
     */
    public List<cn.hutool.json.JSONObject> getRelatedDocumentInfo5(String oddNumbers) {
        List<cn.hutool.json.JSONObject> claimRes = Lists.newArrayList();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ExpClaimQueryDTO claimQueryDTO = new ExpClaimQueryDTO();
        //返参当前单据信息
        claimQueryDTO.setHeaderColumns(Arrays.asList("document_num", "approved_date", "column17", "status", "submit_user", "charge_user", "branch_id",
                "description", "column2", "column22", "column25", "column44", "column65", "charge_department", "currency_code",
                "submit_department", "total_amount", "submit_date", "invoice_type", "start_datetime", "end_datetime", "gl_period", "project_name", "supplier_id"));
        //返回back后序单据参数
        //返回head_link前序单据
        claimQueryDTO.setHeaderBackLinkColumns(Arrays.asList("document_num", "status", "header_type_id", "gl_period", "total_amount", "total_pay_amount", "advance_amount", "column21", "column101", "column_json"));
        //费用行
        //      claimQueryDTO.setLineColumns(Arrays.asList("destination_city_to"));
        //     claimQueryDTO.setCompanyId(1050);
//        claimQueryDTO.setPageNum(pageNum);
        Map<String, List<String>> paramsMap = Maps.newHashMap();
        paramsMap.put("status", Arrays.asList("approved"));
//        paramsMap.put("header_type_id", Arrays.asList(typeId));
        paramsMap.put("document_num", Arrays.asList(oddNumbers));
        claimQueryDTO.setParamsMap(paramsMap);
        String jsonString = JSONUtil.toJsonStr(claimQueryDTO);
        ServiceRes serviceRes = yjApiService.api1(findListByConditionUrl, HttpMethod.POST, jsonString, ExpClaimHeaderCommonVo.class);
        String resCode = serviceRes.getCode();
        if (!"200000".equals(resCode) || Objects.isNull(serviceRes.getData())) {
            log.info("自动核销单，未查询到单据");
            return claimRes;
        }
        claimRes = (List<cn.hutool.json.JSONObject>) serviceRes.getData();
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(claimRes)) {
            return claimRes;
        }
        return claimRes;
    }
}
