package com.cloudpense.akesobio.service;

import com.cloudpense.akesobio.dto.WorkFlowStatusDto;
import com.cloudpense.akesobio.entity.DeliverieMqueue;
import com.cloudpense.akesobio.vo.ValResultCommonVo;
import com.cloudpense.common.bean.MqQueue;
import com.cloudpense.common.bean.Response;
import com.cloudpense.common.vo.ServiceRes;
import com.cloudpense.standard.vo.claim.ExpClaimHeaderCommonVo;
import com.fasterxml.jackson.core.JsonProcessingException;

/**
 * 报销校验
 */
public interface ReimbursementService {


    public ServiceRes updateWorkFlowStatus(WorkFlowStatusDto statusDto);

    /**
     * 个人报销-报销总金额校验
     */
    ValResultCommonVo getPersonalReimbursement(ExpClaimHeaderCommonVo vo);

    /**
     * 校验采购合同关联申请单总金额
     */
    ValResultCommonVo getContractApplication(ExpClaimHeaderCommonVo vo);

    /**
     * 校验对公支付关联合同审批总金额
     */
    ValResultCommonVo getCorporatePayment(ExpClaimHeaderCommonVo vo);

    /**
     * 虚拟岗：先触发虚拟岗后收单
     * 收单和不收单 都会触发
     */
    ServiceRes receivedInterface(ExpClaimHeaderCommonVo claim, MqQueue mqQueue) throws Exception;

    /**
     * 收单触发消息
     */
    ServiceRes deliveryReceived(ExpClaimHeaderCommonVo claim, DeliverieMqueue mqQueue) throws Exception;

    /**
     * 费用报销总金额统计
     */
    Response personalTotalAmount(String param) throws JsonProcessingException;

    /**
     * 共享费控单据给协办人
     */
    Response sharedDocument(String param) throws JsonProcessingException;

    /**
     * 校验IIT和平台项目总金额
     */
    ValResultCommonVo projectTotalAmount(ExpClaimHeaderCommonVo vo);

    /**
     * 校验京东采购总金额不超前序申请费用行总额汇总金额（K003）
     */
    ValResultCommonVo jdPurchaseRequisition(ExpClaimHeaderCommonVo vo);

    /**
     * 校验实际领药数量
     */
    ValResultCommonVo medicationQuantity(ExpClaimHeaderCommonVo vo);

    /**
     * 校验平台执行计划总金额
     */
    ValResultCommonVo platformExecutionPlanTotalAmount(ExpClaimHeaderCommonVo vo);

    /**
     * 共享平台执行计划单据给立项申请人
     */
    Response sharedDocumentToProjectApplicant(String param) throws JsonProcessingException;

    /**
     * 汇总个人月度交际费总金额
     */
    Response communicationFeeTotalAmount(String param) throws JsonProcessingException;

    /**
     * 校验公司名称
     */
    ValResultCommonVo verifyCompanyName(ExpClaimHeaderCommonVo vo);

    /**
     * 借款校验
     */
    ValResultCommonVo loanVerification(ExpClaimHeaderCommonVo vo);

    /**
     * 校验供应商
     */
    ValResultCommonVo supplierVerification(ExpClaimHeaderCommonVo vo);


    /**
     * 校验前序申请单关联次数
     * 如果一笔申请被关联过两次以上（含2次）提示：该申请已被关联过多次，请检查无误后提交
     */
    ValResultCommonVo checkPreDocumentRelations(ExpClaimHeaderCommonVo vo);


}
