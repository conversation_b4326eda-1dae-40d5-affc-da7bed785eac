package com.cloudpense.akesobio.entity;

import com.cloudpense.akesobio.dto.PageInfoOapiDto;
import lombok.Data;

import java.util.List;

@Data
public class BaseEntityPage {

      private  Integer	 pageSize	  ;   // 分页条数
      private  Integer	 pageNum	  ;       // 分页页数
      private  Long	     totalCount	  ;   // 订单总数
      private  Integer	 totalPage	  ;   // 总页数
      private  Boolean	 isFirstPage;	     // 是否是是首页
      private  Boolean	 isLastPage	  ;   // 是否是最后一页
      private  List<PageInfoOapiDto> pageInfo ;   // 订单数据集合
}
