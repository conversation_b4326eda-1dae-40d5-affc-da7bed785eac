INSERT INTO mq_config (
     company_id, exchange, rooting_key, platform, 
    interface_name, description, event_code, header_type_code, 
    position_code, workflow_type, created_by, creation_date, 
    last_updated_by, last_update_date 
) VALUES (
    23049, 'wf_claim', 'mq-aliprod-customize-claim-push', 'cloudpense',
    'addBusinessTrip', NULL, 'wfp', 'YXCL01',
    'KF09', NULL, NULL, NOW(),
    NULL, NOW()
);


INSERT INTO `cpkfsw`.`fnd_lov_value_tl` (`value_id`,`value_meaning`, `description`, `language`, `last_update_date`) VALUES ((SELECT value_id from `cpkfsw`.`fnd_lov_value` where `value_code` = 'addBusinessTrip' and `company_id` = 23049), '云简业财系统的差旅申请单需对接考勤系统', NULL, 'en_US', now());

INSERT INTO `cpkfsw`.`fnd_lov_value_tl` (`value_id`, `value_meaning`, `description`, `language`, `last_update_date`) VALUES ((SELECT value_id from `cpkfsw`.`fnd_lov_value` where `value_code` = 'addBusinessTrip' and `company_id` = 23049), '云简业财系统的差旅申请单需对接考勤系统', NULL, 'ja_JP', now());

INSERT INTO `cpkfsw`.`fnd_lov_value_tl` (`value_id`,`value_meaning`, `description`, `language`, `last_update_date`) VALUES ((SELECT value_id from `cpkfsw`.`fnd_lov_value` where `value_code` = 'addBusinessTrip' and `company_id` = 23049),'云简业财系统的差旅申请单需对接考勤系统', NULL, 'zh_CN', now());

INSERT INTO `cpkfsw`.`fnd_lov_value_tl` (`value_id`, `value_meaning`, `description`, `language`, `last_update_date`) VALUES ((SELECT value_id from `cpkfsw`.`fnd_lov_value` where `value_code` = 'addBusinessTrip' and `company_id` = 23049),'云简业财系统的差旅申请单需对接考勤系统', NULL, 'zh_TW', now());


INSERT INTO `cpkfsw`.`fnd_lov_value` (`lov_id`, `company_id`, `value_code`, `enabled_flag`, `column10`, `created_by`, `creation_date`, `last_updated_by`, `last_update_date`) VALUES ((select `lov_id` from `cpkfsw`.`fnd_lov` where `lov_name` = 'HTTP_CONFIG_PARAM' and `company_id` = 23049), 23049, 'addBusinessTrip', 'Y', '{\"url\": \"http://*********/*********/HrController/addBusinessTrip\",\r\n	\"jsonText\": \"{\\\"headerColumns\\\":[\\\"header_id\\\",\\\"workflow_paths\\\",\\\"document_num\\\",\\\"column_json\\\",\\\"column10\\\",\\\"column17\\\",\\\"column45\\\",\\\"column46\\\",\\\"status\\\",\\\"header_type_id\\\",\\\"submit_user\\\",\\\"charge_user\\\",\\\"submit_date\\\"],\\\"headerLinkColumns\\\":[\\\"header_id\\\",\\\"document_num\\\",\\\"column22\\\",\\\"header_type_id\\\"],\\\"lineColumns\\\":[\\\"line_id\\\",\\\"type_id\\\",\\\"column_json\\\"]}\"}',  NULL, NOW(), NULL, NOW());
