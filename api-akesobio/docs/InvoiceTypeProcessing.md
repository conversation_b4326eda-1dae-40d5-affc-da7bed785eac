# 发票类型判断和COLUMN175字段自动填充接口

## 接口概述

本接口实现了基于费用明细行中发票类型的自动判断逻辑，并根据判断结果自动填充单据头的COLUMN175字段（是否投递纸单）。

## 接口信息

- **接口路径**: `/IITController/KF-SAP-01/InvoiceTypeProcessing`
- **请求方法**: POST
- **接口描述**: 发票类型判断和COLUMN175字段自动填充

## 业务逻辑

### 1. 发票类型判断逻辑

系统会检查费用明细行（claim_lines）中的invoice_type字段，根据以下白名单进行判断：

**发票类型白名单**：
```
01, 02, 03, 04, 11, 15, 18, 19, 24, 25, 
50, 51, 60, 59, 61, 62, 63, 64, 65, 66, 
70, 72, 74, 99
```

- 如果invoice_type**不在**白名单中，则判定该发票为**纯电子发票**
- 如果invoice_type在白名单中，则需要进一步检查票据类型含义

### 2. COLUMN175字段自动填充逻辑

系统会根据费用明细行的票据类型自动判定COLUMN175字段的值：

| 条件 | COLUMN175值 | 说明 |
|------|-------------|------|
| 票据类型含义包含"电子"关键字 | "否" | 电子发票无需投递纸单 |
| 票据类型含义不包含"电子"关键字 | "是" | 纸质发票需要投递纸单 |
| 发票类型不在白名单中 | "否" | 纯电子发票无需投递纸单 |
| 没有发票信息 | "是" | 默认需要投递纸单 |

### 3. 混合发票处理逻辑

当单据中同时存在电子发票和纸质发票时：
- 如果存在任何纸质发票，COLUMN175设置为"是"
- 只有全部为电子发票时，COLUMN175才设置为"否"

## 请求参数

```json
{
  "mqQueue": {
    "pathId": "工作流路径ID"
  },
  "claimVo": "单据信息的JSON字符串"
}
```

### 参数说明

- `mqQueue.pathId`: 工作流路径ID，用于状态回调
- `claimVo`: 包含完整单据信息的JSON字符串，需要包含费用明细行和发票信息

## 响应结果

```json
{
  "code": "200000",
  "message": "处理成功",
  "data": {
    "status": "APPROVED",
    "pathId": "工作流路径ID"
  }
}
```

### 错误响应

```json
{
  "code": "500000", 
  "message": "发票类型处理失败: 具体错误信息",
  "data": {
    "status": "APPROVING",
    "pathId": "工作流路径ID",
    "note": "错误详情"
  }
}
```

## 使用示例

### 示例1：纯电子发票

**请求数据**：
```json
{
  "mqQueue": {"pathId": "path-123"},
  "claimVo": "{\"id\":12345,\"header_id\":12345,\"document_num\":\"TEST001\",\"expClaimLineVoList\":[{\"receipts\":[{\"invoice_type\":\"05\",\"invoice_type_meaning\":\"电子普通发票\"}]}]}"
}
```

**处理结果**：COLUMN175 = "否"

### 示例2：纸质发票

**请求数据**：
```json
{
  "mqQueue": {"pathId": "path-456"},
  "claimVo": "{\"id\":12346,\"header_id\":12346,\"document_num\":\"TEST002\",\"expClaimLineVoList\":[{\"receipts\":[{\"invoice_type\":\"01\",\"invoice_type_meaning\":\"增值税普通发票\"}]}]}"
}
```

**处理结果**：COLUMN175 = "是"

### 示例3：混合发票

**请求数据**：
```json
{
  "mqQueue": {"pathId": "path-789"},
  "claimVo": "{\"id\":12347,\"header_id\":12347,\"document_num\":\"TEST003\",\"expClaimLineVoList\":[{\"receipts\":[{\"invoice_type\":\"01\",\"invoice_type_meaning\":\"增值税普通发票\"},{\"invoice_type\":\"05\",\"invoice_type_meaning\":\"电子普通发票\"}]}]}"
}
```

**处理结果**：COLUMN175 = "是"（因为存在纸质发票）

## 注意事项

1. **数据完整性**：确保传入的claimVo包含完整的费用明细行和发票信息
2. **字段更新**：COLUMN175字段通过columnJson进行更新，会保留原有的其他自定义字段
3. **异常处理**：如果处理过程中出现异常，会返回详细的错误信息
4. **日志记录**：所有处理过程都会记录详细的日志，便于问题排查

## 相关代码文件

- 控制器：`IITController.java`
- 服务接口：`IitProjectService.java`
- 服务实现：`IitProjectServiceImpl.java`
- 测试类：`InvoiceTypeProcessingTest.java`
